# Google Cloud Platform Configuration
[GCP]
[GCP.STORAGE]
BUCKET_NAME = "your_bucket_name_here"           # Google Cloud Storage bucket name

[GCP.GSHEETS]
SPREADSHEET_ID = "your_spreadsheet_id_here"     # Google Sheets spreadsheet ID

# System Configuration
[SYSTEM]
NODES = ["NODE1", "NODE2", "NODE3"]                                                                 # List of nodes to process
DISTANCE_METHOD = "google_maps"                                                                     # Method used for distance calculation
MAXIMUM_DISTANCE_THRES_KM = 643.74                                                                  # Maximum distance threshold in kilometers (400 miles)
MAX_IMPORT_MATCHES = 1                                                                              # Maximum number of import matches
MAX_EXPORT_MATCHES = 1                                                                              # Maximum number of export matches
CRITERIA = "distance"                                                                               # Matching criteria
TIME_TOLERANCE_THRES = 0                                                                            # Time tolerance threshold
STREET_TURN_COST_OPTIONS = ["FORMULA_2"]                                                            # Street turn cost calculation options
RANKING_PRIORITY_OPTIONS = ["street_turn_total_cost", "cost_save", "dist_sav_km", "time_gap"]       # Ranking priority options
PRIMARY_RANKING_OPTIONS = "street_turn_total_cost"  # Primary ranking option

DELIVERY_TYPE = "Door"                  # Type of delivery
HAZMAT = "N"                            # Hazardous materials flag (Y/N)
CUSTOMER_NOMINATED_TRUCKER = false      # Whether customer nominates trucker
DROP_AND_PICK = "N"                     # Drop and pick service flag (Y/N)

# Database Configuration
[DB]
# Dataset IDs
SRC_DATASET_ID = "your_dataset_name_here"                       # Source data dataset (location and yard load)
DE_OUT_DATASET_ID = "your_dataset_name_here"                    # Data engineering output dataset
DE_INTER_DATASET_ID = "your_dataset_name_here"                  # Data engineering intermediate dataset

# Table IDs
REPORT_TABLE_ID = "your_table_name_here"                        # Processed report table
RATELANE_TABLE_ID = "your_table_name_here"                      # Rate lane table
DIST_TABLE_ID = "your_table_name_here"                          # Street turn metadata table
LOCATION_TABLE_ID = "your_table_name_here"                      # Location information table
YARD_TABLE_ID = "your_table_name_here"                          # Yard information table
RATELANE_TABLE_MAPPING_ID = "your_table_name_here"              # Rate lane mapping table
MATCHED_CANDIDATE_PAIRS_TABLE_ID = "your_table_name_here"       # Matched candidate pairs table
POSSIBLE_CANDIDATE_PAIRS_TABLE_ID = "your_table_name_here"      # Possible candidate pairs table
DISTANCE_TABLE_ID = "your_table_name_here"                      # Distance calculation table
