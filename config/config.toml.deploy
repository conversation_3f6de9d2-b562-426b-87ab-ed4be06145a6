[GCP]
[GCP.STORAGE]
BUCKET_NAME = "iro_system_runs_dev"

[GCP.GSHEETS]
SPREADSHEET_ID="1lK6rYyizW4zH04VZQbMobOJMCX6OrXM3_jXF5BwR7Y4"

[SYSTEM]
NODES = ["USATL63", "USORF01", "USORF03", "USORF61", "USJAX01", "USJAX03", "USJAX65", "USLAX01", "USLAX03", "USCHI79"]
DISTANCE_METHOD = "google_maps"
MAXIMUM_DISTANCE_THRES_KM = 643.74      # 400 miles
MAX_IMPORT_MATCHES = 1
MAX_EXPORT_MATCHES = 1
CRITERIA = "distance"
TIME_TOLERANCE_THRES  = 0
STREET_TURN_COST_OPTIONS = ["FORMULA_2"]
RANKING_PRIORITY_OPTIONS = ["street_turn_total_cost", "cost_save", "dist_sav_km", "time_gap"]
PRIMARY_RANKING_OPTIONS = "street_turn_total_cost"

DELIVERY_TYPE = "Door"
HAZMAT = "N"
CUSTOMER_NOMINATED_TRUCKER = false
DROP_AND_PICK = "N"

[DB]
SRC_DATASET_ID = "SOURCE_DATA" # location and yard load
DE_OUT_DATASET_ID = "OUTPUTS"
DE_INTER_DATASET_ID = "INTERMEDIATE"

REPORT_TABLE_ID = "RPT_PROC"
RATELANE_TABLE_ID = "GET_RT_LANE_BY_DT"

DIST_TABLE_ID = "STREET_TURN_METADATA"
LOCATION_TABLE_ID = "DWC_LOCATION"
YARD_TABLE_ID = "DWC_YARD"
RATELANE_TABLE_MAPPING_ID = "GET_RATE_LANE_MAP_BY_PORT_AND_DATETIME"
MATCHED_CANDIDATE_PAIRS_TABLE_ID = "ST_MATCHED_CANDIDATE_PAIRS"
POSSIBLE_CANDIDATE_PAIRS_TABLE_ID = "ST_POSSIBLE_CANDIDATE_PAIRS"
DISTANCE_TABLE_ID = "DISTANCE"
