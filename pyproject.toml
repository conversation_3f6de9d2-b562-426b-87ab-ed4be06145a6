[tool.poetry]
name = "ghq-bpit-ncop-iro"
version = "1.0.0"
description = "GHQ BPIT NCOP IRO Street Turn Matching Service - A logistics optimization system for container street turn operations"
authors = ["Development Team <<EMAIL>>"]
maintainers = ["Development Team <<EMAIL>>"]
readme = "README.md"
license = "Proprietary"
keywords = ["logistics", "optimization", "container", "street-turn", "matching", "gcp"]
packages = [{ include = "src" }]
include = ["pyproject.toml", "README.md", "config/*.sample", ".env.sample"]
exclude = ["tests/", "docs/", "cache/", "logs/", "tmp/", "runs/"]

[tool.poetry.dependencies]
python = ">=3.11,<3.13"
python-dotenv = "~1.0.1"
google-cloud-bigquery = "^3.25.0"
pandas = "^2.2.3"
pydantic = "^2.9.2"
pydantic-settings = "^2.5.2"
google-api-python-client = "^2.147.0"
db-dtypes = "^1.3.0"
matplotlib = "^3.9.2"
google-cloud-bigquery-storage = "^2.26.0"
pulp = "^3.0.2"
tqdm = "^4.66.5"
numba = "^0.60.0"
rich = "^13.9.2"
googlemaps = "^4.10.0"
tomli = "^2.1.0"
google-cloud-storage = "^3.1.0"
geopy = "^2.4.1"
pandas-gbq = "^0.27.0"
xlsxwriter = "^3.2.2"
fastapi = "^0.115.12"
uvicorn = "^0.34.0"
requests = "^2.32.3"
gspread = "^6.2.0"
google-cloud-batch = "^0.17.35"
google-cloud-pubsub = "^2.29.0"
aiofiles = "^24.1.0"
gspread-formatting = "^1.2.1"
folium = "^0.19.5"

[tool.poetry.group.dev.dependencies]
ruff = "^0.11.0"
ipykernel = "^6.29.5"
pre-commit = "^4.2.0"
types-pyyaml = "^6.0.12.20240917"
plotly = "^6.0.1"
nbformat = "^5.10.4"
mypy = "^1.13.0"
types-pytz = "^2025.1.0.20250204"
types-aiofiles = "^24.1.0.20250326"
pytest = "^8.2.0"
pytest-cov = "^6.1.1"
pytest-asyncio = "^0.23.5"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
    "docs",
]
indent-width = 4
line-length = 120
output-format = "grouped"
target-version = "py310"


[tool.ruff.lint]
external = ["V"]
select = [
    "F",     # pyflakes: https://docs.astral.sh/ruff/rules/#pyflakes-f
    "E",     # pycodestyle errors: https://docs.astral.sh/ruff/rules/#pycodestyle-e
    "W",     # pycodestyle warnings: https://docs.astral.sh/ruff/rules/#pycodestyle-w
    "I",     # isort rules: https://docs.astral.sh/ruff/rules/#isort
    #"C90",   # mccabe: https://docs.astral.sh/ruff/rules/#mccabe
    "N",     # pep8-naming: https://docs.astral.sh/ruff/rules/#pep8-naming
    "D",     # pydocstyle: https://docs.astral.sh/ruff/rules/#pydocstyle
    "UP",    # pyupgrade: https://docs.astral.sh/ruff/rules/#pyupgrade
    "B",     # flake8-bugbear: https://docs.astral.sh/ruff/rules/#flake8-bugbear
    "S",     # flake8-bandit: https://docs.astral.sh/ruff/rules/#flake8-bandit
    "C4",    # flake8-comprehensions: https://docs.astral.sh/ruff/rules/#flake8-comprehensions
    "PLE",   # pylint: https://docs.astral.sh/ruff/rules/#pylint-pl
    "ASYNC", # flake8-async: https://docs.astral.sh/ruff/rules/#flake8-async
]
ignore = [
    "E501", # line too long, handled by black
    "E402", # module level import not at top of file
    "D401", # first line should be in imperative mood
    "D104",
    "D100",
    "D101",
    "B007",
]
# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []
# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "~(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"
# Exclude a variety of commonly ignored directories.
exclude = [
    ".eggs",
    ".git",
    ".mypy_cache",
    ".ruff_cache",
    "__pypackages__",
    "_build",
    "build",
    "dist",
    "docs",
]
task-tags = ["TODO", "NOTE", "FIXME", "HACK"]


[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401", "F403", "E402"]
"**/{tests,docs,tools}/*" = ["E402", "S101"]

[tool.ruff.lint.flake8-annotations]
allow-star-arg-any = true


[tool.ruff.lint.mccabe]
max-complexity = 10


[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.format]
docstring-code-format = true
docstring-code-line-length = "dynamic"
indent-style = "space"
quote-style = "double"
line-ending = "auto"
skip-magic-trailing-comma = false

[tool.docformatter]
black = true
in-place = true
recursive = true
wrap-summaries = 100
wrap-descriptions = 100

[tool.mypy]
plugins = [ "pydantic.mypy" ]
