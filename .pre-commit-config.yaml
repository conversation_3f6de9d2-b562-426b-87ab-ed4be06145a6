ci:
  autofix_prs: true
  autoupdate_commit_msg: "[pre-commit.ci] pre-commit suggestions"
  autoupdate_schedule: monthly
  # submodules: true

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      # - id: check-added-large-files
      #   args: [--maxkb=100]
      - id: check-ast
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-json
      - id: check-toml
      - id: check-yaml
      - id: debug-statements
      # - id: detect-aws-credentials
      - id: detect-private-key
      - id: end-of-file-fixer
      - id: name-tests-test
      - id: trailing-whitespace
      - id: check-merge-conflict
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.11.0
    hooks:
      # Run the linter.
      - id: ruff
        types_or: [python, pyi, jupyter]
        args: [--fix]
      # Run the formatter.
      - id: ruff-format
        types_or: [python, pyi, jupyter]
  - repo: https://github.com/gitleaks/gitleaks
    rev: v8.24.0
    hooks:
      - id: gitleaks
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.15.0
    hooks:
      - id: mypy
        args:
          [
            --ignore-missing-imports,
            --no-implicit-optional,
            --explicit-package-bases,
          ]
        additional_dependencies:
          - types-PyYAML
          - types-pytz
          - pydantic
          - types-aiofiles
  - repo: https://github.com/kynan/nbstripout
    rev: 0.8.1
    hooks:
      - id: nbstripout
  - repo: https://github.com/python-poetry/poetry
    rev: "master"
    hooks:
      - id: poetry-export
        args: ["-f", "requirements.txt"]
        verbose: true
