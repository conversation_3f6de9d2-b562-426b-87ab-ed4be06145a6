# Version control
.git
.gitignore
.github/

# Python artifacts
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Python virtual environments
.env
.venv
env/
venv/
ENV/

# Distribution / packaging
dist/
build/
*.egg-info/

# Docker files
Dockerfile
docker-compose.yml
.dockerignore

# Local development
.idea/
.vscode/
*.swp
*.swo

# Logs and databases
*.log
*.sqlite3
logs/
*.db

# Documentation
docs/
README.md
CHANGELOG.md
LICENSE
.creds
poetry.lock
uv.lock
logs/
db/
data/
runs/
tmp/
notebooks/
