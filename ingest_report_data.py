from __future__ import annotations

import argparse
import datetime as dt
import logging
import os
import re
from pathlib import Path
from typing import Any

import pandas as pd
from google.api_core import exceptions as google_exceptions

from archive.src_v2.const.config import settings
from archive.src_v2.core.processors.file_processor import FileProcessor
from archive.src_v2.core.queries.report_queries import ReportQueries
from archive.src_v2.thirdparty.gcp import GoogleDriveManager, ReportBigQueryManager
from root import DATA_DIR, PROJECT_ID
from src.utils.file_utils import extract_datetime_from_filename

logger = logging.getLogger(__name__)


class ReportProcessor:
    def __init__(self, interval_days: int) -> None:
        """Initialize the ReportProcessor with the necessary parameters and managers.

        Args:
            interval_days (int): The number of days to consider for processing.
        """
        self.dataset_id = settings.DB.REPORT_DATASET_ID
        self.raw_table_id = settings.DB.REPORT_RAW_TABLE_ID
        self.processed_table_id = settings.DB.REPORT_TABLE_ID
        self.interval_days = interval_days
        self.drive_manager = GoogleDriveManager()
        self.db_manager = ReportBigQueryManager()
        self._validate_config()

    def _validate_config(self) -> None:
        """Validate configuration settings."""
        required_settings = [
            self.dataset_id,
            self.raw_table_id,
            self.processed_table_id,
            settings.ENV.DB_REPORT_DATA_FOLDER_ID,
        ]
        if any(not setting for setting in required_settings):
            raise ValueError("Missing required configuration settings")

    @staticmethod
    def _validate_dates(start_date: str | None, end_date: str | None) -> None:
        """Validate date parameters."""
        if start_date and not re.match(r"^\d{4}-\d{2}-\d{2}$", start_date):
            raise ValueError(f"Invalid start_date format: {start_date}")
        if end_date and not re.match(r"^\d{4}-\d{2}-\d{2}$", end_date):
            raise ValueError(f"Invalid end_date format: {end_date}")

    def get_existing_dates(self, start_date: str = "", is_backfill: bool = False) -> pd.DataFrame:
        """Retrieve existing dates from the BigQuery table within the specified interval.

        Args:
            start_date (str): The start date for the query in 'YYYY-MM-DD' format.
            is_backfill (bool): Flag to indicate if the process is a backfill.

        Returns:
            pd.DataFrame: A DataFrame containing existing dates in the table.
        """
        query = ReportQueries.get_existing_dates_query(
            PROJECT_ID,
            self.dataset_id,
            self.raw_table_id,
            start_date if not is_backfill else None,
            self.interval_days if not is_backfill else None,
        )

        try:
            existing_dates = self.db_manager.execute_query(query)
            if not existing_dates.empty:
                existing_dates["EDW_UPD_DT"] = existing_dates["EDW_UPD_DT"].dt.strftime("%Y-%m-%d %H:%M:%S")
            return existing_dates
        except google_exceptions.NotFound as e:
            logger.error(f"Table {self.dataset_id}.{self.raw_table_id} not found.\nError: {e}", exc_info=True)
            return pd.DataFrame()

    def process_files(self, exist_dt_data: pd.DataFrame) -> None:
        """Process files with retry mechanism."""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                files = self.drive_manager.list_files(settings.ENV.DB_REPORT_DATA_FOLDER_ID, page_size=6)
                for file in files:
                    self.__process_file(file, exist_dt_data)
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Failed to process files after {max_retries} attempts.\nError: {e}", exc_info=True)
                    raise
                logger.warning(f"Attempt {attempt + 1} failed, retrying...")

    def __process_file(self, file: dict[str, Any], exist_dt_data: pd.DataFrame) -> None:
        """Process a single file from Google Drive.

        Args:
            file (dict): Dictionary containing file information.
            exist_dt_data (pd.DataFrame): DataFrame containing existing dates in the table.
        """
        destination_path = self.__get_destination_path(file["name"])
        edw_upd_dt = extract_datetime_from_filename(file["name"])
        print(f"Processing file: {file['name']}")
        print(f"edw_upd_dt: {edw_upd_dt}")

        if self.__should_process_file(edw_upd_dt, exist_dt_data):
            self.__download_and_load_file(file, destination_path)
        else:
            print("Data already exists in the table.")

    def __get_destination_path(self, filename: str) -> Path:
        """Generate the destination path for a file.

        Args:
            filename (str): The name of the file.

        Returns:
            Path: The full path where the file should be saved.
        """
        destination_path = DATA_DIR / "report_raw" / filename
        destination_path.parent.mkdir(parents=True, exist_ok=True)
        return destination_path

    def __should_process_file(self, edw_upd_dt: str, exist_dt_data: pd.DataFrame) -> bool:
        """Determine if a file should be processed based on existing data.

        Args:
            edw_upd_dt (str): The date extracted from the filename.
            exist_dt_data (pd.DataFrame): DataFrame containing existing dates in the table.

        Returns:
            bool: True if the file should be processed, False otherwise.
        """
        return exist_dt_data.empty or edw_upd_dt not in exist_dt_data["EDW_UPD_DT"].values

    def __download_and_load_file(self, file: dict[str, Any], destination_path: Path) -> None:
        """Download a file from Google Drive and load it into BigQuery.

        Args:
            file (dict): Dictionary containing file information.
            destination_path (Path): The path where the file should be saved.
        """
        print("Downloading and loading file...")
        self.drive_manager.download_file(file["id"], destination_path)
        self.db_manager.load_report_raw_to_bq([destination_path], self.dataset_id, self.raw_table_id)

        edw_upd_dt_str: str = extract_datetime_from_filename(os.path.basename(destination_path))
        self.db_manager.process_report_raw(edw_upd_dt_str, self.dataset_id, self.processed_table_id, self.raw_table_id)
        print("Data has been loaded to the table.")

    def process_backfill_files(self, existing_dates: pd.DataFrame, start_date: str, end_date: str) -> None:
        """Process all files from the specified Google Drive folder.

        Args:
            existing_dates (pd.DataFrame): DataFrame containing existing dates in the table.
            start_date (str): The start date for the backfill in 'YYYY-MM-DD' format.
            end_date (str): The end date for the backfill in 'YYYY-MM-DD' format.
        """
        files = self.drive_manager.list_files(settings.ENV.DB_REPORT_DATA_FOLDER_ID, return_all=True, page_size=100)

        filtered_files = FileProcessor.filter_files_by_date_range(files, start_date, end_date)
        for file in filtered_files:
            self.__process_file(file, existing_dates)


def main():
    """Main function to run the report processing workflow."""
    processor = ReportProcessor(interval_days=7)
    current_date: str = dt.datetime.now(dt.timezone.utc).strftime("%Y-%m-%d")
    print(f"Current date: {current_date}")

    exist_dt_data = processor.get_existing_dates(current_date)
    processor.process_files(exist_dt_data)


def backfill(start_date: str, end_date: str):
    """Function to execute the backfill process."""
    processor = ReportProcessor(interval_days=7)
    print(f"Backfilling data from {start_date} to {end_date}")

    exist_dt_data = processor.get_existing_dates(is_backfill=True)
    processor.process_backfill_files(exist_dt_data, start_date, end_date)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Ingest report data script.")
    parser.add_argument(
        "--backfill",
        action="store_true",
        help="Run the backfill process instead of the main process.",
    )
    args = parser.parse_args()
    if args.backfill:
        backfill("2024-10-01", "2025-02-28")
    else:
        main()
