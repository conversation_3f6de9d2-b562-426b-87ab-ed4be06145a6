import google.cloud.bigquery as bigquery

from root import PROJECT_ID
from src.data_model.schemas import REPOR<PERSON> as REPORT_SCHEMA
from src.data_model.schemas import REPORT_RAW as REPORT_RAW_SCHEMA

client = bigquery.Client(project=PROJECT_ID)

dataset_id = "REPORT"

table_id = "REPORT_RAW_GD"
table_ref = client.dataset(dataset_id).table(table_id)
table = bigquery.Table(table_ref, schema=REPORT_RAW_SCHEMA)
table.time_partitioning = bigquery.TimePartitioning(
    type_=bigquery.TimePartitioningType.DAY, field="EDW_UPD_DT", require_partition_filter=True
)
table = client.create_table(table)
print(f"Created table {table.project}.{table.dataset_id}.{table.table_id}")

table_id = "REPORT_GD"
table_ref = client.dataset(dataset_id).table(table_id)
table = bigquery.Table(table_ref, schema=REPORT_SCHEMA)
table.time_partitioning = bigquery.TimePartitioning(
    type_=bigquery.TimePartitioningType.DAY, field="EDW_UPD_DT", require_partition_filter=False
)
table = client.create_table(table)
print(f"Created table {table.project}.{table.dataset_id}.{table.table_id}")
