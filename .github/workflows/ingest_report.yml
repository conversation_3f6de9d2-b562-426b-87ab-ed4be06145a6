name: Ingest Report Data

on:
    push:
        branches:
            - main
            - develop
    schedule:
        - cron: "0 12,20 * * *" # At 12:00 PM UTC and 8:00 PM UTC every day

env:
    PROJECT_ID: one-global-dilab-matchback-dev
    REGION: asia-southeast1
    REPO_NAME: bpit-mb-docker-images
    IMAGE_NAME: ncop-matchback
    TAG: latest

jobs:
    build-push-artifact:
        if: ${{ github.event_name == 'push' }}
        runs-on: ubuntu-latest
        steps:
            - name: "Checkout"
              uses: "actions/checkout@v3"

            - id: "auth"
              uses: "google-github-actions/auth@v1"
              with:
                  credentials_json: "${{ secrets.DEV_GCP_CREDS }}"

            - name: "Set up Cloud SDK"
              uses: "google-github-actions/setup-gcloud@v1"

            - name: "Use gcloud CLI"
              run: "gcloud info"

            - name: "Docker auth"
              run: |-
                  gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

            - name: Build image
              run: docker build -t "${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPO_NAME }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}" .

            - name: Push image
              run: docker push "${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPO_NAME }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}"

    submit-google-batch:
        if: ${{ success() || github.event_name == 'schedule' }}
        needs: [build-push-artifact]
        runs-on: ubuntu-latest
        steps:
            - name: "Checkout"
              uses: "actions/checkout@v3"

            - id: "auth"
              uses: "google-github-actions/auth@v1"
              with:
                  credentials_json: "${{ secrets.DEV_GCP_CREDS }}"

            - name: "Set up Cloud SDK"
              uses: "google-github-actions/setup-gcloud@v1"

            - name: "Use gcloud CLI"
              run: "gcloud info"

            - name: "Trigger Google Batch"
              run: |-
                  sed -i 's|{{SERVICE_ACCOUNT_ENCODE}}|${{ secrets.DEV_SERVICE_ACCOUNT_ENCODE }}|g' gcp/batch/ingest_report.json
                  sed -i 's|{{REPORT_DATA_FOLDER_ID}}|${{ secrets.REPORT_DATA_FOLDER_ID }}|g' gcp/batch/ingest_report.json
                  sed -i 's|{{IMAGE_URI}}|${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPO_NAME }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}|g' gcp/batch/ingest_report.json
                  gcloud batch jobs submit projects/${{ env.PROJECT_ID }}/locations/${{ env.REGION }}/jobs/ingest-report-gdrive-${{ github.run_id }}-${{ github.run_attempt }} --config=gcp/batch/ingest_report.json
