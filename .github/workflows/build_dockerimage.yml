name: <PERSON><PERSON> and Push Docker Image

on:
    push:
        branches: [main, staging, develop]
        tags: ["v*.*.*"]
    pull_request:
        branches: [main, staging, develop]
    workflow_dispatch:
        inputs:
            branch_type:
                description: 'Type of branch to build'
                required: true
                default: 'feature'
                type: choice
                options:
                - feature
                - feat
                - test
    # schedule:
    #   - cron: "0 12,20 * * *" # At 12:00 PM UTC and 8:00 PM UTC every day

env:
    PROJECT_ID: one-global-dilab-matchback-dev
    REGION: asia-southeast1
    REPO_NAME: iro-repo
    IMAGE_NAME: street-turn-matching-service

jobs:
    lint:
        # Uncomment this line if we are running on a solitary OS
        # runs-on: ${{ matrix.os }}
        runs-on: ubuntu-latest
        strategy:
            matrix:
                # Uncomment this line if we are running on multiple operating systems
                # os: ["ubuntu-latest", "macos-latest", "windows-latest"]
                python-version: ["3.11", "3.12"] # Python 3.13 may not be available yet

        steps:
            - name: Checkout
              uses: actions/checkout@v3

            - name: Set up Python ${{ matrix.python-version }}
              uses: actions/setup-python@v4
              with:
                  python-version: ${{ matrix.python-version }}
                  cache: "pip"

            - name: Install linting tools
              run: |
                  python -m pip install --upgrade pip
                  pip install ruff mypy types-PyYAML types-pytz pydantic types-aiofiles

            - name: Run Ruff linting
              uses: astral-sh/ruff-action@v3

            - run: ruff check --fix
            - run: ruff format

            - name: Run MyPy type checking
              run: mypy . --ignore-missing-imports --no-implicit-optional --explicit-package-bases

    # test:
    #     needs: lint
    #     runs-on: ubuntu-latest
    #     strategy:
    #         matrix:
    #             python-version: ["3.11", "3.12"]
    #     steps:
    #         - name: Checkout
    #           uses: actions/checkout@v3

    #         - name: Set up Python ${{ matrix.python-version }}
    #           uses: actions/setup-python@v4
    #           with:
    #               python-version: ${{ matrix.python-version }}

    #         - name: Install Poetry
    #           run: |
    #               python -m pip install --upgrade pip
    #               pip install poetry

    #         - name: Install dependencies with Poetry
    #           run: |
    #               poetry install --no-interaction

    #         - name: Run tests with Poetry
    #           run: |
    #               poetry run pytest -W ignore::pytest.PytestAssertRewriteWarning

    # security-scan:
    #   needs: lint
    #   runs-on: ubuntu-latest
    #   steps:
    #     - name: Checkout
    #       uses: actions/checkout@v3

    #     - name: Run Trivy vulnerability scanner
    #       uses: aquasecurity/trivy-action@master
    #       with:
    #         image-ref: '.'
    #         format: 'table'
    #         exit-code: '1'
    #         ignore-unfixed: true
    #         severity: 'CRITICAL,HIGH'

    build-push-artifact:
        needs: [lint] # test, security-scan
        runs-on: ubuntu-latest
        outputs:
            image_tag: ${{ steps.docker_meta.outputs.tags }}
        steps:
            - name: Checkout
              uses: actions/checkout@v3
              with:
                  fetch-depth: 0

            - name: Authenticate to Google Cloud
              uses: google-github-actions/auth@v1
              with:
                  credentials_json: ${{ secrets.DEV_GCP_CREDS }}

            - name: Set up Cloud SDK
              uses: google-github-actions/setup-gcloud@v1

            - name: Configure Docker to use gcloud credentials
              run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v2

            - name: Generate Docker Tags
              id: docker_meta
              uses: docker/metadata-action@v4
              with:
                  images: ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPO_NAME }}/${{ env.IMAGE_NAME }}
                  tags: |
                      # Production tags (main branch)
                      type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}
                      type=raw,value=prod,enable=${{ github.ref == 'refs/heads/main' }}
                      type=semver,pattern={{version}}
                      type=semver,pattern={{major}}.{{minor}}
                      # Staging tags
                      type=raw,value=staging,enable=${{ github.ref == 'refs/heads/staging' }}
                      type=raw,value=staging-{{sha}},enable=${{ github.ref == 'refs/heads/staging' }}
                      # Development tags
                      type=raw,value=develop,enable=${{ github.ref == 'refs/heads/develop' }}
                      type=ref,event=pr,prefix=pr-
                      type=sha,format=short,prefix=sha-
                      # Feature branch tags
                      type=ref,event=branch,suffix=-{{sha}}
                      type=ref,event=branch,prefix=feature-,enable=${{ startsWith(github.ref, 'refs/heads/feature/') }}
                      type=ref,event=branch,prefix=feat-,enable=${{ startsWith(github.ref, 'refs/heads/feat/') }}
                      type=ref,event=branch,prefix=test-,enable=${{ startsWith(github.ref, 'refs/heads/test/') }}

            - name: Set build args
              id: build_args
              run: |
                  echo "BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_OUTPUT
                  echo "GIT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
                  echo "ENVIRONMENT=${{
                    github.ref == 'refs/heads/main' && 'production' ||
                    github.ref == 'refs/heads/staging' && 'staging' ||
                    'development'
                  }}" >> $GITHUB_OUTPUT

            - name: Build and push
              uses: docker/build-push-action@v4
              with:
                  context: .
                  push: ${{ github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/develop') || (github.event_name == 'pull_request' && (github.base_ref == 'develop' || github.base_ref == 'staging')) || startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && (startsWith(github.ref, 'refs/heads/feature/') || startsWith(github.ref, 'refs/heads/feat/') || startsWith(github.ref, 'refs/heads/test/'))) }}
                  tags: ${{ steps.docker_meta.outputs.tags }}
                  labels: |
                      org.opencontainers.image.created=${{ steps.build_args.outputs.BUILD_DATE }}
                      org.opencontainers.image.revision=${{ steps.build_args.outputs.GIT_SHA }}
                      org.opencontainers.image.version=${{ steps.docker_meta.outputs.version }}
                      org.opencontainers.image.environment=${{ steps.build_args.outputs.ENVIRONMENT }}
                  cache-from: type=gha
                  cache-to: type=gha,mode=max
                  build-args: |
                      ENVIRONMENT=${{ steps.build_args.outputs.ENVIRONMENT }}

    deploy-development:
        needs: build-push-artifact
        if: |
            github.ref == 'refs/heads/develop' ||
            (github.event_name == 'pull_request' && github.base_ref == 'develop')
        runs-on: ubuntu-latest
        environment: development
        steps:
            - name: Deploy to development
              run: |
                  echo "Deploying to development environment"
                  # Add development deployment steps here

    deploy-staging:
        needs: build-push-artifact
        if: |
            github.ref == 'refs/heads/staging' ||
            (github.event_name == 'pull_request' && github.base_ref == 'staging')
        runs-on: ubuntu-latest
        environment: staging
        steps:
            - name: Deploy to staging
              run: |
                  echo "Deploying to staging environment"
                  # Add staging deployment steps here

    deploy-production:
        needs: build-push-artifact
        if: |
            github.ref == 'refs/heads/main' ||
            startsWith(github.ref, 'refs/tags/v')
        runs-on: ubuntu-latest
        environment: production
        concurrency:
            group: production
            cancel-in-progress: false
        steps:
            - name: Deploy to production
              run: |
                  echo "Deploying to production environment"
                  # Add production deployment steps here

    deploy-feature-testing:
        needs: build-push-artifact
        if: |
            github.event_name == 'workflow_dispatch' && (
                startsWith(github.ref, 'refs/heads/feature/') ||
                startsWith(github.ref, 'refs/heads/feat/') ||
                startsWith(github.ref, 'refs/heads/test/')
            )
        runs-on: ubuntu-latest
        environment: feature-testing
        steps:
            - name: Deploy to feature testing environment
              run: |
                  echo "Deploying feature branch to testing environment"
                  echo "Branch: ${{ github.ref_name }}"
                  echo "Image tag: ${{ needs.build-push-artifact.outputs.image_tag }}"
                  # Add feature testing deployment steps here
                  # This could deploy to a separate testing environment/namespace
