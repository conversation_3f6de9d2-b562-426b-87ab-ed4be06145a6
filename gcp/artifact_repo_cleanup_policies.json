[{"name": "dev-cleanup", "action": {"type": "Delete"}, "description": "Delete development and feature branch images after 30 days", "condition": {"tagState": "TAGGED", "tagPrefixes": ["dev", "feature-", "pr-", "sha-"], "versionNamePrefixes": [], "packageNamePrefixes": [], "olderThan": "30d", "newerThan": ""}}, {"name": "rc-cleanup", "action": {"type": "Delete"}, "description": "Delete release candidates and beta images after 90 days", "condition": {"tagState": "TAGGED", "tagPrefixes": ["rc-", "beta-"], "versionNamePrefixes": [], "packageNamePrefixes": [], "olderThan": "90d", "newerThan": ""}}, {"name": "prod-cleanup", "action": {"type": "Delete"}, "description": "Delete patch versions older than 1 year", "condition": {"tagState": "TAGGED", "tagPrefixes": [], "versionNamePrefixes": ["v"], "packageNamePrefixes": [], "olderThan": "365d", "newerThan": ""}}, {"name": "keep-important", "action": {"type": "Keep"}, "description": "Always keep major and minor versions, prod and stable tags", "condition": {"tagState": "TAGGED", "tagPrefixes": ["prod", "stable"], "versionNamePrefixes": [], "packageNamePrefixes": [], "olderThan": "", "newerThan": ""}}, {"name": "keep-recent-releases", "action": {"type": "Keep", "keepCount": 3}, "description": "Keep the 3 most recent release versions", "condition": {"tagState": "TAGGED", "tagPrefixes": ["rc-"], "versionNamePrefixes": [], "packageNamePrefixes": [], "olderThan": "", "newerThan": ""}}, {"name": "untagged-cleanup", "action": {"type": "Delete"}, "description": "Delete untagged images after 14 days", "condition": {"tagState": "UNTAGGED", "tagPrefixes": [], "versionNamePrefixes": [], "packageNamePrefixes": [], "olderThan": "14d", "newerThan": ""}}]