// Configuration
const userProperties = PropertiesService.getScriptProperties();
const CONFIG = {
    API: {
        BASE_URL: userProperties.getProperty("BASE_URL"),
        ENDPOINTS: {
            HEALTH_CHECK: "/api/v1/health",
            REOPTIMIZE: "/api/v1/matching/v2/reoptimize",
            RELOAD: "/api/v1/matching/reload",
        },
        API_KEY: userProperties.getProperty("API_KEY"),
    },
    SHEETS: {
        DASHBOARD_SHEET: "Dashboard",
        SETTINGS_DISTANCE_CELL: "D7",
        SETTINGS_TIME_TOLERANCE_CELL: "D8",
        SETTINGS_SELECTED_PORT: "D9",
        SETTINGS_COST_SAVE_OPTION: "D10",
        SETTINGS_RANKING_PRIORITY: "D11",
        RESULT_START_ROW: 29,
        RESULT_SELECT_COLS: ["B", "F"],
    },
    PORT_TO_NODE: {
        USALT: "USALT63",
        USCHI: "USCHI79",
        USLAX: "USLAX01",
    },
    MENU_NAME: "Street Turn Operations",
};

// UI Helper Functions
class UI {
    static showConfirmationDialog(title, message) {
        const ui = SpreadsheetApp.getUi();
        return ui.alert(title, message, ui.ButtonSet.YES_NO);
    }

    static showAlert(title, message) {
        const ui = SpreadsheetApp.getUi();
        return ui.alert(title, message, ui.ButtonSet.OK);
    }

    static showRichProcessingDialog(params) {
        // Create HTML content for rich formatting
        const formulaExplanation =
            params.streetturnCostOption === "FORMULA_2"
                ? "Option 1: Street-turn cost = Return rate import + Oneway rate export"
                : "Option 2: Street-turn cost = Oneway rate import + Oneway rate export + 50% surcharge street-turn import + 50% surcharge street-turn export";

        const displayMapping = {
            street_turn_total_cost: "Street-turn cost",
            cost_save: "Cost saved",
            dist_sav_km: "Distance saved",
            time_gap: "Time gap",
        };
        const displayRankingPriority = displayMapping[params.rankingPriority] || params.rankingPriority;

        const htmlContent = `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
            <h2 style="color: #1f4e79; margin-bottom: 20px;">🚀 Processing Request</h2>

            <div style="background: #f0f8ff; border-left: 4px solid #4CAF50; padding: 15px; margin: 15px 0;">
                <h3 style="color: #2e7d32; margin-top: 0;">📊 Parameters</h3>
                <ul style="line-height: 1.6;">
                    <li><strong>Maximum Distance:</strong> ${params.maxDistanceKm.toFixed(2)} km (${
            params.maxDistanceMiles
        } miles)</li>
                    <li><strong>Time Tolerance:</strong> ${params.timeTolerance} days</li>
                    <li><strong>COPs Excluded:</strong> ${
                        params.COPItems && params.COPItems.length > 0
                            ? params.COPItems.join(", ")
                            : "No COP been excluded"
                    }</li>
                    <li><strong>Port Location:</strong> ${params.nodeItems}</li>
                    <li><strong>Cost Save Formula:</strong> ${formulaExplanation}</li>
                    <li><strong>Ranking Priority:</strong> ${displayRankingPriority}</li>
                </ul>
            </div>

            <div style="background: #fff3cd; border: 2px solid #ff6b35; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <h3 style="color: #e74c3c; margin-top: 0;">⚠️ IMPORTANT INSTRUCTIONS</h3>
                <div style="color: #721c24;">
                    <p><strong>After clicking OK, please wait while the server processes your data.</strong></p>
                    <div style="margin: 10px 0;">
                        <div style="color: #155724; margin: 5px 0;">✅ The server will write results directly to your spreadsheet</div>
                        <div style="color: #155724; margin: 5px 0;">✅ Keep this spreadsheet open until processing completes</div>
                        <div style="color: #155724; margin: 5px 0;">✅ Processing may take several minutes</div>
                    </div>
                    <div style="margin: 10px 0;">
                        <div style="color: #721c24; margin: 5px 0;">❌ DO NOT close this spreadsheet during processing</div>
                        <div style="color: #721c24; margin: 5px 0;">❌ DO NOT navigate away from this page</div>
                    </div>
                </div>
            </div>
        </div>
        <script>
            google.script.host.close();
        </script>
        `;

        const htmlOutput = HtmlService.createHtmlOutput(htmlContent).setWidth(500).setHeight(400);

        SpreadsheetApp.getUi().showModalDialog(htmlOutput, "Street Turn Operations - Processing Request");
    }

    static showProcessingDialog(params) {
        // Get detailed formula explanation
        let formulaExplanation = "";
        if (params.streetturnCostOption === "FORMULA_2") {
            formulaExplanation = "Option 1: Street-turn cost = Return rate import + Oneway rate export";
        } else if (params.streetturnCostOption === "FORMULA_4") {
            formulaExplanation =
                "Option 2: Street-turn cost = Oneway rate import + Oneway rate export + 50% surcharge street-turn import + 50% surcharge street-turn export";
        }

        // Convert API ranking priority values back to human-readable for display
        const displayMapping = {
            street_turn_total_cost: "Street-turn cost",
            cost_save: "Cost saved",
            dist_sav_km: "Distance saved",
            time_gap: "Time gap",
        };
        const displayRankingPriority = displayMapping[params.rankingPriority] || params.rankingPriority;

        this.showAlert(
            "Processing Request",
            `🚀 SENDING REQUEST TO REOPTIMIZE MATCHING

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📊 PARAMETERS:
• Maximum Distance: ${params.maxDistanceKm.toFixed(2)} km (converted from ${params.maxDistanceMiles} miles)
• Time Tolerance: ${params.timeTolerance} days
• COPs Excluded: ${params.COPItems && params.COPItems.length > 0 ? params.COPItems.join(", ") : "No COP been excluded"}
• Port Location: ${params.nodeItems}
• Cost Save Formula: ${formulaExplanation}
• Ranking Priority: ${displayRankingPriority}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

⚠️  IMPORTANT INSTRUCTIONS ⚠️

After clicking OK, please wait while the server processes your data.

✅ The server will write results directly to your spreadsheet
✅ Keep this spreadsheet open until processing completes
✅ Processing may take several minutes depending on data size

❌ DO NOT close this spreadsheet during processing
❌ DO NOT navigate away from this page`
        );
    }
}

// API Service
class ApiService {
    constructor() {
        const userProperties = PropertiesService.getScriptProperties();
        this.apiKey = userProperties.getProperty("API_KEY");
    }

    reoptimize(params) {
        const url = CONFIG.API.BASE_URL + CONFIG.API.ENDPOINTS.REOPTIMIZE;
        const options = {
            method: "post",
            headers: {
                accept: "application/json",
                "X-API-Key": this.apiKey,
            },
            contentType: "application/json",
            payload: JSON.stringify({
                maximum_distance_thres_km: params.maxDistanceKm,
                time_tolerance_thres: params.timeTolerance,
                cop_list: params.COPItems || [],
                node_cd: params.nodeItems || "",
                street_turn_cost_option: params.streetturnCostOption || "FORMULA_2",
                primary_ranking_options: params.rankingPriority || "street_turn_total_cost",
            }),
            muteHttpExceptions: true,
        };

        const response = UrlFetchApp.fetch(url, options);
        const responseData = {
            code: response.getResponseCode(),
            content: JSON.parse(response.getContentText()),
        };

        return responseData;
    }

    healthCheck() {
        const url = CONFIG.API.BASE_URL + CONFIG.API.ENDPOINTS.HEALTH_CHECK;
        const options = {
            method: "get",
            headers: {
                accept: "application/json",
                "X-API-Key": CONFIG.API.API_KEY,
            },
            muteHttpExceptions: true,
        };

        const response = UrlFetchApp.fetch(url, options);
        const responseData = {
            code: response.getResponseCode(),
            content: JSON.parse(response.getContentText()),
        };

        return responseData;
    }

    reload(params) {
        const url = CONFIG.API.BASE_URL + CONFIG.API.ENDPOINTS.RELOAD;
        const options = {
            method: "post",
            headers: {
                accept: "application/json",
                "X-API-Key": this.apiKey,
            },
            contentType: "application/json",
            payload: JSON.stringify({
                node_cd: params.nodeItems || "",
            }),
            muteHttpExceptions: true,
        };

        const response = UrlFetchApp.fetch(url, options);
        const responseData = {
            code: response.getResponseCode(),
            content: JSON.parse(response.getContentText()),
        };

        return responseData;
    }
}

// Settings Service
class SettingsService {
    constructor(spreadsheet) {
        this.sheet = spreadsheet.getSheetByName(CONFIG.SHEETS.DASHBOARD_SHEET);
        if (!this.sheet) {
            throw new Error(`Could not find the "${CONFIG.SHEETS.DASHBOARD_SHEET}" sheet`);
        }
    }

    getParameters() {
        const maxDistanceMiles = this.sheet.getRange(CONFIG.SHEETS.SETTINGS_DISTANCE_CELL).getValue();
        const timeTolerance = this.sheet.getRange(CONFIG.SHEETS.SETTINGS_TIME_TOLERANCE_CELL).getValue();
        const maxDistanceKm = maxDistanceMiles * 1.60934;
        const nodeItems = String(this.sheet.getRange(CONFIG.SHEETS.SETTINGS_SELECTED_PORT).getValue())
            .toUpperCase()
            .trim();

        // Get cost save option from D10 and map to formula
        const streetturnCostOptionRaw = this.sheet.getRange(CONFIG.SHEETS.SETTINGS_COST_SAVE_OPTION).getValue();
        let streetturnCostOption;
        if (typeof streetturnCostOptionRaw === "string") {
            const option = streetturnCostOptionRaw.trim();
            if (option === "IRT + EOW") {
                streetturnCostOption = "FORMULA_2";
            } else if (option === "IOW + EOW + SCG") {
                streetturnCostOption = "FORMULA_4";
            } else {
                streetturnCostOption = null;
            }
        } else {
            streetturnCostOption = null;
        }

        // Get ranking priority from D11 and convert to API value
        const rankingPriorityRaw = this.sheet.getRange(CONFIG.SHEETS.SETTINGS_RANKING_PRIORITY).getValue();

        // Map human-readable value to API value
        const rankingMapping = {
            "Street-turn cost": "street_turn_total_cost",
            "Cost saved": "cost_save",
            "Distance saved": "dist_sav_km",
            "Time gap": "time_gap",
        };

        let rankingPriorityValue = "street_turn_total_cost"; // default value
        if (rankingPriorityRaw && rankingPriorityRaw !== "" && rankingPriorityRaw !== "-") {
            const mappedValue = rankingMapping[rankingPriorityRaw];
            if (mappedValue) {
                rankingPriorityValue = mappedValue;
            } else {
                console.warn(`Invalid ranking priority value found: ${rankingPriorityRaw}`);
            }
        }

        return {
            maxDistanceMiles,
            maxDistanceKm,
            timeTolerance,
            nodeItems,
            streetturnCostOption,
            rankingPriority: rankingPriorityValue,
        };
    }
}

// Result Service
class SelectionService {
    constructor(spreadsheet) {
        this.sheet = spreadsheet.getSheetByName(CONFIG.SHEETS.DASHBOARD_SHEET);
        if (!this.sheet) {
            throw new Error(`Could not find the "${CONFIG.SHEETS.DASHBOARD_SHEET}" sheet`);
        }
    }

    getCOPItems() {
        const lastRow = this.sheet.getLastRow();
        if (lastRow < CONFIG.SHEETS.RESULT_START_ROW) {
            return [];
        }

        const numRows = lastRow - CONFIG.SHEETS.RESULT_START_ROW + 1;
        let COPItems = [];

        // exclude import
        var selections = this.sheet
            .getRange(
                CONFIG.SHEETS.RESULT_START_ROW,
                this.getColumnNumber(CONFIG.SHEETS.RESULT_SELECT_COLS[0]),
                numRows,
                2
            )
            .getValues();
        for (let i = 0; i < selections.length; i++) {
            // Check column B (index 0) and add value from column C if true
            if (selections[i][0] === true) {
                COPItems.push(selections[i][1]); // C column value
            }
        }

        // exclude export
        selections = this.sheet
            .getRange(
                CONFIG.SHEETS.RESULT_START_ROW,
                this.getColumnNumber(CONFIG.SHEETS.RESULT_SELECT_COLS[1]),
                numRows,
                2
            )
            .getValues();
        for (let i = 0; i < selections.length; i++) {
            // Check column F (index 0) and add value from column G if true
            if (selections[i][0] === true) {
                COPItems.push(selections[i][1]); // G column value
            }
        }

        // Remove duplicates
        COPItems = [...new Set(COPItems)];

        return COPItems;
    }

    getColumnNumber(column) {
        return column.split("").reduce((prev, curr) => prev * 26 + curr.charCodeAt(0) - "A".charCodeAt(0) + 1, 0);
    }
}

/**
 * Creates a custom menu in Google Sheets when the spreadsheet opens.
 */
function onOpen() {
    const ui = SpreadsheetApp.getUi();
    ui.createMenu(CONFIG.MENU_NAME)
        .addItem("Reoptimize Matching", "reoptimizeMatching")
        .addItem("Reload Data", "reloadData") // Add new menu item
        .addToUi();
}

/**
 * Function that calls the health check API endpoint.
 * @returns {Object} The response from the health check API.
 */
function healthCheck() {
    const apiService = new ApiService();
    const response = apiService.healthCheck();
    console.log("Health Check Response: ", response);
    return response;
}

/**
 * Function that calls the reoptimize API endpoint when the menu item is clicked.
 */
function reoptimizeMatching() {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

    try {
        // Initialize services
        const settingsService = new SettingsService(spreadsheet);
        const selectionService = new SelectionService(spreadsheet);
        const apiService = new ApiService();

        // Get selected items
        const COPItems = selectionService.getCOPItems();

        // Get parameters
        const params = settingsService.getParameters();
        params.COPItems = COPItems;

        // Validate cost save option
        if (
            !params.streetturnCostOption ||
            (params.streetturnCostOption !== "FORMULA_2" && params.streetturnCostOption !== "FORMULA_4")
        ) {
            UI.showAlert(
                "Invalid Parameters",
                `Invalid Cost Save Option: ${params.streetturnCostOption}. Please enter "RTI + OWE" or "OWI + OWE + SCG" in cell D10.`
            );
            return;
        }

        // Validate ranking priority
        if (!params.rankingPriority) {
            UI.showAlert(
                "Invalid Parameters",
                "Ranking Priority cannot be empty. Please fill in cell D11 with a valid ranking priority value:\n" +
                    "- Street-turn cost\n" +
                    "- Cost saved\n" +
                    "- Distance saved\n" +
                    "- Time gap\n\n" +
                    "Use '-' if you want to skip this parameter."
            );
            return;
        }

        // Confirm with user
        const response = UI.showConfirmationDialog(
            "Reoptimize Matching",
            "This will send data to the Street Turn Matching Service for reoptimization. Continue?"
        );

        if (response !== SpreadsheetApp.getUi().Button.YES) {
            return;
        }

        // Show processing dialog (you can choose between text or rich HTML version)
        UI.showProcessingDialog(params); // Enhanced text version
        // UI.showRichProcessingDialog(params); // Rich HTML version with colors

        // Call API
        const apiResponse = apiService.reoptimize(params);
        console.log("API Response: ", apiResponse);

        if (apiResponse.code === 200) {
            UI.showAlert(
                "Request Successful",
                "Your request has been successfully sent and is being processed.\n\n" +
                    "The server is now working on your data and will write results directly to your spreadsheet.\n" +
                    "Please keep this spreadsheet open until the process completes.\n\n" +
                    "Note: The server may take several minutes to update your sheets depending on the amount of data."
            );
        } else {
            throw new Error(`API returned error code: ${apiResponse.code}\n\nDetails: ${apiResponse.content}`);
        }
    } catch (error) {
        console.error("Error: ", error);
        UI.showAlert("Error", "An error occurred: " + error.toString());
    }
}

/**
 * Function that calls the reload API endpoint when the menu item is clicked.
 */
function reloadData() {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

    try {
        const settingsService = new SettingsService(spreadsheet);
        const apiService = new ApiService();

        // Get parameters
        const params = settingsService.getParameters();
        const response = apiService.reload(params);

        if (response.code === 200) {
            UI.showAlert(
                "Success",
                "Data reload request has been sent successfully. Please wait for the data to be updated."
            );
        } else {
            throw new Error(`API returned error code: ${response.code}\n\nDetails: ${response.content}`);
        }
    } catch (error) {
        console.error("Error: ", error);
        UI.showAlert("Error", "An error occurred while reloading data: " + error.toString());
    }
}

/**
 * Creates an onChange trigger for the spreadsheet
 */
function createSpreadsheetTrigger() {
    const ss = SpreadsheetApp.getActive();
    ScriptApp.newTrigger("onEdit").forSpreadsheet(ss).onEdit().create();
}

/**
 * Handles edit events in the spreadsheet
 * @param {Object} e The event object
 */
function onEdit(e) {
    const sheet = e.source.getActiveSheet();
    console.log(e);
    if (sheet.getName() !== CONFIG.SHEETS.DASHBOARD_SHEET) return;

    const range = e.range;
    const col = range.getColumn();
    const row = range.getRow();

    // Only process if edit is in Dashboard sheet and after the start row
    if (row >= CONFIG.SHEETS.RESULT_START_ROW) {
        // Check if edit was in column B or F
        if (col === 2 || col === 6) {
            const checked = range.getValue();
            // Get corresponding data column (C for B, G for F)
            const dataCol = col === 2 ? 3 : 7;
            const copValue = sheet.getRange(row, dataCol).getValue();

            // Get all data for the relevant column pair
            const lastRow = sheet.getLastRow();
            const numRows = lastRow - CONFIG.SHEETS.RESULT_START_ROW + 1;

            if (numRows > 0) {
                const dataRange = sheet.getRange(CONFIG.SHEETS.RESULT_START_ROW, col, numRows, 2);
                const values = dataRange.getValues();

                // Update all checkboxes with matching COP value
                for (let i = 0; i < values.length; i++) {
                    const currentRow = CONFIG.SHEETS.RESULT_START_ROW + i;
                    if (values[i][1] === copValue && currentRow !== row) {
                        // values[i][1] is the data column (C or G)
                        sheet.getRange(currentRow, col).setValue(checked);
                    }
                }
            }
        }
    }
}
