{"taskGroups": [{"taskCount": "1", "parallelism": "1", "taskSpec": {"computeResource": {"cpuMilli": "4000", "memoryMib": "16000"}, "runnables": [{"environment": {"variables": {"SERVICE_ACCOUNT_ENCODE": "{{SERVICE_ACCOUNT_ENCODE}}", "REPORT_DATA_FOLDER_ID": "{{REPORT_DATA_FOLDER_ID}}"}}, "container": {"imageUri": "{{IMAGE_URI}}", "volumes": [], "entrypoint": "python", "commands": ["ingest_report_data.py"]}}], "maxRunDuration": "3600s", "volumes": []}}], "allocationPolicy": {"instances": [{"policy": {"provisioningModel": "STANDARD", "machineType": "e2-standard-4"}}]}, "logsPolicy": {"destination": "CLOUD_LOGGING"}}