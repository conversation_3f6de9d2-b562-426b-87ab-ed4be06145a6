from google.cloud import pubsub_v1
from google.oauth2 import service_account


def publish_message(topic_id: str):
    """Publish a test message to a Google Cloud Pub/Sub topic.

    Args:
        topic_id (str): The ID of the Pub/Sub topic to publish to.

    Returns:
        dict: A dictionary containing:
            - status (str): "success" if message was published successfully
            - message_id (str): The ID of the published message
            - topic (str): The full topic path the message was published to
    """
    project_id = "one-global-dilab-matchback-dev"

    credentials = service_account.Credentials.from_service_account_file(".creds/one-global-dilab-matchback-dev.json")

    # Initialize the Pub/Sub publisher client
    publisher = pubsub_v1.PublisherClient(credentials=credentials)

    # Get the full topic path
    topic_path = publisher.topic_path(project_id, topic_id)
    print(topic_path)
    # Publish the message
    future = publisher.publish(topic_path, b"Hello, Pub/Sub!")

    # Wait for message to be published and get the message ID
    message_id = future.result()

    return {"status": "success", "message_id": message_id, "topic": topic_path}


if __name__ == "__main__":
    topic_id = "workflow-status"
    publish_message(topic_id)


# {
#     "message": {
#         "data": "SGVsbG8gQ2xvdWQgUHViL1N1YiEgSGVyZSBpcyBteSBtZXNzYWdlIQ==",
#         "messageId": "****************",
#         "message_id": "****************",
#         "publishTime": "2021-02-26T19:13:55.749Z",
#         "publish_time": "2021-02-26T19:13:55.749Z",
#     },
#     "subscription": "projects/myproject/subscriptions/mysubscription",
# }
