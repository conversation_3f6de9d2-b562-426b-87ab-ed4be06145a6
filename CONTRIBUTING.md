# Contributing to GHQ BPIT NCOP IRO

Thank you for your interest in contributing to the GHQ BPIT NCOP IRO Street Turn Matching Service! This document provides guidelines for contributing to the project.

## Quick Start for Contributors

For comprehensive development information, please refer to our **[Developer Guide](docs/developer_guide.md)** which covers:

-   Development environment setup
-   Coding standards and best practices
-   Testing procedures
-   Local development workflow

## Code Quality Standards

Our code is our treasure, please take care of it. Follow this simple process:

1. **Make it work** - Ensure functionality is correct and tested
2. **Make it beautiful** - Follow coding standards and conventions
3. **Make it performant** - Optimize for efficiency when needed

## Branching Strategy

### Main Branches

**`main`** - Our production branch. This is where all code ends when it has been **developed**, **tested**, **reviewed** and **approved**.

We aim to keep a linear history in a nice and readable way. Example:

-   build: change version config for v1.2.0
-   fix: resolve 404 issue with auth api
-   feat: add raw query helper
-   feat: add example project with performance testing

**`staging`** - Pre-production testing environment for final validation.

**`develop`** - This is our development branch with the latest features.

### Feature Branches

**`feature/NCMB-XXX-description`** - Where we develop new features. These serve as a basis for our PRs and run through CI pipelines for quality and security checks.

**`feat/NCMB-XXX-description`** - Alternative naming convention for features.

**`test/NCMB-XXX-description`** - Testing-related branches for CI/CD improvements or test development.

**`bug/NCMB-XXX-description`** - Where we develop bug fixes.

**`hotfix/NCMB-XXX-description`** - Critical production fixes that need immediate attention.

### Release Branches

**`release/vX.X.X`** - Preparation branches for new releases, used for final testing and version bumping.

## Quality Assurance

### Testing Requirements

We test our code through unit tests and integration tests with a target of **at least 60% coverage**.

Run tests before submitting PR:

```bash
# Run all tests
poetry run pytest

# Run tests with coverage
poetry run pytest --cov=src --cov-report=html

# Run linting and formatting
poetry run pre-commit run --all-files
```

### Continuous Integration

Our CI/CD pipeline automatically runs:

-   **Linting**: Code style and format checks
-   **Testing**: Unit and integration tests
-   **Security**: Security vulnerability scanning
-   **Build**: Docker image building and validation

For `feature/`, `feat/`, and `test/` branches, CI/CD deployment is **manual-only** and requires explicit triggering through GitHub Actions.

## Pull Request Process

### Creating Pull Requests

We never merge anything without a PR except for automated tasks such as releasing.

When you create a Pull Request:

1. **Self-review first** - Review your own changes thoroughly
2. **Ensure CI passes** - All automated checks must pass
3. **Request reviews** - Ask for review from team members
4. **Provide context** - Include clear description and relevant issue references

### Pull Request Review Process

When reviewing a pull request:

1. **Checkout the PR branch** locally
2. **Verify the build** - Ensure code compiles and runs
3. **Run tests** - Execute test suite locally
4. **Functional testing** - Test the feature/fix functionally
    - Read functional specifications before testing
    - Try edge cases and error scenarios
5. **Code review** - Check code quality, style, and architecture
6. **Performance check** - Consider performance implications
7. **Security review** - Look for potential security issues

### Commit Message Standards

Follow [Conventional Commits](https://www.conventionalcommits.org/) format:

```
<type>: <description>

[optional body]

[optional footer]
```

**Types:**

-   **feat**: New feature
-   **fix**: Bug fix
-   **docs**: Documentation changes
-   **test**: Adding or updating tests
-   **build**: Build system or dependency changes
-   **ci**: CI/CD pipeline changes
-   **refactor**: Code refactoring
-   **style**: Code style changes
-   **chore**: Maintenance tasks

**Examples:**

```
feat: add distance caching for improved performance
fix: resolve null pointer exception in matching service
docs: update API documentation with new endpoints
test: add unit tests for matching algorithm
```

### Merge Strategy

We maintain a clean, linear history using **rebase and fast-forward** merging:

1. **One commit per PR** - Squash multiple commits into a single, well-formatted commit
2. **Descriptive commit messages** - Follow our commit message standards
3. **No merge commits** - Use rebase strategy to maintain linear history

#### Preparing Your Branch for Merge

If you have multiple commits, squash them before merging:

```bash
# On your feature branch
git checkout develop
git pull origin develop

# Go back to your branch
git checkout feature/ISSUE-123-your-feature

# Rebase onto latest develop
git rebase develop

# Interactive rebase to squash commits
git rebase -i HEAD~n  # where n is number of commits to squash

# Force push (only allowed on your own branches)
git push --force-with-lease
```

## Getting Help

### Resources

Before asking for help, please check:

1. **[Developer Guide](docs/developer_guide.md)** - Comprehensive development documentation
2. **[Troubleshooting Guide](docs/troubleshooting_guide.md)** - Common issues and solutions
3. **[API Reference](docs/api_reference.md)** - API documentation
4. **Existing Issues** - Search GitHub issues for similar problems

### Support Channels

1. **GitHub Issues** - For bugs, feature requests, and technical discussions
2. **Pull Request Reviews** - Ask questions during code review process
3. **Documentation** - Refer to project documentation in `docs/` folder

## Code of Conduct

-   **Be respectful** - Treat all contributors with respect and professionalism
-   **Be collaborative** - Work together to improve the project
-   **Be constructive** - Provide helpful feedback and suggestions
-   **Be patient** - Remember that contributors have different experience levels
-   **Be inclusive** - Welcome contributions from all backgrounds and skill levels

Thank you for contributing to make this project better! 🚀
