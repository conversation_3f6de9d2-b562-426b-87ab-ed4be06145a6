# Environment Configuration Sample
# Copy this file to .env and fill in the actual values
# DO NOT commit .env file to version control

# Application Environment
# Possible values: development, staging, production
ENVIRONMENT=

# Google Cloud Platform Configuration
# Base64 encoded service account JSON key for GCP authentication
# Generate from GCP Console -> IAM & Admin -> Service Accounts
SERVICE_ACCOUNT_ENCODE=

# Google Maps API Configuration
# API key for Google Maps services (distance calculation, geocoding)
# Enable Maps JavaScript API and Distance Matrix API in GCP Console
GMAP_API_KEY=

# Google Drive Configuration
# Folder ID where report data files are stored
# Extract from Google Drive folder URL: https://drive.google.com/drive/folders/{FOLDER_ID}
DB_REPORT_DATA_FOLDER_ID=

# Server Configuration
# Host address for the FastAPI server (0.0.0.0 for all interfaces)
HOST=0.0.0.0
# Port number for the FastAPI server
PORT=8001
# Number of worker processes for FastAPI (recommend 1 for development)
FASTAPI_WORKER=1

# API Security
# API key for authenticating requests to the service
# Generate a secure random string for production use
API_KEY=
