from pathlib import Path

import aiofiles
import pandas as pd

from src.const import get_settings
from src.const.col_map import STREET_TURN_MAPPING
from src.thirdparty.gcp import BigQueryManager
from src.utils.decorators import measure_time
from src.utils.logger import log_handler

from .base_loader import DataLoaderBase

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class DistanceDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the DistanceDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self.distance_data: pd.DataFrame = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"street_turn": STREET_TURN_MAPPING}

    @measure_time(logger=logger)
    async def fetch_data(self) -> None:
        """Fetch distance and yard data from BigQuery."""
        query = """
        SELECT {col}
        FROM `{table_name}`
        WHERE 1=1
        """
        table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.DIST_TABLE_ID}"
        query = query.format(col=self._generate_select_clause("street_turn"), table_name=table_name)
        self.distance_data = await self.bq_manager.execute_query(query)

    @measure_time(logger=logger)
    async def process_data(self) -> None:
        """Process the fetched distance data, converting distances to numeric values."""
        if self.distance_data is None:
            raise ValueError("Distance data not fetched. Call fetch_data() first.")

        logger.info("Distance data loaded successfully.")

    async def get_data(self) -> pd.DataFrame:
        """Return the processed distance data as a DataFrame.

        Returns:
            pd.DataFrame: The processed distance data.
        """
        if self.distance_data is None:
            raise ValueError("Distance data not available. Call fetch_data() and then process_data() first.")
        return self.distance_data

    @measure_time(logger=logger)
    async def save_data(self, save_dir: Path) -> None:
        """Save the distance data to a file.

        Args:
            save_dir (Path): The directory to save the data to.
        """
        if self.distance_data is None:
            raise ValueError("Data not loaded. Call fetch_data() and then process_data() first.")
        if not save_dir.exists():
            save_dir.mkdir(parents=True, exist_ok=True)
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")

        def df_to_csv_str():
            return self.distance_data.to_csv(index=False)

        async with aiofiles.open(save_dir / "distance_data.csv", "w") as f:
            await f.write(df_to_csv_str())
        logger.info("Distance data saved to distance_data.csv.")

    @measure_time(logger=logger)
    async def load_data(self, save_dir: Path) -> None:
        """Load the distance data from a file.

        Args:
            save_dir (Path): The directory to load the data from.
        """
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")
        if not save_dir.exists():
            raise FileNotFoundError(f"Directory {save_dir} does not exist.")
        if not (save_dir / "distance_data.csv").exists():
            raise FileNotFoundError(f"Distance data file not found at {save_dir / 'distance_data.csv'}")

        async with aiofiles.open(save_dir / "distance_data.csv") as f:
            content = await f.read()
            import io

            self.distance_data = pd.read_csv(io.StringIO(content))
        logger.info("Distance data loaded from distance_data.csv.")


if __name__ == "__main__":
    import asyncio

    bq_manager = BigQueryManager()
    loader = DistanceDataLoader(bq_manager)
    asyncio.run(loader.fetch_data())
    asyncio.run(loader.process_data())
    distance_data = asyncio.run(loader.get_data())
    print(distance_data)
