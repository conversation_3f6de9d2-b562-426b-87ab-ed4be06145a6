import json
from pathlib import Path

import aiofiles
import pandas as pd

from src.const import get_settings
from src.const.col_map import LOC_MAP, YARD_MAP
from src.thirdparty.gcp import BigQueryManager
from src.utils.decorators import measure_time
from src.utils.logger import log_handler

from .base_loader import DataLoaderBase

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class LocationDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the LocationDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self._location_data: pd.DataFrame = None  # type: ignore [assignment]
        self._yard_data: pd.DataFrame = None  # type: ignore [assignment]
        self.location_dict: dict = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"location": LOC_MAP, "yard": YARD_MAP}

    @measure_time(logger=logger)
    async def fetch_data(self) -> None:
        """Fetch location and yard data from BigQuery."""
        location_query = """
        SELECT {col}
        FROM `{table_name}`
        """
        table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.SRC_DATASET_ID}.{settings.DB.LOCATION_TABLE_ID}"
        location_query = location_query.format(col=self._generate_select_clause("location"), table_name=table_name)

        yard_query = """
        SELECT {col}
        FROM `{table_name}`
        """
        table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.SRC_DATASET_ID}.{settings.DB.YARD_TABLE_ID}"
        yard_query = yard_query.format(col=self._generate_select_clause("yard"), table_name=table_name)

        self._location_data = await self.bq_manager.execute_query(location_query)
        self._yard_data = await self.bq_manager.execute_query(yard_query)

        logger.info("Location and yard data fetched successfully.")

    @measure_time(logger=logger)
    async def process_data(self) -> None:
        """Process the fetched data and create the location dictionary."""
        if self._location_data is None or self._yard_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self._location_data = self._location_data.convert_dtypes()
        self._yard_data = self._yard_data.convert_dtypes()
        self.location_dict = self._location_data.set_index("location_code").to_dict(orient="index")

        yard_groups = self._yard_data.groupby("location_code")
        for loc_code, yards in yard_groups:
            yard_list = yards[["yard_code", "yard_latitude", "yard_longitude"]].to_dict(orient="records")
            if loc_code in self.location_dict:
                self.location_dict[loc_code]["yards"] = yard_list
            else:
                self.location_dict[loc_code] = {
                    "location_name": None,
                    "region_code": None,
                    "latitude": None,
                    "longitude": None,
                    "yards": yard_list,
                }
        # Ensure that all locations have a "yards" key. TODO: may not be necessary
        for _, v in self.location_dict.items():  # noqa: B007
            if "yards" not in v:
                v["yards"] = []

        logger.info("Location and yard data processed successfully.")

    @measure_time(logger=logger)
    async def get_data(self) -> dict:
        """Return the processed location data.

        Returns:
            dict: A dictionary containing location and yard information
        """
        if self.location_dict is None:
            raise ValueError("Data not processed. Call fetch_data() and then process_data() first.")
        return self.location_dict

    @measure_time(logger=logger)
    async def save_data(self, save_dir: Path) -> None:
        """Save the location data to a file.

        Args:
            save_dir (Path): The directory to save the data to.
        """
        if self.location_dict is None:
            raise ValueError("Location data not loaded. Call fetch_data() and then process_data() first.")
        if not save_dir.exists():
            save_dir.mkdir(parents=True, exist_ok=True)
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")

        async with aiofiles.open(save_dir / "location_data.json", "w") as f:
            await f.write(json.dumps(self.location_dict))
        logger.info("Location data saved to location_data.json.")

    @measure_time(logger=logger)
    async def load_data(self, save_dir: Path) -> None:
        """Load the location data from a file.

        Args:
            save_dir (Path): The directory to load the data from.
        """
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")
        if not save_dir.exists():
            raise FileNotFoundError(f"Directory {save_dir} does not exist.")
        if not (save_dir / "location_data.json").exists():
            raise FileNotFoundError(f"Location data file not found at {save_dir / 'location_data.json'}")

        async with aiofiles.open(save_dir / "location_data.json") as f:
            content = await f.read()
            self.location_dict = json.loads(content)

        logger.info("Location data loaded from location_data.json.")


if __name__ == "__main__":
    import asyncio

    bq_manager = BigQueryManager()
    loader = LocationDataLoader(bq_manager)

    asyncio.run(loader.fetch_data())
    asyncio.run(loader.process_data())
    location_dict = asyncio.run(loader.get_data())
    print(location_dict)
