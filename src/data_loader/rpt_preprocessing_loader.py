import io
from pathlib import Path

import aiofiles
import pandas as pd

from src.const import get_settings
from src.const.col_map import REPORT_PREPROCESSING_MAP
from src.thirdparty.gcp import BigQueryManager
from src.utils.decorators import measure_time
from src.utils.logger import log_handler

from .base_loader import DataLoaderBase

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class ReportPreprocessingDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the ReportPreprocessingDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self.preprocessing_data: pd.DataFrame = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"preprocessing": REPORT_PREPROCESSING_MAP}

    @measure_time(logger=logger)
    async def fetch_data(  # type: ignore [override]
        self,
        node_cd: tuple[str, ...] = (),
    ) -> None:
        """Fetch preprocessing data from BigQuery.

        Args:
            node_cd (tuple[str, ...], optional): A tuple of specific node codes to filter for. Defaults to an empty tuple.
        """
        query = """
        SELECT
            PORT_CD AS imp_port_cd,
            PORT_CTY AS imp_port_city,
            PORT_CD AS xpt_port_cd,
            PORT_CTY AS xpt_port_city,
            {col}
        FROM `{table_id}`
        WHERE PORT_CD IN ({node_cd})
        """
        table_id = f"{settings.ENV.PROJECT_ID}.OUTPUTS.{settings.DB.REPORT_TABLE_ID}"

        query = query.format(
            col=self._generate_select_clause("preprocessing"),
            table_id=table_id,
            node_cd=",".join(f"'{node}'" for node in node_cd) if node_cd else "'USATL63'",
        )

        self.preprocessing_data = await self.bq_manager.execute_query(query)
        logger.info("Preprocessing data fetched successfully.")

    @measure_time(logger=logger)
    async def process_data(self) -> None:
        """Process the fetched preprocessing data."""
        if self.preprocessing_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self.preprocessing_data = self.preprocessing_data.convert_dtypes()
        logger.info("Preprocessing data processed successfully.")

    async def get_data(self) -> pd.DataFrame:
        """Return the processed preprocessing data.

        Returns:
            pd.DataFrame: The processed preprocessing DataFrame
        """
        if self.preprocessing_data is None:
            raise ValueError("Data not processed. Call fetch_data() and then process_data() first.")
        return self.preprocessing_data

    @measure_time(logger=logger)
    async def save_data(self, save_dir: Path) -> None:
        """Save the preprocessing data to a file.

        Args:
            save_dir (Path): The directory to save the data to.
        """
        if self.preprocessing_data is None:
            raise ValueError("Data not loaded. Call fetch_data() and then process_data() first.")
        if not save_dir.exists():
            save_dir.mkdir(parents=True, exist_ok=True)
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")

        async with aiofiles.open(save_dir / "preprocessing_data.csv", mode="w") as f:
            await f.write(self.preprocessing_data.to_csv(index=False))
        logger.info("Preprocessing data saved to preprocessing_data.csv.")

    @measure_time(logger=logger)
    async def load_data(self, save_dir: Path) -> None:
        """Load the preprocessing data from a file.

        Args:
            save_dir (Path): The directory to load the data from.
        """
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")
        if not save_dir.exists():
            raise FileNotFoundError(f"Directory {save_dir} does not exist.")
        if not (save_dir / "preprocessing_data.csv").exists():
            raise FileNotFoundError(f"Preprocessing data file not found at {save_dir / 'preprocessing_data.csv'}")

        async with aiofiles.open(save_dir / "preprocessing_data.csv") as f:
            content = await f.read()
            self.preprocessing_data = pd.read_csv(io.StringIO(content))

        self.preprocessing_data = self.preprocessing_data.convert_dtypes()
        logger.info("Preprocessing data loaded from preprocessing_data.csv.")


if __name__ == "__main__":
    import asyncio

    bq_manager = BigQueryManager()
    loader = ReportPreprocessingDataLoader(bq_manager)
    asyncio.run(loader.fetch_data())
    asyncio.run(loader.process_data())
    preprocessing_data = asyncio.run(loader.get_data())
    print(preprocessing_data)
