from abc import ABC, abstractmethod
from pathlib import Path


class DataLoaderBase(ABC):
    def __init__(self, db_manager):
        """Initialize the DataLoaderBase with a database manager.

        Args:
            db_manager (DatabaseManager): A database manager.
        """
        self.bq_manager = db_manager
        self.column_mappings: dict[str, dict[str, str]] = {}

    def update_column_mapping(self, data_type: str, new_mapping: dict[str, str]) -> None:
        """Update the column mapping for a specific data type.

        Args:
            data_type (str): The type of data to update
            new_mapping (Dict[str, str]): The new column mapping
        """
        if data_type not in self.column_mappings:
            raise ValueError(f"Invalid data type: {data_type}")
        self.column_mappings[data_type].update(new_mapping)

    def _generate_select_clause(self, data_type: str) -> str:
        """Generate the SELECT clause for a SQL query based on column mappings."""
        return ", ".join([f"{col} as {alias}" for alias, col in self.column_mappings[data_type].items()])

    @abstractmethod
    async def fetch_data(self) -> None:
        """Fetch data from BigQuery. To be implemented by subclasses."""
        pass

    @abstractmethod
    async def process_data(self) -> None:
        """Process the fetched data. To be implemented by subclasses."""
        pass

    @abstractmethod
    async def get_data(self):
        """Return the data. To be implemented by subclasses."""
        pass

    @abstractmethod
    async def save_data(self, save_dir: Path) -> None:
        """Save the data to a file. To be implemented by subclasses."""
        pass

    @abstractmethod
    async def load_data(self, save_dir: Path) -> None:
        """Load the data from a file. To be implemented by subclasses."""
        pass
