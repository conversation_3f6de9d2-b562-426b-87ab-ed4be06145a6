import datetime as dt
import io
from pathlib import Path

import aiofiles
import pandas as pd

from src.const import get_settings
from src.const.col_map import RATELANE_MAPPING
from src.thirdparty.gcp import BigQueryManager
from src.utils.decorators import measure_time
from src.utils.logger import log_handler

from .base_loader import DataLoaderBase

logger = log_handler.get_logger(name=__name__)
settings = get_settings()

# NOTE: temporary mapping for street-turn one-way rates
SCG_ST_OW_RATE_MAP = {
    104402: 2.7,
    106541: 2.75,
    109030: 2.25,
    114917: 2.5,
    118036: 2.1,
    120200: 2.75,
    125759: 2.75,
    131793: 2.8,
    132133: 2.25,
    134973: 2.5,
    200370: 1.8,
    200436: 1.9,
    200946: 4.5,
    220067: 2.0,
    220670: 2.6,
    224295: 2.5,
    232186: 3.0,
    263365: 2.95,
}


class RateLaneMappingLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the RateLaneDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self._ratelane_mapping_data: pd.DataFrame = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"rate_lane_mapping": RATELANE_MAPPING}

    @measure_time(logger=logger)
    async def fetch_data(self, trigger_dt: dt.datetime, node_cd: tuple[str, ...] = ()) -> None:
        """Fetch rate lane data from BigQuery."""
        query = """
        SELECT
            DISTINCT *
        FROM `{table_id}`('{date}', {node_cd})
        WHERE 1=1
            --AND IMP_BSE_RT_RTN IS NOT NULL
            --AND IMP_BSE_RT_ONE_WY IS NOT NULL
            --AND XPT_BSE_RT_RTN is not null
            --AND XPT_BSE_RT_ONE_WY is not null
        """
        table_id = f"{settings.ENV.PROJECT_ID}.{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.RATELANE_TABLE_MAPPING_ID}"
        _trigger_dt_str = trigger_dt.strftime("%Y-%m-%d %H:%M:%S")
        query = query.format(table_id=table_id, date=_trigger_dt_str, node_cd=[f"{node}" for node in node_cd])

        self._ratelane_mapping_data = await self.bq_manager.execute_query(query)
        logger.info("Rate lane mapping data fetched successfully.")

    async def create_round_trip_df(self):
        """Create a DataFrame for round trip rates."""
        rt_table = self._ratelane_mapping_data[["LOC_CD", "VNDR_CD", "IMP_BSE_RT_RTN", "XPT_BSE_RT_RTN"]].dropna(
            subset=["IMP_BSE_RT_RTN", "XPT_BSE_RT_RTN"]
        )

        # Find minimum import rates and corresponding vendor codes
        import_min_idx = rt_table.groupby("LOC_CD")["IMP_BSE_RT_RTN"].idxmin()
        import_min_data = rt_table.loc[import_min_idx, ["LOC_CD", "VNDR_CD", "IMP_BSE_RT_RTN"]].rename(
            columns={"VNDR_CD": "min_import_vendor_cd", "IMP_BSE_RT_RTN": "min_import_base_rate"}
        )

        # Find minimum export rates and corresponding vendor codes
        export_min_idx = rt_table.groupby("LOC_CD")["XPT_BSE_RT_RTN"].idxmin()
        export_min_data = rt_table.loc[export_min_idx, ["LOC_CD", "VNDR_CD", "XPT_BSE_RT_RTN"]].rename(
            columns={"VNDR_CD": "min_export_vendor_cd", "XPT_BSE_RT_RTN": "min_export_base_rate"}
        )

        # Merge the import and export minimum data
        rt_table = pd.merge(import_min_data, export_min_data, on="LOC_CD")
        rt_table = rt_table.rename(columns={"LOC_CD": "loc_cd"})
        rt_table.set_index("loc_cd", inplace=True)
        return rt_table

    async def create_street_turn_df(self):
        """Create a DataFrame for street turn rates."""
        st_table = self._ratelane_mapping_data.dropna(subset=["XPT_BSE_RT_ONE_WY"])
        st_table = st_table[
            [
                "VNDR_CD",
                "LOC_CD",
                "IMP_BSE_RT_RTN",
                "IMP_BSE_RT_ONE_WY",
                "XPT_BSE_RT_RTN",
                "XPT_BSE_RT_ONE_WY",
                "SCG_ST_OW_RATE",
            ]
        ]
        st_table = st_table.rename(
            columns={
                "VNDR_CD": "vendor_cd",
                "LOC_CD": "loc_cd",
                "IMP_BSE_RT_RTN": "import_base_rate_return",
                "IMP_BSE_RT_ONE_WY": "import_base_rate_one_way",
                "XPT_BSE_RT_RTN": "export_base_rate_return",
                "XPT_BSE_RT_ONE_WY": "export_base_rate_one_way",
                "SCG_ST_OW_RATE": "surcharge_street_turn_one_way",
            }
        )
        st_table.set_index(["vendor_cd", "loc_cd"], inplace=True)
        return st_table

    @measure_time(logger=logger)
    async def process_data(self) -> None:
        """Process the fetched rate lane data."""
        if self._ratelane_mapping_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self._ratelane_mapping_data = self._ratelane_mapping_data.convert_dtypes()
        for col in ["IMP_BSE_RT_RTN", "IMP_BSE_RT_ONE_WY", "XPT_BSE_RT_RTN", "XPT_BSE_RT_ONE_WY"]:
            self._ratelane_mapping_data[col] = self._ratelane_mapping_data[col].astype(float)

        self._ratelane_mapping_data["SCG_ST_OW_RATE"] = self._ratelane_mapping_data["VNDR_CD"].map(SCG_ST_OW_RATE_MAP)
        self.round_trip_df = await self.create_round_trip_df()
        self.street_turn_df = await self.create_street_turn_df()
        logger.info("Rate lane mapping data processed successfully.")

    async def get_data(self) -> dict[str, pd.DataFrame]:
        """Return the processed rate lane data.

        Returns:
            pd.DataFrame: The processed rate lane data.
        """
        if self._ratelane_mapping_data is None:
            raise ValueError("Data not processed. Call fetch_data() and then process_data() first.")
        return {
            "rl_map": self._ratelane_mapping_data,
            "rt": self.round_trip_df,
            "st": self.street_turn_df,
        }

    @measure_time(logger=logger)
    async def save_data(self, save_dir: Path) -> None:
        """Save the rate lane data to a file.

        Args:
            save_dir (Path): The directory to save the file to.
        """
        if self._ratelane_mapping_data is None:
            raise ValueError("Rate lane data not loaded. Call fetch_data() and then process_data() first.")
        if not save_dir.exists():
            save_dir.mkdir(parents=True, exist_ok=True)
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")

        async with aiofiles.open(save_dir / "import_rate_lane_mapping.csv", "w") as f:
            await f.write(self._ratelane_mapping_data.to_csv(index=False))

        logger.info("Rate lane mapping data saved to import_rate_lane_mapping.csv and export_rate_lane_mapping.csv.")

    @measure_time(logger=logger)
    async def load_data(self, save_dir: Path) -> None:
        """Load the rate lane mapping data from a file.

        Args:
            save_dir (Path): The directory to load the file from.
        """
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")
        if not save_dir.exists():
            raise FileNotFoundError(f"Directory {save_dir} does not exist.")
        if (
            not (save_dir / "import_rate_lane_mapping.csv").exists()
            or not (save_dir / "export_rate_lane_mapping.csv").exists()
        ):
            raise FileNotFoundError(
                f"Rate lane mapping data not found at {save_dir / 'import_rate_lane_mapping.csv'} or {save_dir / 'export_rate_lane_mapping.csv'}"
            )

        async with aiofiles.open(save_dir / "import_rate_lane_mapping.csv") as f:
            content = await f.read()
            self._ratelane_mapping_data = pd.read_csv(io.StringIO(content))

        self._ratelane_mapping_data = self._ratelane_mapping_data.convert_dtypes()
        logger.info("Rate lane mapping data loaded from rate_lane_mapping.csv.")


if __name__ == "__main__":
    import asyncio

    bq_manager = BigQueryManager()
    loader = RateLaneMappingLoader(bq_manager)
    asyncio.run(loader.fetch_data(dt.datetime.now()))
    asyncio.run(loader.process_data())
    imp_data, xpt_data = asyncio.run(loader.get_data())
    print(imp_data)
    print(xpt_data)
