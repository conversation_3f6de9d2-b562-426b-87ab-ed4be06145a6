import datetime as dt
import io
from pathlib import Path

import aiofiles
import pandas as pd

from src.const import get_settings
from src.const.col_map import RATELANE_MAP
from src.thirdparty.gcp import BigQueryManager
from src.utils.decorators import measure_time
from src.utils.logger import log_handler

from .base_loader import DataLoaderBase

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class RateLaneLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the RateLaneDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self._ratelane_data: pd.DataFrame = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"rate_lane": RATELANE_MAP}

    @staticmethod
    def _generate_location_pattern(location: str) -> str:
        """Generate a regex pattern for a specific location.

        The pattern matches strings that start and end with the location (with optional alphanumeric suffixes)
        and have something in between.
        """
        # Pattern for location at start: USATL[A-Z0-9]*_[A-Z0-9]+_[A-Z0-9]+
        start_pattern = f"{location}[A-Z0-9]*_[A-Z0-9]+_[A-Z0-9]+"

        # Pattern for location at end: [A-Z0-9]+_[A-Z0-9]+_USATL[A-Z0-9]*
        end_pattern = f"[A-Z0-9]+_[A-Z0-9]+_{location}[A-Z0-9]*"

        return f"^({start_pattern}|{end_pattern})$"

    async def fetch_data(self, trigger_dt: dt.datetime, node_cd: tuple[str, ...] = ()) -> None:  # type: ignore [override]
        """Fetch rate lane data from BigQuery.

        Args:
            trigger_dt (dt.datetime): The date to filter the data by.
            node_cd (tuple[str, ...]): A tuple of specific container types to filter by.
        """
        query = """
        SELECT DISTINCT {col}
        FROM `{table_name}`('{date}', {node_cd})
        """

        # table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.RATELANE_TABLE_ID}"
        # tmp hardcode dataset
        table_name = f"{settings.ENV.PROJECT_ID}.OUTPUTS.{settings.DB.RATELANE_TABLE_ID}"
        _trigger_dt_str = trigger_dt.strftime("%Y-%m-%d %H:%M:%S")
        query = query.format(
            col=self._generate_select_clause("rate_lane"),
            table_name=table_name,
            date=_trigger_dt_str,
            node_cd=[f"{node}" for node in node_cd],
        )
        self._ratelane_data = await self.bq_manager.execute_query(query)

    @measure_time(logger=logger)
    async def process_data(self) -> None:
        """Process the fetched rate lane data."""
        if self._ratelane_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self._ratelane_data = self._ratelane_data.convert_dtypes()
        logger.info("Ratelane data processed.")

    async def get_data(self) -> pd.DataFrame:
        """Return the processed rate lane data.

        Returns:
            pd.DataFrame: The processed rate lane data.
        """
        if self._ratelane_data is None:
            raise ValueError("Data not processed. Call fetch_data() and then process_data() first.")
        return self._ratelane_data

    @measure_time(logger=logger)
    async def save_data(self, save_dir: Path) -> None:
        """Save the rate lane data to a file.

        Args:
            save_dir (Path): The directory to save the data to.
        """
        if self._ratelane_data is None:
            raise ValueError("Data not loaded. Call fetch_data() and then process_data() first.")
        if not save_dir.exists():
            save_dir.mkdir(parents=True, exist_ok=True)
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")

        async with aiofiles.open(save_dir / "ratelane_data.csv", "w") as f:
            await f.write(self._ratelane_data.to_csv(index=False))
        logger.info("Ratelane data saved to ratelane_data.csv.")

    @measure_time(logger=logger)
    async def load_data(self, save_dir: Path) -> None:
        """Load the rate lane data from a file.

        Args:
            save_dir (Path): The directory to load the data from.
        """
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")
        if not save_dir.exists():
            raise FileNotFoundError(f"Directory {save_dir} does not exist.")
        if not (save_dir / "ratelane_data.csv").exists():
            raise FileNotFoundError(f"Ratelane data file not found at {save_dir / 'ratelane_data.csv'}")

        async with aiofiles.open(save_dir / "ratelane_data.csv") as f:
            content = await f.read()
            self._ratelane_data = pd.read_csv(io.StringIO(content))

        self._ratelane_data = self._ratelane_data.convert_dtypes()
        logger.info("Ratelane data loaded from ratelane_data.csv.")


if __name__ == "__main__":
    import asyncio

    bq_manager = BigQueryManager()
    loader = RateLaneLoader(bq_manager)
    asyncio.run(loader.fetch_data(dt.datetime.now(), ("USATL",)))
    asyncio.run(loader.process_data())
    data = asyncio.run(loader.get_data())
    print(data.head())
