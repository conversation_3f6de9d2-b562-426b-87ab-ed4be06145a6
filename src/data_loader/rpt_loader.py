import datetime as dt
import io
from pathlib import Path

import aiofiles
import pandas as pd

from src.const import get_settings
from src.const.col_map import REPORT_MAP
from src.thirdparty.gcp import BigQueryManager
from src.utils.decorators import measure_time
from src.utils.logger import log_handler

from .base_loader import DataLoaderBase

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class ReportDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the ReportDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self.report_data: pd.DataFrame = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"report": REPORT_MAP}

    @measure_time(logger=logger)
    async def fetch_data(  # type: ignore [override]
        self,
        trigger_dt: dt.datetime,
        door_cy_value: str = "Door",
        hazmat: str = "N",
        customer_nominated_trucker: bool = False,
        drop_and_pick: str | None = "N",
        node_cd: tuple[str, ...] = (),
    ) -> None:
        """Fetch report data from BigQuery with dynamic parameters for filtering.

        Args:
            trigger_dt (str): The date and time for filtering the edw_upd_dt column (format: "YYYY-MM-DDTHH:MM").
            door_cy_value (str): The value to filter for the door_cy column.
            hazmat (str): The value to filter for the hazmat column.
            customer_nominated_trucker (bool): The flag to filter for NULL customer_nominated_trucker column.
            drop_and_pick (str, optional): The value to filter for drop_and_pick column ("N" or None for NULL).
            node_cd (list[str], optional): A list of specific CY values to filter for.

        Raises:
            ValueError: If the run_dt is invalid.
        """
        query = """
        SELECT {col}
        FROM `{table_id}`('{date_time}', '{door_cy_value}', '{hazmat}', {customer_nominated_trucker}, '{drop_and_pick}', {node_cd})
        """
        # tmp hardcode dataset
        table_id = f"{settings.ENV.PROJECT_ID}.OUTPUTS.{settings.DB.REPORT_TABLE_ID}"
        _trigger_dt_str = trigger_dt.strftime("%Y-%m-%d %H:%M:%S")
        query = query.format(
            col=self._generate_select_clause("report"),
            table_id=table_id,
            date_time=_trigger_dt_str,
            door_cy_value=door_cy_value,
            hazmat=hazmat,
            customer_nominated_trucker=customer_nominated_trucker,
            drop_and_pick=drop_and_pick,
            node_cd=[f"{node}" for node in node_cd],
        )
        self.report_data = await self.bq_manager.execute_query(query)

        logger.info("Report data fetched successfully.")

    @measure_time(logger=logger)
    async def process_data(self) -> None:
        """Process the fetched report data."""
        if self.report_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self.report_data = self.report_data.convert_dtypes()
        logger.info("Report data processed successfully.")

    async def get_data(self) -> pd.DataFrame:
        """Return the processed report data.

        Returns:
            pd.DataFrame: The processed report DataFrame
        """
        if self.report_data is None:
            raise ValueError("Data not processed. Call fetch_data() and then process_data() first.")
        return self.report_data

    @measure_time(logger=logger)
    async def save_data(self, save_dir: Path) -> None:
        """Save the report data to a file.

        Args:
            save_dir (Path): The directory to save the data to.
        """
        if self.report_data is None:
            raise ValueError("Data not loaded. Call fetch_data() and then process_data() first.")
        if not save_dir.exists():
            save_dir.mkdir(parents=True, exist_ok=True)
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")

        async with aiofiles.open(save_dir / "report_data.csv", mode="w") as f:
            await f.write(self.report_data.to_csv(index=False))
        logger.info("Report data saved to report_data.csv.")

    @measure_time(logger=logger)
    async def load_data(self, save_dir: Path) -> None:
        """Load the report data from a file.

        Args:
            save_dir (Path): The directory to load the data from.
        """
        if not save_dir.is_dir():
            raise ValueError(f"Provided path {save_dir} is not a directory.")
        if not save_dir.exists():
            raise FileNotFoundError(f"Directory {save_dir} does not exist.")
        if not (save_dir / "report_data.csv").exists():
            raise FileNotFoundError(f"Report data file not found at {save_dir / 'report_data.csv'}")

        async with aiofiles.open(save_dir / "report_data.csv") as f:
            content = await f.read()
            self.report_data = pd.read_csv(io.StringIO(content))

        self.report_data = self.report_data.convert_dtypes()
        logger.info("Report data loaded from report_data.csv.")


if __name__ == "__main__":
    import asyncio

    bq_manager = BigQueryManager()
    loader = ReportDataLoader(bq_manager)
    asyncio.run(loader.fetch_data(dt.datetime.now()))
    asyncio.run(loader.process_data())
    report_data = asyncio.run(loader.get_data())
    print(report_data)