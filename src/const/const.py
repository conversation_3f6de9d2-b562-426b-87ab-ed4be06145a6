# Length of location code prefix
LOCATION_CODE_PREFIX_LENGTH: int = 5
# Default distance when calculation fails
DEFAULT_DISTANCE: float = 999999.0
# Radius of the Earth in km
EARTH_RADIUS: float = 6371.0

# Ranking priorities for matching process
RANKING_PRIORITY_LST: dict[str, dict[str, str]] = {
    "street_turn_total_cost": {"column": "street_turn_total_cost", "type": "min"},
    "cost_save": {"column": "cost_save", "type": "max"},
    "dist_sav_km": {"column": "dist_sav_km", "type": "max"},
    "time_gap": {"column": "time_gap_in_hour", "type": "min_abs"},
}
RANKING_COFF_MAPPING: dict[str, tuple[float, ...]] = {
    "street_turn_total_cost": (0.6, 0.2, 0.1, 0.1),
    "cost_save": (0.2, 0.6, 0.1, 0.1),
    "dist_sav_km": (0.2, 0.1, 0.6, 0.1),
    "time_gap": (0.2, 0.1, 0.1, 0.6),
}
