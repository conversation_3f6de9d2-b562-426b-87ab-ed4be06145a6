from enum import Enum
from functools import lru_cache

import tomli
from dotenv import load_dotenv
from pydantic import Field, SecretStr, computed_field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from src.utils.utils import get_project_id

from .const import RANKING_COFF_MAPPING, RANKING_PRIORITY_LST

load_dotenv()


class EnvironmentType(str, Enum):
    """Environment types."""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class GCSConfig(BaseSettings):
    """Google Cloud Storage configuration."""

    BUCKET_NAME: str

    # BUCKET_FOLDER: str
    @field_validator("BUCKET_NAME")
    @classmethod
    def validate_bucket_name(cls, v: str):
        """Validate the bucket name."""
        if v == "":
            raise ValueError("BUCKET_NAME is empty")
        return v


class GSheetsConfig(BaseSettings):
    SPREADSHEET_ID: str
    RESULT_RANGE: str = "Results!A1"


class GCPConfig(BaseSettings):
    """Google Cloud Platform configuration."""

    STORAGE: GCSConfig
    GSHEETS: GSheetsConfig


class DBConfig(BaseSettings):
    """Database configuration."""

    SRC_DATASET_ID: str
    DE_OUT_DATASET_ID: str
    DE_INTER_DATASET_ID: str

    REPORT_TABLE_ID: str
    DIST_TABLE_ID: str
    LOCATION_TABLE_ID: str
    YARD_TABLE_ID: str
    RATELANE_TABLE_ID: str
    RATELANE_TABLE_MAPPING_ID: str

    MATCHED_CANDIDATE_PAIRS_TABLE_ID: str
    POSSIBLE_CANDIDATE_PAIRS_TABLE_ID: str
    DISTANCE_TABLE_ID: str


class SystemConfig(BaseSettings):
    """System configuration for matching process."""

    NODES: tuple[str, ...] = Field(default=(), description="Specific container yard/port/terminal codes to process")
    DISTANCE_METHOD: str = Field(description="Method to calculate distances between locations")
    MAXIMUM_DISTANCE_THRES_KM: float = Field(description="Distance threshold in km for matching")
    MAX_IMPORT_MATCHES: int = Field(description="Maximum number of import matches to return", default=1)
    MAX_EXPORT_MATCHES: int = Field(description="Maximum number of export matches to return", default=1)
    CRITERIA: str = Field(description="Optimization criteria for matching")
    TIME_TOLERANCE_THRES: int = Field(description="Time gap in days between available date and cutoff date")
    STREET_TURN_COST_OPTIONS: list[str] = Field(
        default=["FORMULA_2"], description="List of cost formulas for street turn calculation"
    )
    # ver 1
    RANKING_PRIORITY_OPTIONS: list[str] = Field(
        default=list(RANKING_PRIORITY_LST.keys()), description="List of ranking priorities for matching"
    )

    # ver 2
    PRIMARY_RANKING_OPTIONS: str = Field(
        default="street_turn_total_cost",
        description="Primary ranking option for matching. Options: street_turn_total_cost, cost_save, dist_sav_km, time_gap",
    )

    DELIVERY_TYPE: str = Field(
        default="DOOR",
        description="Default delivery type. For now, only DOOR is supported",
    )
    HAZMAT: str = Field(default="N", description="Default hazmat match. For now, only N is supported")
    CUSTOMER_NOMINATED_TRUCKER: bool = Field(
        default=False, description="Flag to filter for NULL customer_nominated_trucker"
    )
    DROP_AND_PICK: str = Field(
        default="N",
        description="Default drop and pick match. For now, only N is supported",
    )

    @computed_field  # type: ignore[prop-decorator]
    @property
    def RANKING_PRIORITIES(self) -> list[dict]:  # noqa: D102, N802
        """Return the list of ranking priorities based on the options set."""
        return [
            RANKING_PRIORITY_LST[priority]
            for priority in self.RANKING_PRIORITY_OPTIONS
            if priority in RANKING_PRIORITY_LST
        ]

    @computed_field  # type: ignore[prop-decorator]
    @property
    def RANKING_COFF(self) -> tuple[float, ...]:  # noqa: D102, N802
        """Return the list of ranking priorities based on the options set."""
        return RANKING_COFF_MAPPING[self.PRIMARY_RANKING_OPTIONS]

    @field_validator("DISTANCE_METHOD")
    @classmethod
    def validate_dist_method(cls, v: str):
        """Validate the distance method."""
        valid_methods = ["random", "google_maps", "haversine"]
        if v not in valid_methods:
            raise ValueError(f"Invalid distance method. Must be one of: {valid_methods}")
        return v

    @field_validator("CRITERIA")
    @classmethod
    def validate_criteria(cls, v: str):
        """Validate the optimization criteria."""
        valid_criteria = ["distance", "distance_cost", "cost"]
        if v not in valid_criteria:
            raise ValueError(f"Invalid optimization criteria. Must be one of: {valid_criteria}")
        return v

    @field_validator("DELIVERY_TYPE")
    @classmethod
    def validate_delivery_type(cls, v: str):
        """Validate the delivery type."""
        valid_types = ["Door", "CY"]
        if v not in valid_types:
            raise ValueError(f"Invalid delivery type. Must be one of: {valid_types}")
        return v

    @field_validator("MAX_IMPORT_MATCHES")
    @classmethod
    def validate_max_import_matches(cls, v: int):
        """Ensure MAX_IMPORT_MATCHES is always set to 1."""
        if v != 1:
            raise ValueError("MAX_IMPORT_MATCHES must be set to 1 for correct results.")
        return v

    @field_validator("MAX_EXPORT_MATCHES")
    @classmethod
    def validate_max_export_matches(cls, v: int):
        """Ensure MAX_EXPORT_MATCHES is always set to 1."""
        if v != 1:
            raise ValueError("MAX_EXPORT_MATCHES must be set to 1 for correct results.")
        return v


class EnvConfig(BaseSettings):
    """Environment configuration."""

    ENVIRONMENT: EnvironmentType = Field(default=EnvironmentType.DEVELOPMENT)

    @computed_field  # type: ignore[prop-decorator]
    @property
    def PROJECT_ID(self) -> str:  # noqa: D102, N802
        return get_project_id()

    # Log settings
    LOG_FORMATTER: str = (
        "Log entry for %(name)s:%(asctime)s:%(levelname)s:%(filename)s:%(module)s:%(funcName)s(%(lineno)d): %(message)s"
    )
    LOG_ROTATION: str = "midnight"
    LOG_LEVEL: str = Field(default="DEBUG")

    # GCP settings
    SERVICE_ACCOUNT_ENCODE: SecretStr = Field()
    GMAP_API_KEY: SecretStr = Field()
    DB_REPORT_DATA_FOLDER_ID: str = Field()

    # Server settings
    HOST: str = Field(default="0.0.0.0", description="Host address for the FastAPI application")  # noqa: S104
    PORT: int = Field(default=8000, description="Port for the FastAPI application")
    FASTAPI_WORKER: int = Field(default=4, description="Number of worker processes for the FastAPI application")
    API_KEY: str = Field(
        default="key_for_testing",
        min_length=32,
        max_length=256,
        description="API key for authentication",
        pattern=r"^[A-Za-z0-9_-]+$",
    )
    ## COST_FORMULA settings

    @field_validator("SERVICE_ACCOUNT_ENCODE")
    @classmethod
    def check_service_account(cls, v: str):
        """Check if the service account is set."""
        if v == SecretStr("") or v == "":
            raise ValueError("SERVICE_ACCOUNT_ENCODE is empty")
        return v

    @field_validator("GMAP_API_KEY")
    @classmethod
    def validate_gmap_api_key(cls, v: str):
        """Validate the Google Maps API key."""
        if v == SecretStr("") or v == "":
            raise ValueError("Google Maps API key not set. Please set the GMAP_API_KEY variable.")
        return v

    @field_validator("DB_REPORT_DATA_FOLDER_ID")
    @classmethod
    def validate_db_report_data_folder_id(cls, v: str):
        """Validate the DB_REPORT_DATA_FOLDER_ID."""
        if v == "":
            raise ValueError("DB_REPORT_DATA_FOLDER_ID is empty")
        return v

    @field_validator("API_KEY")
    @classmethod
    def validate_api_key(cls, v: str) -> str:
        """Validate API key format and complexity."""
        if not any(c.isupper() for c in v):
            raise ValueError("API_KEY must contain at least one uppercase letter")
        if not any(c.islower() for c in v):
            raise ValueError("API_KEY must contain at least one lowercase letter")
        if not any(c.isdigit() for c in v):
            raise ValueError("API_KEY must contain at least one number")
        return v

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        validate_assignment=True,
        extra="ignore",
    )

    def is_development(self) -> bool:
        """Check if the environment is development."""
        return self.ENVIRONMENT == EnvironmentType.DEVELOPMENT

    def is_staging(self) -> bool:
        """Check if the environment is staging."""
        return self.ENVIRONMENT == EnvironmentType.STAGING

    def is_production(self) -> bool:
        """Check if the environment is production."""
        return self.ENVIRONMENT == EnvironmentType.PRODUCTION


class Settings(BaseSettings):
    """Settings class."""

    model_config = SettingsConfigDict(extra="ignore", env_file_encoding="utf-8", case_sensitive=True)

    ENV: EnvConfig
    GCP: GCPConfig
    SYSTEM: SystemConfig
    DB: DBConfig

    @classmethod
    def load(cls, config_path: str = "config/config.toml", env_file: str = ".env"):
        """Load the settings."""
        # Load TOML
        try:
            with open(config_path, "rb") as f:
                toml_data = tomli.load(f)
        except FileNotFoundError:
            with open("../" + config_path, "rb") as f:
                toml_data = tomli.load(f)

        # Load ENV
        env_config = EnvConfig(_env_file=env_file)

        return cls(
            ENV=env_config,
            GCP=GCPConfig(**toml_data["GCP"]),
            SYSTEM=SystemConfig(**toml_data["SYSTEM"]),
            DB=DBConfig(**toml_data["DB"]),
        )


_settings_instance = None


@lru_cache
def get_settings() -> Settings:  # noqa: D103
    global _settings_instance
    if _settings_instance is None:
        _settings_instance = Settings.load()
    return _settings_instance


async def aget_settings():
    """Dependency to retrieve application configuration settings.

    Returns:
        Settings: Application configuration settings from environment or defaults.
    """
    return get_settings()
