LOC_MAP = {
    "location_code": "LOC_CD",
    "location_name": "LOC_NM",
    "region_code": "RGN_CD",
    "yard_code": "MTY_PKUP_YD_CD",
    "latitude": "NEW_LOC_LAT",
    "longitude": "NEW_LOC_LON",
}

YARD_MAP = {
    "location_code": "LOC_CD",
    "yard_code": "YD_CD",
    "yard_name": "YD_NM",
    "yard_address": "YD_ADDR",
    "zip_code": "ZIP_CD",
    "yard_latitude": "YD_LAT",
    "yard_longitude": "YD_LON",
}


RATELANE_MAP = {
    "lane_description": "LANE_DESC",
    "origin_location_code": "ORG_LOC_CD",
    "origin_location_name": "ORG_LOC_NM",
    "origin_location_type": "ORG_LOC_TP",
    "origin_geo_hierarchy": "GEO_HRCHY_ORG",
    "origin_address_1": "ORG_ADDR1",
    "origin_city": "ORG_CTY",
    "origin_state": "ORG_STE",
    "origin_country": "ORG_CNT",
    "destination_location_code": "DES_LOC_CD",
    "destination_location_name": "DES_LOC_NM",
    "destination_location_type": "DES_LOC_TP",
    "destination_geo_hierarchy": "GEO_HRCHY_DES",
    "destination_address_1": "DES_ADDR1",
    "destination_city": "DES_CTY",
    "destination_state": "DES_STE",
    "destination_country": "DES_CNT",
    "via_point1": "VIA_PNT1",
    "base_rate": "BSE_RT",
    "currency_cd": "CURR_CD",
    "trip_type": "TRP_TP",
    "effective_date": "EFF_DT",
    "expiry_date": "EXP_DT",
    "commodity_class": "CMDT_CLSS",
    "is_hazmat": "IS_HAZMAT",
    "equipment_class": "EQ_CLSS",
    "equipment_size_type": "MAP_SIZE",
    "vendor_cd": "VNDR_CD",
    "carrier_cd": "CRR_CD",
}

RATELANE_MAPPING = {
    "port_cd": "PORT_CD",
    "import_loc_cd": "IMP_LOC_CD",
    "export_loc_cd": "XPT_LOC_CD",
    "import_base_rate": "IMP_BSE_RT",
    "export_base_rate": "XPT_BSE_RT",
    "import_trip_type": "IMP_TRP_TP",
    "export_trip_type": "XPT_TRP_TP",
    "import_vendor": "IMP_VNDR",
    "export_vendor": "XPT_VNDR",
    "import_lane_description": "IMP_LANE_DESC",
    "export_lane_description": "XPT_LANE_DESC",
    "import_carrier_cd": "IMP_CRR_CD",
    "export_carrier_cd": "XPT_CRR_CD",
}

REPORT_MAP = {
    "bound": "BND",
    "reuse": "RUSE",
    "shipping_line": "SHP_LINE",
    "truck_company_sequence": "TRUK_CO_SEQ",
    "truck_company": "TRUK_CO",
    "cop_no": "COP_NO",
    "booking_no": "BKG_NO",
    "container_no": "CNTR_NO",
    "shipper__consignee_name": "SHPR_CCNE_NM",
    "container_qty": "CNTR_QTY",
    "container_size_type": "CNTR_TPSZ",
    "location_cd": "LOC_CD",
    "location_city": "LOC_CTY",
    "location_state": "LOC_STE",
    "location_zip": "LOC_ZIP",
    "location_country": "LOC_CNT",
    "interchange_location": "ITCHG_LOC",
    "interchange_location_city": "ITCHG_LOC_CTY",
    "transport_mode": "TRSP_MOD",
    "import_availability_at_final_cy": "IMP_AVAL_AT_FNL_CY",
    "estimated_import_delivery_date": "ESTM_IMP_DEL_DT",
    "export_first_receiving_date": "XPT_N1ST_RCV_DT",
    "export_cut_off_date": "XPT_COFF_DT",
    "hazmat": "HAZMAT",
    "container_tare_weight_lbs": "CNTR_TARE_WGT_LBS",
    "estimate_cargo_weight_lbs": "ESTM_CGO_WGT_LBS",
    "flex_height": "FLEX_HGT",
    "control_office": "CTRL_OFC",
    "so_creator": "SO_CRER",
    "so_create_date": "SO_CRER_DT",
    "service_order_no": "SO_NO",
    "work_order_no": "WO_NO",
    "first_port_of_load_location": "N1ST_POL_LOC",
    "first_port_of_load_cutoff_date": "N1ST_POL_COFF_DT",
    "last_port_of_discharge_location": "LST_POD_LOC",
    "drop_and_pick": "DRP_AND_PK",
    "foc_cleared_status": "FOC_CLR_STS",
    "customer_nominated_trucker": "CUST_NOMI_TRUK",
    "commodity": "CMDT",
    "trucking_company_scac": "TRUK_CO_SCAC",
    "pol_pod": "POL_POD",
    "inland_transport_mode": "INLND_TRSP_MOD",
    "rail_scac": "RAIL_SCAC",
    "door_cy": "DOR_CY",
    "stcc_code": "STCC_CD",
    "street_address": "ST_ADDR",
}

REPORT_PREPROCESSING_MAP = {
    "imp_bkg_no": "IMP_BKG_NO",
    "imp_cop_no": "IMP_COP_NO",
    "imp_cntr_no": "IMP_CNTR_NO",
    "imp_cntr_tpsz_cd": "IMP_CNTR_TPSZ_CD",
    "imp_availability_at_final_cy": "IMP_AVAL_AT_FNL_CY",
    "imp_estimated_delivery_date": "IMP_ESTM_IMP_DEL_DT",
    "imp_st_addr": "IMP_ST_ADDR",
    "imp_loc_city": "IMP_LOC_CTY",
    "imp_loc_cd": "IMP_LOC_CD",
    "xpt_cop_no": "XPT_COP_NO",
    "xpt_bkg_no": "XPT_BKG_NO",
    "xpt_cntr_no": "XPT_CNTR_NO",
    "xpt_cntr_tpsz_cd": "XPT_CNTR_TPSZ_CD",
    "xpt_first_receiving_date": "XPT_N1ST_RCV_DT",
    "xpt_cut_off_date": "XPT_COFF_DT",
    "xpt_st_addr": "XPT_ST_ADDR",
    "xpt_loc_city": "XPT_LOC_CTY",
    "xpt_loc_cd": "XPT_LOC_CD",
    "time_gap_in_hour": "TIME_GAP_IN_HOUR",
}

STREET_TURN_MAPPING = {
    "port_cd": "PORT_CD",
    "import_loc_cd": "IMP_LOC_CD",
    "export_loc_cd": "XPT_LOC_CD",
    "import_street_address": "IMP_ST_ADDR",
    "export_street_address": "XPT_ST_ADDR",
    "distance_port_import": "DIST_PORT_IMP",
    "distance_port_export": "DIST_PORT_XPT",
    "distance_door_door": "DIST_DOR_DOR",
    "distance_saved": "DIST_SAV",
    "max_cost_saved": "MAX_COST_SAV",
    "created_at": "CRE_AT",
    "updated_at": "UPD_AT",
    "deleted_at": "DELT_AT",
}


MATCHING_RESULTS_SCHEMA = [
    {"name": "MATCH_ID", "type": "STRING"},
    {"name": "TRIGGER_TIMESTAMP", "type": "TIMESTAMP"},
    {"name": "MAXIMUM_DISTANCE_KM", "type": "FLOAT"},
    {"name": "TIME_TOLERANCE", "type": "FLOAT"},
    {"name": "PORT_CD", "type": "STRING"},
    {"name": "IMP_COP_NO", "type": "STRING"},
    {"name": "IMP_BKG_NO", "type": "STRING"},
    {"name": "IMP_CNTR_NO", "type": "STRING"},
    {"name": "XPT_COP_NO", "type": "STRING"},
    {"name": "XPT_BKG_NO", "type": "STRING"},
    {"name": "IMP_CNTR_TPSZ_CD", "type": "STRING"},
    {"name": "XPT_CNTR_TPSZ_CD", "type": "STRING"},
    {"name": "IMP_LOC_CD", "type": "STRING"},
    {"name": "XPT_LOC_CD", "type": "STRING"},
    {"name": "IMP_LOC_CITY", "type": "STRING"},
    {"name": "XPT_LOC_CITY", "type": "STRING"},
    {"name": "IMP_AVAILABILITY_AT_FINAL_CY", "type": "TIMESTAMP"},
    {"name": "IMP_ESTIMATED_DELIVERY_DATE", "type": "TIMESTAMP"},
    {"name": "XPT_FIRST_RECEIVING_DATE", "type": "TIMESTAMP"},
    {"name": "XPT_CUT_OFF_DATE", "type": "TIMESTAMP"},
    {"name": "TIME_GAP_IN_HOUR", "type": "FLOAT"},
    {"name": "IMP_ST_ADDR", "type": "STRING"},
    {"name": "XPT_ST_ADDR", "type": "STRING"},
    {"name": "IMP_TO_XPT_DIST_KM", "type": "FLOAT"},
    {"name": "STREET_TURN_ROUTE_DIST_KM", "type": "FLOAT"},
    {"name": "ROUND_TRIP_ROUTE_DIST_KM", "type": "FLOAT"},
    {"name": "DIST_SAV_KM", "type": "FLOAT"},
    {"name": "STREET_TURN_VNDR", "type": "STRING"},
    {"name": "STREET_TURN_TOTAL_COST", "type": "FLOAT"},
    {"name": "ROUND_TRIP_IMP_VNDR", "type": "STRING"},
    {"name": "ROUND_TRIP_XPT_VNDR", "type": "STRING"},
    {"name": "ROUND_TRIP_TOTAL_COST", "type": "FLOAT"},
    {"name": "COST_SAV", "type": "FLOAT"},
    {"name": "OPTIMAL", "type": "BOOLEAN"},
]

CANDIDATE_PAIRS_SCHEMA = [
    {"name": "MATCH_ID", "type": "STRING"},
    {"name": "TRIGGER_TIMESTAMP", "type": "TIMESTAMP"},
    {"name": "IMP_COP_NO", "type": "STRING"},
    {"name": "IMP_BKG_NO", "type": "STRING"},
    {"name": "IMP_CNTR_NO", "type": "STRING"},
    {"name": "IMP_CNTR_TPSZ_CD", "type": "STRING"},
    {"name": "IMP_LOC_CD", "type": "STRING"},
    {"name": "XPT_COP_NO", "type": "STRING"},
    {"name": "XPT_BKG_NO", "type": "STRING"},
    {"name": "XPT_CNTR_TPSZ_CD", "type": "STRING"},
    {"name": "XPT_LOC_CD", "type": "STRING"},
    {"name": "PORT_CD", "type": "STRING"},
    {"name": "IMP_PORT_CITY", "type": "STRING"},
    {"name": "XPT_PORT_CITY", "type": "STRING"},
    {"name": "IMP_LOC_CITY", "type": "STRING"},
    {"name": "XPT_LOC_CITY", "type": "STRING"},
    {"name": "IMP_ST_ADDR", "type": "STRING"},
    {"name": "XPT_ST_ADDR", "type": "STRING"},
    {"name": "IMP_AVAILABILITY_AT_FINAL_CY", "type": "TIMESTAMP"},
    {"name": "IMP_ESTIMATED_DELIVERY_DATE", "type": "TIMESTAMP"},
    {"name": "XPT_FIRST_RECEIVING_DATE", "type": "TIMESTAMP"},
    {"name": "XPT_CUT_OFF_DATE", "type": "TIMESTAMP"},
    {"name": "TIME_GAP_IN_HOUR", "type": "FLOAT"},
    {"name": "IMP_TO_PORT_DIST_KM", "type": "FLOAT"},
    {"name": "XPT_TO_PORT_DIST_KM", "type": "FLOAT"},
    {"name": "IMP_TO_XPT_DIST_KM", "type": "FLOAT"},
    {"name": "STREET_TURN_ROUTE_DIST_KM", "type": "FLOAT"},
    {"name": "ROUND_TRIP_ROUTE_DIST_KM", "type": "FLOAT"},
    {"name": "DIST_SAV_KM", "type": "FLOAT"},
    # {"name": "WEIGHT", "type": "FLOAT"},
]


DISTANCE_SCHEMA = [
    {"name": "ORIG_CD", "type": "STRING"},
    {"name": "ORIG_NM", "type": "STRING", "mode": "REQUIRED"},
    {"name": "DEST_CD", "type": "STRING"},
    {"name": "DEST_NM", "type": "STRING", "mode": "REQUIRED"},
    {"name": "DIST", "type": "FLOAT64", "mode": "REQUIRED"},
    {"name": "DUR", "type": "FLOAT64", "mode": "REQUIRED"},
    {"name": "ORIG_LAT", "type": "NUMERIC"},
    {"name": "ORIG_LON", "type": "NUMERIC"},
    {"name": "DEST_LAT", "type": "NUMERIC"},
    {"name": "DEST_LON", "type": "NUMERIC"},
    {"name": "DELT_FLG", "type": "STRING", "mode": "REQUIRED"},
    {"name": "CRE_DT", "type": "TIMESTAMP", "mode": "REQUIRED"},
    {"name": "UPD_DT", "type": "TIMESTAMP", "mode": "REQUIRED"},
]
