from pydantic import BaseModel, Field


class SystemConfigSchema(BaseModel):
    """Schema for updating system configuration."""

    NODES: tuple[str, ...] = Field(default=(), description="Specific container yard/port/terminal codes to process")
    DISTANCE_METHOD: str = Field(description="Method to calculate distances between locations")
    MAXIMUM_DISTANCE_THRES_KM: float = Field(description="Distance threshold in km for matching")
    CRITERIA: str = Field(description="Optimization criteria for matching")
    TIME_TOLERANCE_THRES: int = Field(description="Time gap in days between available date and cutoff date")
    DELIVERY_TYPE: str = Field(
        default="DOOR",
        description="Default delivery type. For now, only DOOR is supported",
    )
    HAZMAT: str = Field(default="N", description="Default hazmat match. For now, only N is supported")
    CUSTOMER_NOMINATED_TRUCKER: bool = Field(
        default=False, description="Flag to filter for NULL customer_nominated_trucker"
    )
    DROP_AND_PICK: str = Field(
        default="N",
        description="Default drop and pick match. For now, only N is supported",
    )


class SystemConfigUpdateResponse(BaseModel):
    """Response model for SystemConfig update."""

    success: bool
    message: str
    data: SystemConfigSchema
