from pydantic import BaseModel


class PubSubMessage(BaseModel):
    """Model for the Pub/Sub message data.

    Attributes:
        data: Base64-encoded message data
        attributes: Optional key-value pairs attached to the message
        message_id: Unique identifier for the message
        publish_time: Timestamp when message was published
    """

    data: str
    attributes: dict[str, str] | None = None
    message_id: str
    publish_time: str


class PubSubEnvelope(BaseModel):
    """Model for the complete Pub/Sub push message envelope.

    Attributes:
        message: The actual Pub/Sub message content
        subscription: Name of the subscription that received the message
    """

    message: PubSubMessage
    subscription: str
