from datetime import datetime
from enum import Enum
from typing import Any, Literal

from pydantic import BaseModel, Field, field_validator


class MatchPair(BaseModel):
    """Schema for a matched pair of import delivery and export pickup.

    Represents a successful match between an import delivery and export pickup,
    including the match details and calculated metrics.

    Attributes:
        import_reference: Reference ID of the import delivery
        export_reference: Reference ID of the export pickup
        container_id: ID of the container used for the match
        distance_km: Distance between delivery and pickup locations
        savings_estimate: Estimated cost savings for this match
        match_score: Quality score for the match (higher is better)
        delivery_date: Scheduled date for import delivery
        pickup_date: Scheduled date for export pickup
        time_window_days: Number of days between delivery and pickup
    """

    import_reference: str = Field(description="Reference ID of the import delivery")
    export_reference: str = Field(description="Reference ID of the export pickup")
    container_id: str = Field(description="Container ID used for the match")
    distance_km: float = Field(description="Distance between delivery and pickup in km")
    savings_estimate: float = Field(description="Estimated savings for this match")
    match_score: float = Field(description="Score indicating quality of match (higher is better)")
    delivery_date: datetime = Field(description="Scheduled delivery date")
    pickup_date: datetime = Field(description="Scheduled pickup date")
    time_window_days: int = Field(description="Days between delivery and pickup")


class MatchResponseStatus(str, Enum):
    """Enumeration of possible matching process statuses.

    Values:
        COMPLETED: Matching process completed successfully
        PARTIAL: Some matches were found but not all requests were matched
        FAILED: Matching process failed to complete
    """

    COMPLETED = "completed"
    PARTIAL = "partial"
    FAILED = "failed"


class MatchResponse(BaseModel):
    """Schema for the complete matching process response.

    Provides comprehensive results of a matching run, including successful matches,
    unmatched requests, and overall metrics.

    Attributes:
        match_id: Unique identifier for this matching result
        status: Status of the matching process
        timestamp: When the matching was performed
        matches: List of successful matches found
        unmatched_imports: List of import requests that couldn't be matched
        unmatched_exports: List of export requests that couldn't be matched
        total_savings: Total estimated cost savings across all matches
        total_distance_saved: Total distance saved in kilometers
        constraints_used: Configuration constraints used for this matching run
    """

    match_id: str = Field(description="Unique identifier for this matching result")
    status: MatchResponseStatus = Field(description="Status of the matching process")
    timestamp: datetime = Field(description="Time when matching was performed")
    matches: list[MatchPair] = Field(default_factory=list, description="List of successful matches")
    unmatched_imports: list[str] = Field(
        default_factory=list, description="Reference IDs of unmatched import deliveries"
    )
    unmatched_exports: list[str] = Field(default_factory=list, description="Reference IDs of unmatched export pickups")
    total_savings: float = Field(description="Total estimated savings across all matches")
    total_distance_saved: float = Field(description="Total distance saved in km")
    constraints_used: dict[str, Any] = Field(description="Constraints used for this matching run")


class ReoptimizeRequest(BaseModel):
    """Schema for match reoptimization request.

    Defines parameters for reoptimizing a previous match with new constraints.

    Attributes:
        maximum_distance_thres_km: Maximum allowed distance between locations
        time_tolerance_thres: Maximum allowed days between delivery and pickup
        cop_list: Optional list of company codes to filter by
        node_cd: Optional node code to filter by
        street_turn_cost_option: Cost savings formula selection (must be FORMULA_2 or FORMULA_4)
        ranking_priority_options: Priority order for ranking matches (array from D11-D14)
    """

    maximum_distance_thres_km: float = Field(
        description="Maximum allowed distance between delivery and pickup locations in km (must be >= 0)", ge=0.0
    )
    time_tolerance_thres: int = Field(description="Maximum allowed days between import availability and export pickup")
    cop_list: list[str] = Field([], description="Optional list of company codes to filter matching by")
    node_cd: str = Field(description="Optional node code to filter matching by")
    street_turn_cost_option: Literal["FORMULA_2", "FORMULA_4"] = Field(
        description="Cost savings formula selection for street turn costs (must be FORMULA_2 or FORMULA_4)",
    )
    ranking_priority_options: list[str] = Field(
        description="Priority order for ranking matches (valid values: street_turn_total_cost, cost_save, dist_sav_km, time_gap)"
    )

    @field_validator("ranking_priority_options")
    @classmethod
    def validate_ranking_priority_options(cls, v):
        """Validate that ranking priority items are from the allowed list."""
        allowed_values = {"street_turn_total_cost", "cost_save", "dist_sav_km", "time_gap"}
        for item in v:
            if item not in allowed_values:
                raise ValueError(f"Invalid ranking priority item: {item}. Must be one of: {', '.join(allowed_values)}")
        return v

    class Config:
        """Configuration for Pydantic model."""

        json_schema_extra = {
            "example": {
                "maximum_distance_thres_km": 643.736,
                "time_tolerance_thres": 0,
                "cop_list": ["CRIC5423798553", "CTYO5311760678"],
                "node_cd": "USATL63",
                "street_turn_cost_option": "FORMULA_2",
                "ranking_priority_options": [
                    "street_turn_total_cost",
                    "cost_save",
                    "dist_sav_km",
                    "time_gap",
                ],
            }
        }


class ReoptimizeRequestV2(BaseModel):
    """Schema for match reoptimization request.

    Defines parameters for reoptimizing a previous match with new constraints.

    Attributes:
        maximum_distance_thres_km: Maximum allowed distance between locations
        time_tolerance_thres: Maximum allowed days between delivery and pickup
        cop_list: Optional list of company codes to filter by
        node_cd: Optional node code to filter by
        street_turn_cost_option: Cost savings formula selection (must be FORMULA_2 or FORMULA_4)
        primary_ranking_options: Primary ranking option for matches (single value from D11)
    """

    maximum_distance_thres_km: float = Field(
        description="Maximum allowed distance between delivery and pickup locations in km (must be >= 0)", ge=0.0
    )
    time_tolerance_thres: int = Field(description="Maximum allowed days between import availability and export pickup")
    cop_list: list[str] = Field([], description="Optional list of company codes to filter matching by")
    node_cd: str = Field(description="Optional node code to filter matching by")
    street_turn_cost_option: Literal["FORMULA_2", "FORMULA_4"] = Field(
        description="Cost savings formula selection for street turn costs (must be FORMULA_2 or FORMULA_4)",
    )
    primary_ranking_options: Literal["street_turn_total_cost", "cost_save", "dist_sav_km", "time_gap"] = Field(
        description="Primary ranking option for matches (must be one of: street_turn_total_cost, cost_save, dist_sav_km, time_gap)"
    )

    class Config:
        """Configuration for Pydantic model."""

        json_schema_extra = {
            "example": {
                "maximum_distance_thres_km": 643.736,
                "time_tolerance_thres": 0,
                "cop_list": ["CRIC5423798553", "CTYO5311760678"],
                "node_cd": "USATL63",
                "street_turn_cost_option": "FORMULA_2",
                "primary_ranking_options": "street_turn_total_cost",
            }
        }


class ReoloadRequest(BaseModel):
    """Schema for match reload request."""

    node_cd: str = Field(description="Optional node code to filter matching by")

    class Config:
        """Configuration for Pydantic model."""

        json_schema_extra = {
            "example": {
                "node_cd": "USATL63",
            }
        }
