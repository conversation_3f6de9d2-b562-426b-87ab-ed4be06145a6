from contextlib import asynccontextmanager
from typing import Annotated

from fastapi import Depends, FastAPI

from src.services import ArtifactService, DataService, StreetTurnMatchingService
from src.services.distance_service import download_distance_cache
from src.thirdparty.gcp import <PERSON><PERSON><PERSON>yManager, GCSManager, GMapsManager, GoogleDriveManager, GoogleSheetsManager
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


class ServiceNotInitializedError(Exception):
    """Raised when trying to access a service that hasn't been initialized."""

    def __init__(self, service_name: str):
        """Initialize the exception with a service name.

        Args:
            service_name: Name of the service that was not initialized
        """
        self.message = f"{service_name} has not been initialized"
        super().__init__(self.message)


class Services:
    """Container for application services."""

    def __init__(self):
        """Initialize the services container with all services set to None."""
        self.bigquery_manager: BigQueryManager | None = None
        self.gmaps_manager: GMapsManager | None = None
        self.gcs_manager: GCSManager | None = None
        self.drive_manager: GoogleDriveManager | None = None
        self.sheets_manager: GoogleSheetsManager | None = None
        self.distance_cache: dict = {}


services = Services()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize services on startup and cleanup on shutdown."""
    # Initialize services
    services.bigquery_manager = BigQueryManager()
    services.gmaps_manager = GMapsManager()
    services.gcs_manager = GCSManager()
    services.drive_manager = GoogleDriveManager()
    services.sheets_manager = GoogleSheetsManager()

    # Load distance cache
    services.distance_cache = await download_distance_cache()
    logger.info("Distance cache loaded")

    yield

    # Cleanup on shutdown
    services.bigquery_manager = None
    services.gmaps_manager = None
    services.gcs_manager = None
    services.drive_manager = None
    services.sheets_manager = None
    services.distance_cache = {}


# Service dependencies
async def get_bigquery() -> BigQueryManager:
    """Get BigQuery manager instance."""
    if services.bigquery_manager is None:
        raise ServiceNotInitializedError("BigQuery Manager")
    return services.bigquery_manager


async def get_gmaps() -> GMapsManager:
    """Get Google Maps manager instance."""
    if services.gmaps_manager is None:
        raise ServiceNotInitializedError("Google Maps Manager")
    return services.gmaps_manager


async def get_gcs() -> GCSManager:
    """Get GCS manager instance."""
    if services.gcs_manager is None:
        raise ServiceNotInitializedError("GCS Manager")
    return services.gcs_manager


async def get_drive() -> GoogleDriveManager:
    """Get Google Drive manager instance."""
    if services.drive_manager is None:
        raise ServiceNotInitializedError("Google Drive Manager")
    return services.drive_manager


async def get_sheets() -> GoogleSheetsManager:
    """Get Google Sheets manager instance."""
    if services.sheets_manager is None:
        raise ServiceNotInitializedError("Google Sheets Manager")
    return services.sheets_manager


# Business service dependencies
async def get_matching_service(bq: Annotated[BigQueryManager, Depends(get_bigquery)]) -> StreetTurnMatchingService:
    """Get the matching service instance."""
    return StreetTurnMatchingService(bq)


async def get_data_service(bq: Annotated[BigQueryManager, Depends(get_bigquery)]) -> DataService:
    """Get the data service instance."""
    return DataService(bq)


async def get_artifact_service(
    bq: Annotated[BigQueryManager, Depends(get_bigquery)],
    gcs: Annotated[GCSManager, Depends(get_gcs)],
    drive: Annotated[GoogleDriveManager, Depends(get_drive)],
    sheets: Annotated[GoogleSheetsManager, Depends(get_sheets)],
) -> ArtifactService:
    """Get the artifact service instance."""
    return ArtifactService(bq, gcs, drive, sheets)
