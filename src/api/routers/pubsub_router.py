import datetime as dt

from fastapi import APIRouter, Depends, HTTPException, status

from src.api.dependencies import get_artifact_service, get_bigquery, get_data_service, get_matching_service
from src.api.schemas.pub_sub_schemas import PubSubEnvelope
from src.const import aget_settings
from src.services import StreetTurnMatchingService  # StreetTurnDBUpdater
from src.utils.decorators import measure_time
from src.utils.logger import log_handler
from src.utils.matching_utils import MatchingSettings, execute_matching_process

router = APIRouter()
logger = log_handler.get_logger(name=__name__)


@router.post("/exec-code")
@measure_time(logger=logger)
async def exec_code(
    envelope: PubSubEnvelope,
    bq_manager=Depends(get_bigquery),  # noqa: B008
    matching_service: StreetTurnMatchingService = Depends(get_matching_service),  # noqa: B008
    data_service=Depends(get_data_service),  # noqa: B008
    artifact_service=Depends(get_artifact_service),  # noqa: B008
    settings=Depends(aget_settings),  # noqa: B008
):
    """Handle incoming Google Cloud Pub/Sub messages via HTTP push subscription.

    This endpoint processes Pub/Sub messages to trigger automated street-turn matching.
    It fetches necessary data, performs matching, enriches the results, and writes them to Google Sheets.

    Args:
        envelope: The Pub/Sub message envelope containing the message and subscription details.
        bq_manager: Dependency-injected BigQuery manager for database operations.
        matching_service: Dependency-injected service for performing street-turn matching.
        data_service: Dependency-injected service for fetching required data.
        artifact_service: Dependency-injected service for writing results to Google Sheets.
        settings: Dependency-injected configuration settings.

    Returns:
        A JSON response indicating success or failure of the matching process.
    """
    # Current timestamp in UTC+8
    run_timestamp = dt.datetime.now(dt.timezone(dt.timedelta(hours=8)))

    logger.info(f"Received Pub/Sub message for matching process at {run_timestamp}")
    logger.info(f"Processing for message ID = {envelope.message.message_id} at {envelope.message.publish_time}")
    logger.info(f"Received message: {envelope.message.data}")
    logger.info(f"Received attributes: {envelope.message.attributes}")
    logger.info(f"Received subscription: {envelope.subscription}")

    try:
        # Decode the message data
        # data_decoded = base64.b64decode(envelope.message.data).decode("utf-8").strip()

        # Initialize update services with required parameters
        # rate_lane_updater = RateLaneUpdater(
        #     bq_manager=bq_manager, trigger_dt=run_timestamp, node_cds=settings.SYSTEM.NODES
        # )
        # logger.info("Starting rate lane mapping update")
        # rate_lane_updater.update_rate_lane_table(settings.SYSTEM.NODES)
        # logger.info("Rate lane mapping update completed")

        # street_turn_updater = StreetTurnDBUpdater(
        #     bq_manager=bq_manager,
        #     trigger_dt=run_timestamp,
        #     door_cy_value=settings.SYSTEM.DELIVERY_TYPE,
        #     hazmat=settings.SYSTEM.HAZMAT,
        #     customer_nominated_trucker=settings.SYSTEM.CUSTOMER_NOMINATED_TRUCKER,
        #     drop_and_pick=settings.SYSTEM.DROP_AND_PICK,
        #     node_cd=settings.SYSTEM.NODES,
        # )
        # logger.info("Starting street turn database update")
        # street_turn_updater.update_street_turn_db(node_codes=settings.SYSTEM.NODES)
        # logger.info("Street turn database update completed")

        run_settings = MatchingSettings(
            trigger_timestamp=run_timestamp,
            nodes=settings.SYSTEM.NODES,
            maximum_distance_thres_km=settings.SYSTEM.MAXIMUM_DISTANCE_THRES_KM,
            time_tolerance_thres=settings.SYSTEM.TIME_TOLERANCE_THRES,
            ranking_priorities=settings.SYSTEM.RANKING_PRIORITIES,
            street_turn_cost_options=settings.SYSTEM.STREET_TURN_COST_OPTIONS,
        )
        return await execute_matching_process(
            matching_service=matching_service,
            data_service=data_service,
            artifact_service=artifact_service,
            settings=run_settings,
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform matching: {str(e)}",
        ) from e
