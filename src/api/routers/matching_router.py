import datetime as dt

from fastapi import APIRouter, Depends, HTTPException, status

from src.api.dependencies import get_artifact_service, get_data_service, get_matching_service
from src.api.schemas.matching_schemas import ReoloadRequest, ReoptimizeRequest, ReoptimizeRequestV2
from src.const import aget_settings
from src.const.const import RANKING_COFF_MAPPING, RANKING_PRIORITY_LST
from src.services import StreetTurnMatchingService
from src.thirdparty.gcp import BigQueryManager
from src.utils.logger import log_handler
from src.utils.matching_utils import MatchingSettings, execute_matching_process
from src.utils.matching_utils_v2 import MatchingSettingsV2, execute_matching_process_v2

from .auth import get_api_key

logger = log_handler.get_logger(name=__name__)

router = APIRouter()


@router.post("/match", status_code=status.HTTP_201_CREATED)
async def create_match(
    api_key: str = Depends(get_api_key),
    matching_service: StreetTurnMatchingService = Depends(get_matching_service),  # noqa: B008
    data_service=Depends(get_data_service),  # noqa: B008
    artifact_service=Depends(get_artifact_service),  # noqa: B008
    settings=Depends(aget_settings),  # noqa: B008,
):
    """Perform street-turn matching between import deliveries and export pickups.

    This endpoint performs the matching process by:
    - Setting up a temporary result folder for the run.
    - Fetching all necessary data for matching.
    - Creating all possible matches based on the provided data.
    - Filtering matches based on distance and time thresholds.
    - Enriching matches with additional route information.
    - Exporting the results to Google Sheets.

    Args:
        api_key (str): API key for authentication.
        matching_service (StreetTurnMatchingService): Service for performing the matching.
        data_service: Service for fetching data required for matching.
        artifact_service: Service for handling artifacts like Google Sheets.
        settings: Application configuration settings.

    Returns:
        dict: A success message with the match ID if the operation is successful.

    Raises:
        HTTPException: If an error occurs during the matching process.
    """
    # Current timestamp in UTC+8
    run_timestamp = dt.datetime.now(dt.timezone(dt.timedelta(hours=8)))

    logger.info(f"Received matching request at {run_timestamp}")

    try:
        run_settings = MatchingSettings(
            trigger_timestamp=run_timestamp,
            nodes=settings.SYSTEM.NODES,
            maximum_distance_thres_km=settings.SYSTEM.MAXIMUM_DISTANCE_THRES_KM,
            time_tolerance_thres=settings.SYSTEM.TIME_TOLERANCE_THRES,
            ranking_priorities=settings.SYSTEM.RANKING_PRIORITIES,
            street_turn_cost_options=settings.SYSTEM.STREET_TURN_COST_OPTIONS,
        )
        return await execute_matching_process(
            matching_service=matching_service,
            data_service=data_service,
            artifact_service=artifact_service,
            settings=run_settings,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform matching: {str(e)}",
        ) from e


@router.post("/reoptimize", status_code=status.HTTP_200_OK)
async def reoptimize_match(
    request: ReoptimizeRequest,
    api_key: str = Depends(get_api_key),
    matching_service: StreetTurnMatchingService = Depends(get_matching_service),  # noqa: B008
    data_service=Depends(get_data_service),  # noqa: B008
    artifact_service=Depends(get_artifact_service),  # noqa: B008
    settings=Depends(aget_settings),  # noqa: B008,
):
    """Reoptimize a previous match with new constraints."""
    run_timestamp = dt.datetime.now(dt.timezone(dt.timedelta(hours=8)))

    logger.info(f"Received reoptimization request at {run_timestamp}")
    logger.info(f"Reoptimization request details: {request}")

    try:
        logger.info("Reoptimizing match with new constraints")

        # Query candidate pairs from BigQuery
        bq_manager = BigQueryManager()

        query = """
        SELECT *
        FROM `{table_id}_{port_cd}`
        WHERE IMP_COP_NO NOT IN ('{cop_list}')
        AND XPT_COP_NO NOT IN ('{cop_list}')
        -- AND DISTANCE_KM <= {maximum_distance_thres_km}
        -- AND TIME_TOLERANCE_DAYS <= {time_tolerance_thres}
        """
        table_id = (
            f"{settings.ENV.PROJECT_ID}.{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.POSSIBLE_CANDIDATE_PAIRS_TABLE_ID}"
        )
        query = query.format(
            table_id=table_id,
            port_cd=request.node_cd[:5],  # Ensure port_cd is 5 characters for location level
            cop_list="','".join(request.cop_list) if request.cop_list else "",
            maximum_distance_thres_km=request.maximum_distance_thres_km,
            time_tolerance_thres=request.time_tolerance_thres,
        )
        all_pairs = await bq_manager.execute_query(query)

        if all_pairs.empty:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No candidate pairs found",
            )

        # Convert columns back to lowercase to match existing code
        all_pairs.columns = all_pairs.columns.str.lower()

        run_settings = MatchingSettings(
            trigger_timestamp=run_timestamp,
            nodes=[request.node_cd],
            maximum_distance_thres_km=request.maximum_distance_thres_km,
            time_tolerance_thres=request.time_tolerance_thres,
            ranking_priorities=[
                RANKING_PRIORITY_LST[name] for name in request.ranking_priority_options if name in RANKING_PRIORITY_LST
            ],
            street_turn_cost_options=[request.street_turn_cost_option],
        )
        return await execute_matching_process(
            matching_service=matching_service,
            data_service=data_service,
            artifact_service=artifact_service,
            settings=run_settings,
            filtered_pairs=all_pairs,
            filtered_node=request.node_cd,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reoptimize matching: {str(e)}",
        ) from e


@router.post("/v2/reoptimize", status_code=status.HTTP_200_OK)
async def reoptimize_match_v2(
    request: ReoptimizeRequestV2,
    api_key: str = Depends(get_api_key),
    matching_service: StreetTurnMatchingService = Depends(get_matching_service),  # noqa: B008
    data_service=Depends(get_data_service),  # noqa: B008
    artifact_service=Depends(get_artifact_service),  # noqa: B008
    settings=Depends(aget_settings),  # noqa: B008,
):
    """Reoptimize a previous match with new constraints."""
    run_timestamp = dt.datetime.now(dt.timezone(dt.timedelta(hours=8)))

    logger.info(f"Received reoptimization request at {run_timestamp}")
    logger.info(f"Reoptimization request details: {request}")

    try:
        logger.info("Reoptimizing match with new constraints")

        # Query candidate pairs from BigQuery
        bq_manager = BigQueryManager()

        query = """
        SELECT *
        FROM `{table_id}_{port_cd}`
        WHERE IMP_COP_NO NOT IN ('{cop_list}')
        AND XPT_COP_NO NOT IN ('{cop_list}')
        -- AND DISTANCE_KM <= {maximum_distance_thres_km}
        -- AND TIME_TOLERANCE_DAYS <= {time_tolerance_thres}
        """
        table_id = (
            f"{settings.ENV.PROJECT_ID}.{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.POSSIBLE_CANDIDATE_PAIRS_TABLE_ID}"
        )
        query = query.format(
            table_id=table_id,
            port_cd=request.node_cd[:5],  # Ensure port_cd is 5 characters for location level
            cop_list="','".join(request.cop_list) if request.cop_list else "",
            maximum_distance_thres_km=request.maximum_distance_thres_km,
            time_tolerance_thres=request.time_tolerance_thres,
        )
        all_pairs = await bq_manager.execute_query(query)

        if all_pairs.empty:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No candidate pairs found",
            )

        # Convert columns back to lowercase to match existing code
        all_pairs.columns = all_pairs.columns.str.lower()

        run_settings = MatchingSettingsV2(
            trigger_timestamp=run_timestamp,
            nodes=[request.node_cd],
            maximum_distance_thres_km=request.maximum_distance_thres_km,
            time_tolerance_thres=request.time_tolerance_thres,
            ranking_coff=RANKING_COFF_MAPPING[request.primary_ranking_options],
            street_turn_cost_options=[request.street_turn_cost_option],
        )
        return await execute_matching_process_v2(
            matching_service=matching_service,
            data_service=data_service,
            artifact_service=artifact_service,
            settings=run_settings,
            filtered_pairs=all_pairs,
            filtered_node=request.node_cd,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reoptimize matching: {str(e)}",
        ) from e


@router.post("/reload", status_code=status.HTTP_200_OK)
async def reload_result(
    request: ReoloadRequest,
    api_key: str = Depends(get_api_key),
    artifact_service=Depends(get_artifact_service),  # noqa: B008
    settings=Depends(aget_settings),  # noqa: B008,
):
    """Reload the result of a previous match."""
    run_timestamp = dt.datetime.now(dt.timezone(dt.timedelta(hours=8)))

    logger.info(f"Received reload request at {run_timestamp}")
    logger.info(f"Reload request details: {request}")

    try:
        logger.info("Reload match with new constraints")

        # Query candidate pairs from BigQuery
        bq_manager = BigQueryManager()

        query = """
            SELECT *
            FROM `{table_id}`
            WHERE PORT_CD = '{port_cd}'
        """
        table_id = (
            f"{settings.ENV.PROJECT_ID}.{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.MATCHED_CANDIDATE_PAIRS_TABLE_ID}"
        )
        query = query.format(table_id=table_id, port_cd=request.node_cd)
        result = await bq_manager.execute_query(query)

        if result.empty:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No candidate pairs found")

        # Convert columns back to lowercase to match existing code
        result.columns = result.columns.str.lower()
        column_mapping = {
            "imp_cop_no": "import_cop_no",
            "imp_bkg_no": "import_bkg_no",
            "imp_cntr_no": "import_container_no",
            "xpt_cop_no": "export_cop_no",
            "xpt_bkg_no": "export_bkg_no",
            "imp_cntr_tpsz_cd": "import_container_type_size_code",
            "xpt_cntr_tpsz_cd": "export_container_type_size_code",
            "imp_loc_cd": "import_location_code",
            "xpt_loc_cd": "export_location_code",
            "imp_loc_city": "import_location_city",
            "xpt_loc_city": "export_location_city",
            "imp_availability_at_final_cy": "import_availability_at_final_cy",
            "imp_estimated_delivery_date": "import_estimated_delivery_date",
            "xpt_first_receiving_date": "export_first_receiving_date",
            "xpt_cut_off_date": "export_cut_off_date",
            "time_gap_in_hour": "time_gap_in_hours",
            "imp_st_addr": "import_street_address",
            "xpt_st_addr": "export_street_address",
            "imp_to_xpt_dist_km": "import_to_export_distance_km",
            "street_turn_route_dist_km": "street_turn_route_distance_km",
            "round_trip_route_dist_km": "round_trip_route_distance_km",
            "dist_sav_km": "distance_saved_km",
            "street_turn_vendor": "street_turn_vendor",
            "street_turn_total_cost": "street_turn_total_cost",
            "round_trip_imp_vndr": "round_trip_import_vendor",
            "round_trip_xpt_vndr": "round_trip_export_vendor",
            "round_trip_total_cost": "round_trip_total_cost",
            "cost_sav": "cost_saved",
            "optimal": "is_optimal",
        }
        result = result.rename(columns=column_mapping)

        query = """
            SELECT
                COUNT(DISTINCT IMP_COP_NO) AS COUNT_IMPS,
                COUNT(DISTINCT XPT_COP_NO) AS COUNT_XPTS,
                COUNT(*) AS COUNT_POSSIBLE_PAIRS
            FROM `{table_id}_{port_cd}`
        """
        table_id = (
            f"{settings.ENV.PROJECT_ID}.{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.POSSIBLE_CANDIDATE_PAIRS_TABLE_ID}"
        )
        query = query.format(
            table_id=table_id, port_cd=request.node_cd[:5]
        )  # Ensure port_cd is 5 characters for location level)
        meta_result = await bq_manager.execute_query(query)

        metadata = {
            "Max distance threshold (km)": result["maximum_distance_km"].iloc[0],
            "Time tolerance threshold (days)": result["time_tolerance"].iloc[0],
            "Total cost saved (USD)": result[result["is_optimal"]]["cost_saved"].sum(),
            "Total distance saved (km)": result[result["is_optimal"]]["distance_saved_km"].sum(),
            "Container reuse": result["import_container_no"].nunique(),
            "Number of import": meta_result["COUNT_IMPS"],
            "Number of export": meta_result["COUNT_XPTS"],
            "Number of possible street-turns": meta_result["COUNT_POSSIBLE_PAIRS"],
            # NOTE: for internal use only, this was the number of result return by linear programming
            # "Number of selected street-turns": result["import_container_no"].nunique(),
        }

        result = result.drop(
            columns=["match_id", "trigger_timestamp", "maximum_distance_km", "time_tolerance"], errors="ignore"
        )
        # convert time_gap_in_hour more visually appealing
        result["time_gap_in_hours"] = result["time_gap_in_hours"].apply(
            lambda x: f"{int(x // 24)} days {int(x % 24)} hours" if abs(x) >= 24 else f"{int(x)} hours"
        )
        result = result.rename(columns={"time_gap_in_hours": "time_gap"})

        artifact_service.write_matches(settings.GCP.GSHEETS.SPREADSHEET_ID, result, metadata)
        return {"status": "success", "message": "Results reloaded successfully"}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reoptimize matching: {str(e)}",
        ) from e
