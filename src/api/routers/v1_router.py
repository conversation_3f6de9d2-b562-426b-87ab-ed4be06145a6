from fastapi import APIRouter, Depends, HTTPException

from src.api.schemas.system_config import SystemConfigSchema, SystemConfigUpdateResponse
from src.const import get_settings
from src.const.config import SystemConfig
from src.utils.logger import log_handler

from .auth import get_api_key

logger = log_handler.get_logger(name=__name__)

router = APIRouter()


@router.get("/")
async def api_root(api_key: str = Depends(get_api_key)):
    """API root endpoint that provides basic information about the API.

    Returns:
        dict: A message indicating these are the API routes.
    """
    return {"message": "Hello from the API"}


@router.get("/health")
async def health(api_key: str = Depends(get_api_key)):
    """Health check endpoint to verify the API is running properly.

    Returns:
        dict: Status indicating the API is operational.
    """
    return {"status": "OK"}


@router.get("/system-config", response_model=SystemConfigSchema, tags=["config"])
async def get_system_config(api_key: str = Depends(get_api_key)):
    """Get current system configuration.

    Returns:
        SystemConfigSchema: The current system configuration.
    """
    settings = get_settings()
    system_config = settings.SYSTEM
    return SystemConfigSchema(**system_config.model_dump())


@router.put("/system-config", response_model=SystemConfigUpdateResponse, tags=["config"])
async def update_system_config(config: SystemConfigSchema, api_key: str = Depends(get_api_key)):
    """Update system configuration.

    Args:
        api_key (str): API key for authentication.
        config (SystemConfigSchema): The updated system configuration.

    Returns:
        SystemConfigUpdateResponse: Response indicating success or failure.
    """
    try:
        settings = get_settings()

        # Update the SystemConfig with new values
        system_config = SystemConfig(**config.model_dump())

        # Update settings.SYSTEM with the new config
        for key, value in system_config.model_dump().items():
            setattr(settings.SYSTEM, key, value)

        logger.info("System configuration updated successfully")

        return SystemConfigUpdateResponse(
            success=True,
            message="System configuration updated successfully",
            data=SystemConfigSchema(**settings.SYSTEM.model_dump()),
        )
    except Exception as e:
        logger.error(f"Error updating system configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update system configuration: {str(e)}") from e
