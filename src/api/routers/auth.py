from fastapi import HTTPException, Security, status
from fastapi.security import <PERSON><PERSON>ey<PERSON>eader

from src.const import get_settings

settings = get_settings()

API_KEY_NAME = "X-API-Key"
api_key_header = APIKeyHeader(name=API_KEY_NAME)


async def get_api_key(api_key_header: str = Security(api_key_header)):
    """Authenticate API key."""
    if api_key_header != settings.ENV.API_KEY:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid API Key")
    return api_key_header
