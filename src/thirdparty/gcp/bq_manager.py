import asyncio
import base64
import json
import time
from typing import Any

import google.cloud.bigquery as bigquery
import pandas as pd
from google.oauth2 import service_account

from src.const import get_settings
from src.utils.exceptions import AuthenticationError, BigQueryError
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class BigQueryManager:
    """A class to manage interactions with Google BigQuery."""

    RETRY_ATTEMPTS = 3
    RETRY_DELAY = 1  # seconds

    def __init__(self) -> None:
        """Initialize the BigQueryInteraction class."""
        self.client = self.__authenticate_service_account()
        logger.info(f"{self.__class__.__name__} initialized for project: {settings.ENV.PROJECT_ID}")

    def __load_service_account(self) -> dict[str, Any]:
        """Load the service account info from the provided file."""
        try:
            __base64_str = settings.ENV.SERVICE_ACCOUNT_ENCODE

            __decoded_bytes = base64.b64decode(__base64_str.get_secret_value())
            creds_dict = json.loads(__decoded_bytes.decode("utf-8"))
            return creds_dict
        except json.JSONDecodeError as e:
            error_msg = f"{self.__class__.__name__}: Invalid JSON format in decoded credentials: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e
        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to load service account credentials. Exception type: {type(e).__name__}, Details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e

    def __authenticate_service_account(self) -> bigquery.Client:
        try:
            logger.info(f"{self.__class__.__name__}: Authenticating with service account credentials")
            credentials = service_account.Credentials.from_service_account_info(self.__load_service_account())
            client = bigquery.Client(credentials=credentials, project=settings.ENV.PROJECT_ID)
            logger.info(f"{self.__class__.__name__}: Authentication successful for project {settings.ENV.PROJECT_ID}")
            return client
        except Exception as e:
            msg = f"{self.__class__.__name__}: Failed to authenticate with BigQuery: {str(e)}"
            logger.error(msg, exc_info=True)
            raise AuthenticationError(msg) from e

    async def execute_query(
        self, query: str, job_config: bigquery.QueryJobConfig | None = None, **kwargs: Any
    ) -> pd.DataFrame:
        """Execute a BigQuery SQL query asynchronously.

        Args:
            query (str): The SQL query to execute.
            job_config (bigquery.QueryJobConfig, optional): The configuration for the query job. Defaults to None.
            **kwargs: Additional keyword arguments to pass to the query method.

        Returns:
            pd.DataFrame: The results of the query.

        Raises:
            BigQueryError: If there's an error executing the query.
        """
        start_time = time.time()
        logger.info(f"Executing BigQuery query: {query}")

        try:
            # Run the query in a thread pool to avoid blocking
            query_job = await asyncio.to_thread(self.client.query, query, job_config=job_config, **kwargs)
            job_id = query_job.job_id
            logger.info(f"Query job {job_id} started")

            # Wait for the query to complete
            results = await asyncio.to_thread(query_job.result)
            df = await asyncio.to_thread(results.to_dataframe)

            elapsed_time = time.time() - start_time
            row_count = len(df)
            logger.info(f"Query job {job_id} completed successfully in {elapsed_time:.2f}s, returning {row_count} rows")
            return df
        except Exception as e:
            elapsed_time = time.time() - start_time
            error_msg = f"Query execution failed after {elapsed_time:.2f}s.\nError type: {type(e).__name__}\nError details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise BigQueryError(f"Error executing query: {str(e)}") from e

    async def _validate_dataset_table(self, dataset_id: str, table_id: str) -> None:
        logger.info(f"Validating dataset '{dataset_id}' and table '{table_id}'")
        try:
            await asyncio.to_thread(self.client.get_dataset, dataset_id)
            logger.info(f"Dataset '{dataset_id}' exists")
            try:
                table_ref = f"{dataset_id}.{table_id}"
                await asyncio.to_thread(self.client.get_table, table_ref)
                logger.info(f"Table '{table_ref}' exists")
            except Exception as e:
                logger.warning(f"Table validation failed for '{table_id}' in dataset '{dataset_id}': {str(e)}")
        except Exception as e:
            error_msg = f"Invalid dataset '{dataset_id}': {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg) from None

    async def upsert_dataframe(
        self, dataframe: pd.DataFrame, table_id: str, schema: list[dict[str, str]], if_exists: str = "append"
    ) -> None:
        """Upsert (insert or update) a pandas DataFrame to a BigQuery table asynchronously.

        Args:
            dataframe (pd.DataFrame): The DataFrame to upsert
            table_id (str): The ID of the target table
            schema (list[dict[str, str]]): The schema of the table as a list of dicts with 'name' and 'type'
            if_exists (str, optional): What to do if the table exists.
                Options are 'fail', 'replace', or 'append'. Defaults to 'append'.

        Raises:
            ValueError: If invalid if_exists option is provided
            BigQueryError: If there's an error during the upsert operation
        """
        start_time = time.time()
        logger.info(f"Upserting {len(dataframe)} rows to table {table_id}")
        _tmp_df = dataframe.copy()
        _tmp_df.rename(columns={k: k.upper() for k in _tmp_df.columns}, inplace=True)

        try:
            # Validate if_exists parameter
            if if_exists not in ["fail", "replace", "append"]:
                raise ValueError("if_exists must be one of: 'fail', 'replace', 'append'")

            # Configure the load job
            job_config = bigquery.LoadJobConfig(
                schema=schema,
                write_disposition={
                    "fail": bigquery.WriteDisposition.WRITE_EMPTY,
                    "replace": bigquery.WriteDisposition.WRITE_TRUNCATE,
                    "append": bigquery.WriteDisposition.WRITE_APPEND,
                }[if_exists],
            )

            # Start the load job in a thread pool
            load_job = await asyncio.to_thread(
                self.client.load_table_from_dataframe, _tmp_df, table_id, job_config=job_config
            )

            # Wait for the job to complete
            await asyncio.to_thread(load_job.result)

            elapsed_time = time.time() - start_time
            logger.info(
                f"Upsert job completed successfully in {elapsed_time:.2f}s, {load_job.output_rows} rows affected"
            )
            logger.info(f"Successfully upserted {len(dataframe)} rows to {table_id}")

        except Exception as e:
            error_msg = f"Error upserting data to BigQuery: {str(e)}"
            logger.error(error_msg)
            raise BigQueryError(error_msg) from e
