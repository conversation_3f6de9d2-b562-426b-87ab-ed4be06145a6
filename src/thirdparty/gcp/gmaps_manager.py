import googlemaps

from src.const.config import get_settings
from src.utils.exceptions import GMapsAPIError
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class GMapsManager:
    """Manages Google Maps API interactions with caching and error handling.

    This class provides a wrapper around the Google Maps API client to handle
    distance calculations, geocoding, and other mapping operations with built-in
    error handling and request rate tracking.
    """

    client = googlemaps.Client(key=settings.ENV.GMAP_API_KEY.get_secret_value())

    def __init__(self):
        """Initialize GMapsManager with API key and reset API call counter."""
        self.api_call_count = 0
        logger.info("GMapsManager initialized.")

    def get_distance_matrix(self, origin: str, destination: str, mode: str = "driving") -> dict:
        """Calculate the distance and duration between two locations using Google Maps API.

        Args:
            origin (str): Starting location address or coordinates
            destination (str): Ending location address or coordinates
            mode (str, optional): Travel mode (driving, walking, bicycling, transit). Defaults to "driving"

        Returns:
            dict: Distance matrix result containing distance and duration information.
                Example: {
                    "rows": [{
                        "elements": [{
                            "distance": {"text": "12.8 km", "value": 12800},
                            "duration": {"text": "15 mins", "value": 900},
                            "status": "OK"
                        }]
                    }]
                }

        Note:
            - Makes actual API calls to Google Maps
            - Tracks number of API calls made
            - Returns distances in meters and durations in seconds
        """
        # TODO: research on how to handle rate limiting and batch requests
        res = self.client.distance_matrix([origin], [destination], mode=mode, units="metric", language="en")
        self.api_call_count += 1
        return res

    def get_distance_duration(self, origin: str, destination: str) -> tuple[float, str]:
        """Extract distance in kilometers and duration text between two locations.

        Args:
            origin (str): Starting location address or coordinates
            destination (str): Ending location address or coordinates

        Returns:
            tuple[float, str]: Distance in kilometers and duration as a human-readable string.
                Example: (12.8, "15 mins")

        Note:
            - Parses the Google Maps API response to extract relevant information
            - Handles different distance units (meters, kilometers)
            - Raises GMapsAPIError in case of API failure or unexpected response format
        """
        try:
            result = self.get_distance_matrix(origin, destination)
            element = result["rows"][0]["elements"][0]

            # Extract duration
            distance_text = element["distance"]["text"]

            # Extract distance in km or m
            distance_value = float(distance_text.replace(",", "").split()[0])
            if "km" in distance_text:
                distance_km = distance_value
            elif "m" in distance_text:
                distance_km = distance_value / 1000
            else:
                raise ValueError(f"Unknown distance unit in Google Maps API response. Got: {distance_text}")

            # TODO: handle convert duration in seconds
            duration_text = element["duration"]["text"]

            return distance_km, duration_text

        except Exception as e:
            logger.warning(f"Error getting distance matrix from {origin} and {destination} {e}")
            raise GMapsAPIError("Error getting distance matrix") from e

    def get_api_call_count(self) -> int:
        """Retrieve the total number of API calls made by this instance.

        Returns:
            int: Total API call count.
        """
        return self.api_call_count
