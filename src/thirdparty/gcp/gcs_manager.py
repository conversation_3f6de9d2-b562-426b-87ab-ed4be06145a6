"""A wrapper for the Google Cloud Storage client."""

import base64
import json
from pathlib import Path
from typing import Any

from google.cloud import storage
from google.oauth2 import service_account

from src.const.config import get_settings
from src.utils.exceptions import AuthenticationError, StorageError
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class GCSManager:
    """Base class for Google Cloud Storage operations."""

    def __init__(self) -> None:
        """Initialize the GCSManager class."""
        self.client = self.__authenticate_service_account()
        logger.info(f"{self.__class__.__name__} initialized for project: {settings.ENV.PROJECT_ID}")

    def __load_service_account(self) -> dict[str, Any]:
        """Load the service account info from the provided file."""
        try:
            __base64_str = settings.ENV.SERVICE_ACCOUNT_ENCODE

            __decoded_bytes = base64.b64decode(__base64_str.get_secret_value())
            creds_dict = json.loads(__decoded_bytes.decode("utf-8"))
            return creds_dict
        except json.JSONDecodeError as e:
            error_msg = f"{self.__class__.__name__}: Invalid JSON format in decoded credentials: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e
        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to load service account credentials. Exception type: {type(e).__name__}, Details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e

    def __authenticate_service_account(self) -> storage.Client:
        """Authenticate using the provided service account info and build the GCS client."""
        try:
            logger.info(f"{self.__class__.__name__}: Authenticating with service account credentials")
            credentials = service_account.Credentials.from_service_account_info(self.__load_service_account())
            client = storage.Client(credentials=credentials, project=settings.ENV.PROJECT_ID)
            logger.info(f"{self.__class__.__name__}: Authentication successful for project {settings.ENV.PROJECT_ID}")
            return client
        except Exception as e:
            msg = f"{self.__class__.__name__}: Failed to authenticate with GCS: {str(e)}"
            logger.error(msg, exc_info=True)
            raise AuthenticationError(msg) from e

    def upload_file(self, bucket_name: str, source_file_path: str | Path, destination_blob_name: str) -> None:
        """Upload a file to a GCS bucket.

        Args:
            bucket_name (str): Name of the bucket to upload to
            source_file_path (Union[str, Path]): Local file path to upload
            destination_blob_name (str): Destination blob name in GCS

        Raises:
            StorageError: If there's an error uploading the file
        """
        try:
            source_file_path = Path(source_file_path) if isinstance(source_file_path, str) else source_file_path
            logger.info(
                f"{self.__class__.__name__}: Uploading file {source_file_path} to {bucket_name}/{destination_blob_name}"
            )

            bucket = self.client.bucket(bucket_name)
            blob = bucket.blob(destination_blob_name)
            blob.upload_from_filename(str(source_file_path))

            logger.info(
                f"{self.__class__.__name__}: Uploaded {source_file_path} to {bucket_name}/{destination_blob_name}"
            )
        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to upload file. Error: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise StorageError(error_msg) from e

    def download_file(self, bucket_name: str, source_blob_name: str, destination_file_path: str | Path) -> None:
        """Download a file from a GCS bucket.

        Args:
            bucket_name (str): Name of the bucket to download from
            source_blob_name (str): Source blob name in GCS
            destination_file_path (Union[str, Path]): Local file path to download to

        Raises:
            StorageError: If there's an error downloading the file
        """
        try:
            destination_file_path = (
                Path(destination_file_path) if isinstance(destination_file_path, str) else destination_file_path
            )
            destination_file_path.parent.mkdir(parents=True, exist_ok=True)

            logger.info(
                f"{self.__class__.__name__}: Downloading file {bucket_name}/{source_blob_name} to {destination_file_path}"
            )

            bucket = self.client.bucket(bucket_name)
            blob = bucket.blob(source_blob_name)
            blob.download_to_filename(str(destination_file_path))

            logger.info(
                f"{self.__class__.__name__}: Downloaded {bucket_name}/{source_blob_name} to {destination_file_path}"
            )
        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to download file. Error: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise StorageError(error_msg) from e

    def list_blobs(self, bucket_name: str, prefix: str | None = None) -> list[storage.Blob]:
        """List blobs in a GCS bucket with an optional prefix.

        Args:
            bucket_name (str): Name of the bucket to list blobs from
            prefix (Optional[str]): Prefix to filter blobs by

        Returns:
            List[storage.Blob]: List of blobs in the bucket

        Raises:
            StorageError: If there's an error listing blobs
        """
        try:
            logger.info(f"{self.__class__.__name__}: Listing blobs in {bucket_name} with prefix {prefix}")

            bucket = self.client.bucket(bucket_name)
            blobs = list(bucket.list_blobs(prefix=prefix))

            logger.info(f"{self.__class__.__name__}: Listed {len(blobs)} blobs in {bucket_name} with prefix {prefix}")
            return blobs
        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to list blobs. Error: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise StorageError(error_msg) from e

    def upload_folder(
        self,
        bucket_name: str,
        source_folder_path: str | Path,
        destination_prefix: str = "",
    ) -> list[str]:
        """Upload all files from a folder to a GCS bucket, preserving directory structure.

        Args:
            bucket_name (str): Name of the bucket to upload to
            source_folder_path (Union[str, Path]): Local folder path to upload
            destination_prefix (str): Prefix to add to all destination blob names

        Returns:
            list[str]: List of uploaded blob names

        Raises:
            StorageError: If there's an error uploading any file
        """
        source_folder_path = Path(source_folder_path) if isinstance(source_folder_path, str) else source_folder_path

        if not source_folder_path.is_dir():
            error_msg = f"{self.__class__.__name__}: Source path is not a directory: {source_folder_path}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            logger.info(
                f"{self.__class__.__name__}: Uploading folder {source_folder_path} to {bucket_name}/{destination_prefix}"
            )

            uploaded_blobs = []
            total_files = 0

            # Walk through the directory
            for item in source_folder_path.glob("**/*"):
                if item.is_file():
                    # Calculate relative path from source directory
                    relative_path = item.relative_to(source_folder_path)
                    # Construct destination blob name
                    if destination_prefix:
                        destination_blob_name = f"{destination_prefix}/{relative_path}"
                    else:
                        destination_blob_name = str(relative_path)

                    # Replace backslashes with forward slashes for GCS paths on Windows
                    destination_blob_name = destination_blob_name.replace("\\", "/")

                    # Upload file
                    bucket = self.client.bucket(bucket_name)
                    blob = bucket.blob(destination_blob_name)
                    blob.upload_from_filename(str(item))

                    uploaded_blobs.append(destination_blob_name)
                    total_files += 1
                    logger.debug(f"{self.__class__.__name__}: Uploaded {item} to {bucket_name}/{destination_blob_name}")

            logger.info(
                f"{self.__class__.__name__}: Completed folder upload of {total_files} files to"
                f" {bucket_name}/{destination_prefix}"
            )
            return uploaded_blobs

        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to upload folder {source_folder_path}. Error: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise StorageError(error_msg) from e

    def download_folder(
        self,
        bucket_name: str,
        source_prefix: str,
        destination_folder: str | Path,
        preserve_structure: bool = True,
    ) -> list[Path]:
        """Download all blobs with a specific prefix from a GCS bucket to a local folder.

        Args:
            bucket_name (str): Name of the bucket to download from
            source_prefix (str): Prefix to filter blobs by
            destination_folder (Union[str, Path]): Local folder to download files to
            preserve_structure (bool): If True, preserves the directory structure from GCS

        Returns:
            list[Path]: List of downloaded file paths

        Raises:
            StorageError: If there's an error downloading any file
        """
        destination_folder = Path(destination_folder) if isinstance(destination_folder, str) else destination_folder

        try:
            logger.info(
                f"{self.__class__.__name__}: Downloading files from {bucket_name}/{source_prefix} to {destination_folder}"
            )

            # Ensure the destination folder exists
            destination_folder.mkdir(parents=True, exist_ok=True)

            # List all blobs with the given prefix
            blobs = self.list_blobs(bucket_name, prefix=source_prefix)

            downloaded_files: list[Path] = []
            if not blobs:
                logger.info(
                    f"{self.__class__.__name__}: No files found with prefix {source_prefix} in bucket {bucket_name}"
                )
                return downloaded_files

            for blob in blobs:
                # Determine the destination path
                if preserve_structure:
                    if source_prefix and blob.name.startswith(source_prefix):
                        # Extract the relative path from the prefix
                        relative_path = blob.name[len(source_prefix) :].lstrip("/")
                    else:
                        relative_path = blob.name

                    dest_file_path = destination_folder / relative_path
                else:
                    # Just use the filename without path structure
                    dest_file_path = destination_folder / Path(blob.name).name

                # Ensure the parent directory exists
                dest_file_path.parent.mkdir(parents=True, exist_ok=True)

                # Download the blob
                blob.download_to_filename(str(dest_file_path))
                downloaded_files.append(dest_file_path)

                logger.debug(f"{self.__class__.__name__}: Downloaded {blob.name} to {dest_file_path}")

            logger.info(
                f"{self.__class__.__name__}: Downloaded {len(downloaded_files)} files from "
                f"{bucket_name}/{source_prefix} to {destination_folder}"
            )
            return downloaded_files

        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to download folder from {bucket_name}/{source_prefix}. Error: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise StorageError(error_msg) from e
