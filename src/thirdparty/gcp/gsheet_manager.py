import base64
import json
import time
from typing import Any

import gspread
import pandas as pd
from google.oauth2 import service_account

from src.const.config import get_settings
from src.utils.exceptions import AuthenticationError, GoogleSheetsError
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class GoogleSheetsManager:
    """A warpper class for Google Sheets operations."""

    def __init__(self) -> None:
        """Initialize the GoogleSheetsManager class."""
        self.client = self.__authenticate_service_account()
        logger.info(f"{self.__class__.__name__} initialized for project: {settings.ENV.PROJECT_ID}")

    def __load_service_account(self) -> dict[str, Any]:
        """Load the service account info from the provided file."""
        try:
            __base64_str = settings.ENV.SERVICE_ACCOUNT_ENCODE

            __decoded_bytes = base64.b64decode(__base64_str.get_secret_value())
            creds_dict = json.loads(__decoded_bytes.decode("utf-8"))
            return creds_dict
        except json.JSONDecodeError as e:
            error_msg = f"{self.__class__.__name__}: Invalid JSON format in decoded credentials: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e
        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to load service account credentials. Exception type: {type(e).__name__}, Details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e

    def __authenticate_service_account(self) -> gspread.Client:
        try:
            logger.info(f"{self.__class__.__name__}: Authenticating with service account credentials")
            scope = ["https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/drive"]
            credentials = service_account.Credentials.from_service_account_info(
                self.__load_service_account(), scopes=scope
            )
            client = gspread.authorize(credentials)
            logger.info(f"{self.__class__.__name__}: Authentication successful for project {settings.ENV.PROJECT_ID}")
            return client
        except Exception as e:
            msg = f"{self.__class__.__name__}: Failed to authenticate with Google Sheets: {str(e)}"
            logger.error(msg, exc_info=True)
            raise AuthenticationError(msg) from e

    def create_or_update_sheet(self, sheet_id: str, dataframe: pd.DataFrame) -> str:
        """Create a new spreadsheet or update existing one with data from DataFrame.

        Args:
            sheet_id: ID of the spreadsheet (or name for new spreadsheets)
            dataframe: DataFrame containing the data to write

        Returns:
            str: URL of the spreadsheet

        Raises:
            GoogleSheetsError: If there's an error creating or updating the sheet.
        """
        start_time = time.time()
        logger.info(f"Creating or updating Google Sheet with ID/name: {sheet_id}")

        try:
            # Try to open existing sheet by ID, create new if not exists
            try:
                spreadsheet = self.client.open_by_key(sheet_id)
                logger.info(f"Found existing spreadsheet with ID: {sheet_id}")
            except gspread.exceptions.APIError:
                # If the ID is invalid, try to create a new sheet using the ID as name
                logger.info(f"Sheet ID not found, creating new spreadsheet with name: {sheet_id}")
                spreadsheet = self.client.create(sheet_id)
                # Make the spreadsheet publicly viewable
                spreadsheet.share(None, perm_type="anyone", role="reader")

            # Select the first worksheet or create if not exists
            try:
                worksheet = spreadsheet.get_worksheet(0)
                if not worksheet:
                    raise Exception("No worksheet found")
            except Exception:
                worksheet = spreadsheet.add_worksheet(title="Results", rows=1, cols=1)
                logger.info("Added new worksheet 'Results'")

            # Clear existing content
            worksheet.clear()

            # Write headers and data
            headers = dataframe.columns.tolist()
            values = [headers] + dataframe.values.tolist()

            worksheet.update(values)

            elapsed_time = time.time() - start_time
            logger.info(
                f"Updated spreadsheet with {len(dataframe)} rows and {len(headers)} columns in {elapsed_time:.2f}s"
            )

            return spreadsheet.url

        except Exception as e:
            elapsed_time = time.time() - start_time
            error_msg = f"Sheet creation/update failed after {elapsed_time:.2f}s.\nError type: {type(e).__name__}\nError details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise GoogleSheetsError(f"Error creating or updating sheet: {str(e)}") from e

    def append_to_sheet(self, sheet_id: str, worksheet_name: str, dataframe: pd.DataFrame) -> None:
        """Append data to an existing worksheet.

        Args:
            sheet_id: ID of the spreadsheet
            worksheet_name: Name of the worksheet
            dataframe: DataFrame containing the data to append

        Raises:
            GoogleSheetsError: If there's an error appending to the sheet.
        """
        start_time = time.time()
        logger.info(f"Appending data to Google Sheet ID: {sheet_id}, Worksheet: {worksheet_name}")

        try:
            spreadsheet = self.client.open_by_key(sheet_id)
            worksheet = spreadsheet.worksheet(worksheet_name)

            # Get current data to know where to append
            existing_data = worksheet.get_all_values()
            start_row = len(existing_data) + 1

            # Convert DataFrame to list of values
            values = dataframe.values.tolist()

            # Prepare cell range
            end_row = start_row + len(values) - 1
            end_col = len(dataframe.columns)
            cell_range = f"A{start_row}:{chr(64 + end_col)}{end_row}"

            # Update cells
            worksheet.update(cell_range, values)

            elapsed_time = time.time() - start_time
            logger.info(f"Appended {len(values)} rows to spreadsheet in {elapsed_time:.2f}s")

        except Exception as e:
            elapsed_time = time.time() - start_time
            error_msg = f"Sheet append failed after {elapsed_time:.2f}s.\nError type: {type(e).__name__}\nError details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise GoogleSheetsError(f"Error appending to sheet: {str(e)}") from e

    def update_values(self, sheet_id: str, worksheet_name: str, range_name: str, values: list) -> None:
        """Update values in a specific range in a worksheet.

        Args:
            sheet_id: ID of the spreadsheet
            worksheet_name: Name of the worksheet
            range_name: Cell range to update (e.g. 'A1:B5')
            values: List of lists containing values to update

        Raises:
            GoogleSheetsError: If there's an error updating the sheet.
        """
        start_time = time.time()
        logger.info(f"Updating values in Google Sheet ID: {sheet_id}, Worksheet: {worksheet_name}, Range: {range_name}")

        try:
            spreadsheet = self.client.open_by_key(sheet_id)
            worksheet = spreadsheet.worksheet(worksheet_name)

            # Update the specified range with provided values
            worksheet.update(range_name, values)

            elapsed_time = time.time() - start_time
            logger.info(f"Updated {len(values)} rows in range {range_name} in {elapsed_time:.2f}s")

        except Exception as e:
            elapsed_time = time.time() - start_time
            error_msg = f"Value update failed after {elapsed_time:.2f}s.\nError type: {type(e).__name__}\nError details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise GoogleSheetsError(f"Error updating values in sheet: {str(e)}") from e

    def _validate_spreadsheet(self, spreadsheet_id: str, worksheet_name: str) -> None:
        """Validate if a spreadsheet and optionally a worksheet exists.

        Args:
            spreadsheet_id: ID of the spreadsheet to validate
            worksheet_name: Optional name of worksheet to validate

        Raises:
            ValueError: If spreadsheet or worksheet doesn't exist
        """
        logger.info(f"Validating spreadsheet '{spreadsheet_id}'")
        try:
            spreadsheet = self.client.open_by_key(spreadsheet_id)
            logger.info(f"Spreadsheet '{spreadsheet.title}' exists")

            try:
                spreadsheet.worksheet(worksheet_name)
                logger.info(f"Worksheet '{worksheet_name}' exists")
            except Exception as e:
                error_msg = (
                    f"Worksheet validation failed for '{worksheet_name}' in spreadsheet '{spreadsheet.title}': {str(e)}"
                )
                logger.warning(error_msg)
                raise ValueError(error_msg) from e

        except Exception as e:
            error_msg = f"Invalid spreadsheet ID '{spreadsheet_id}': {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg) from None
