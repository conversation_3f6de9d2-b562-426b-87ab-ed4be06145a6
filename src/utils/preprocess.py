import pandas as pd
import pandas_gbq

from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


def find_key_by_location_name(location_name: str, locations_dict: dict) -> str | None:
    """Find the location key in a dictionary by matching the location name.

    Performs a case-sensitive search through the locations dictionary to find
    an entry where the location_name matches exactly.

    Args:
        location_name (str): Location name to search for
        locations_dict (dict): Dictionary mapping location keys to details
            Format: {key: {"location_name": str, ...}, ...}

    Returns:
        str | None: Key for the matching location, or None if not found
    """
    for key, value in locations_dict.items():
        if value["location_name"] == location_name:
            return key
    return None


def upload_to_bq(df: pd.DataFrame, project_id: str, dataset_id: str, table_id: str, method: str) -> None:
    """Uploads the given DataFrame to BigQuery.

    Args:
        df: DataFrame to upload
        project_id: GCP project ID
        dataset_id: BigQuery dataset ID
        table_id: BigQuery table ID
        method: Upload method (e.g., "replace", "append")
    """
    full_table_id = f"{project_id}.{dataset_id}.{table_id}"

    pandas_gbq.to_gbq(
        df,
        full_table_id,
        project_id=project_id,
        if_exists=method,
    )
    logger.info(f"✅ Data uploaded successfully to {full_table_id}")
