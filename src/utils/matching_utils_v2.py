"""Utilities for street turn matching operations."""

import datetime
from typing import Any

import pandas as pd
from pydantic import BaseModel

from src.const import get_settings
from src.const.col_map import MATCHING_RESULTS_SCHEMA
from src.services import ArtifactService, DataService, StreetTurnMatchingService
from src.thirdparty.gcp.bq_manager import BigQueryManager
from src.utils.decorators import measure_time
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)
settings = get_settings()
bq_manager = BigQueryManager()


class MatchingSettingsV2(BaseModel):
    """Settings for the street turn matching process."""

    trigger_timestamp: datetime.datetime
    door_cy_value: str = settings.SYSTEM.DELIVERY_TYPE
    hazmat: str = settings.SYSTEM.HAZMAT
    customer_nominated_trucker: bool = settings.SYSTEM.CUSTOMER_NOMINATED_TRUCKER
    drop_and_pick: str = settings.SYSTEM.DROP_AND_PICK
    nodes: list[str]
    maximum_distance_thres_km: float
    time_tolerance_thres: int
    spreadsheet_id: str = settings.GCP.GSHEETS.SPREADSHEET_ID
    matching_results_table: str = f"{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.MATCHED_CANDIDATE_PAIRS_TABLE_ID}"
    ranking_coff: tuple[float, ...]
    street_turn_cost_options: list[str]


@measure_time(logger=logger)
async def execute_matching_process_v2(
    matching_service: StreetTurnMatchingService,
    data_service: DataService,
    artifact_service: ArtifactService,
    settings: MatchingSettingsV2,
    filtered_pairs=None,
    filtered_node: str = "USATL63",
) -> dict[str, Any]:
    """Execute the common street-turn matching process.

    This function encapsulates the shared logic used across different entry points
    for the street-turn matching process. It handles:
    - Setting up the execution environment
    - Fetching or using provided data
    - Running the matching algorithm
    - Enriching results
    - Exporting results to Google Sheets and BigQuery

    Args:
        matching_service: Service for performing street-turn matching
        data_service: Service for fetching necessary data
        artifact_service: Service for storing results in Google Sheets
        settings: Configuration settings.
        filtered_pairs: Optional pre-filtered candidate pairs for matching
        filtered_node: Node to filter the results for Google Sheets export

    Returns:
        Dict containing status, message, and match_id
    """
    trigger_timestamp = settings.trigger_timestamp

    try:
        # Setup environment for this run
        match_id, run_folder = matching_service.setup(trigger_timestamp)

        all_enriched_matches = []
        all_matches_meta = {}

        if filtered_pairs is None:
            # Process each node separately
            for node in settings.nodes:
                # Fetch required data for this node
                data_collection = await data_service.fetch_all_data(
                    trigger_dt=trigger_timestamp,
                    door_cy_value=settings.door_cy_value,
                    hazmat=settings.hazmat,
                    customer_nominated_trucker=settings.customer_nominated_trucker,
                    drop_and_pick=settings.drop_and_pick,
                    node_cd=(node,),
                    save_dir=run_folder,
                )

                # Create all possible pairs for matching
                all_pairs = await matching_service.create_all_matches(match_id, trigger_timestamp, data_collection)
                matches = await matching_service.find_matches(
                    max_distance_thres_km=settings.maximum_distance_thres_km,
                    time_tolerance_thres=settings.time_tolerance_thres,
                    all_pairs=all_pairs,
                )

                # Get rate lane mapping data
                ratelane_mapping_data = data_collection["ratelane_mapping_data"]

                # Enrich matches with additional route information
                enrich_matches, matches_meta = await matching_service.find_actual_route(
                    matches,
                    ratelane_mapping_data,
                    street_turn_cost_options=settings.street_turn_cost_options,
                    ranking_priorities=[],
                    dev_mode=True,
                    ranking_coefficients=settings.ranking_coff,
                )

                if not enrich_matches.empty:
                    all_enriched_matches.append(enrich_matches)
                    _matches_meta = {}
                    for item in matches_meta:
                        cost_formula = item.pop("cost_formula")  # Remove and get the cost_formula value
                        _matches_meta[cost_formula] = item
                        _matches_meta[cost_formula]["no_imports"] = all_pairs["imp_cop_no"].nunique()
                        _matches_meta[cost_formula]["no_exports"] = all_pairs["xpt_cop_no"].nunique()
                        _matches_meta[cost_formula]["no_possible_street_turns"] = len(all_pairs)
                        all_matches_meta[node] = _matches_meta

        else:
            # Use the provided filtered pairs
            all_pairs = filtered_pairs
            matches = await matching_service.find_matches(
                max_distance_thres_km=settings.maximum_distance_thres_km,
                time_tolerance_thres=settings.time_tolerance_thres,
                all_pairs=all_pairs,
            )

            ratelane_mapping_data = await data_service._fetch_ratelane_mapping_data(
                trigger_timestamp, (filtered_node[:5],)
            )

            enrich_matches, matches_meta = await matching_service.find_actual_route(
                matches,
                ratelane_mapping_data,
                street_turn_cost_options=settings.street_turn_cost_options,
                ranking_priorities=[],
                dev_mode=True,
                ranking_coefficients=settings.ranking_coff,
            )
            if not enrich_matches.empty:
                all_enriched_matches.append(enrich_matches)
                _matches_meta = {}
                for item in matches_meta:
                    cost_formula = item.pop("cost_formula")  # Remove and get the cost_formula value
                    _matches_meta[cost_formula] = item
                    _matches_meta[cost_formula]["no_imports"] = all_pairs["imp_cop_no"].nunique()
                    _matches_meta[cost_formula]["no_exports"] = all_pairs["xpt_cop_no"].nunique()
                    _matches_meta[cost_formula]["no_possible_street_turns"] = len(all_pairs)
                    all_matches_meta[filtered_node] = _matches_meta

        # Combine all results
        if not all_enriched_matches:
            logger.warning("No matches found for any nodes")
            enrich_matches = pd.DataFrame()
        else:
            enrich_matches = pd.concat(all_enriched_matches, ignore_index=True)
        export_df = prepare_export_data(enrich_matches)

        # Store results in BigQuery
        if not export_df.empty:
            export_df["match_id"] = match_id
            export_df["trigger_timestamp"] = trigger_timestamp
            export_df["maximum_distance_km"] = settings.maximum_distance_thres_km
            export_df["time_tolerance"] = settings.time_tolerance_thres

            export_df = export_df.convert_dtypes()
            timestamp_columns = [
                "imp_availability_at_final_cy",
                "imp_estimated_delivery_date",
                "xpt_first_receiving_date",
                "xpt_cut_off_date",
            ]
            for col in timestamp_columns:
                export_df[col] = pd.to_datetime(export_df[col])
            export_df["street_turn_vndr"] = export_df["street_turn_vndr"].astype("string")
            export_df["round_trip_imp_vndr"] = export_df["round_trip_imp_vndr"].astype("string")
            export_df["round_trip_xpt_vndr"] = export_df["round_trip_xpt_vndr"].astype("string")
            export_df["street_turn_total_cost"] = pd.to_numeric(export_df["street_turn_total_cost"])
            export_df["round_trip_total_cost"] = pd.to_numeric(export_df["round_trip_total_cost"])
            export_df["cost_sav"] = pd.to_numeric(export_df["cost_sav"])
            if filtered_pairs is None:
                await bq_manager.upsert_dataframe(
                    dataframe=export_df,
                    table_id=settings.matching_results_table,
                    schema=MATCHING_RESULTS_SCHEMA,
                    if_exists="replace",
                )
                await bq_manager.upsert_dataframe(
                    dataframe=export_df,
                    table_id=settings.matching_results_table + "_HIS",
                    schema=MATCHING_RESULTS_SCHEMA,
                    if_exists="append",
                )
            logger.info(f"Successfully stored {len(export_df)} matching results in BigQuery")

        # ----------------- For google sheet only: -----------------
        export_df = export_df[
            (export_df["port_cd"] == filtered_node)
            & (export_df["cost_formula"] == settings.street_turn_cost_options[0])
        ]
        # Combine all results
        if not all_enriched_matches:
            _matches_meta = {
                "total_cost_save": 0.0,
                "total_distance_save": 0.0,
                "container_reuse": 0,
            }
        else:
            _matches_meta = {
                "total_cost_save": all_matches_meta[filtered_node][settings.street_turn_cost_options[0]][
                    "total_cost_save"
                ],
                "total_distance_save": all_matches_meta[filtered_node][settings.street_turn_cost_options[0]][
                    "total_distance_save"
                ],
                "container_reuse": all_matches_meta[filtered_node][settings.street_turn_cost_options[0]][
                    "container_reuse"
                ],
                "number_of_imports": all_matches_meta[filtered_node][settings.street_turn_cost_options[0]][
                    "no_imports"
                ],
                "number_of_exports": all_matches_meta[filtered_node][settings.street_turn_cost_options[0]][
                    "no_exports"
                ],
                "number_of_possible_street-turns": all_matches_meta[filtered_node][
                    settings.street_turn_cost_options[0]
                ]["no_possible_street_turns"],
                # NOTE: this maybe only for internal use
                # "number_of_selected_street-turns": all_matches_meta[filtered_node]["no_selected_street_turns"],
            }

        # Prepare metadata for export
        metadata = {
            "Max Distance Threshold (km)": settings.maximum_distance_thres_km,
            "Time Tolerance Threshold (days)": settings.time_tolerance_thres,
        }
        metadata.update(_matches_meta)
        # Rename abbreviated columns back to full names for Google Sheets export
        column_mapping = {
            "imp_cop_no": "import_cop_no",
            "imp_bkg_no": "import_bkg_no",
            "imp_cntr_no": "import_container_no",
            "xpt_cop_no": "export_cop_no",
            "xpt_bkg_no": "export_bkg_no",
            "imp_cntr_tpsz_cd": "import_container_type_size_code",
            "xpt_cntr_tpsz_cd": "export_container_type_size_code",
            "imp_loc_cd": "import_location_code",
            "xpt_loc_cd": "export_location_code",
            "imp_loc_city": "import_location_city",
            "xpt_loc_city": "export_location_city",
            "imp_availability_at_final_cy": "import_availability_at_final_cy",
            "imp_estimated_delivery_date": "import_estimated_delivery_date",
            "xpt_first_receiving_date": "export_first_receiving_date",
            "xpt_cut_off_date": "export_cut_off_date",
            "time_gap_in_hour": "time_gap_in_hours",
            "imp_st_addr": "import_street_address",
            "xpt_st_addr": "export_street_address",
            "imp_to_xpt_dist_km": "import_to_export_distance_km",
            "street_turn_route_dist_km": "street_turn_route_distance_km",
            "round_trip_route_dist_km": "round_trip_route_distance_km",
            "dist_sav_km": "distance_saved_km",
            "street_turn_vendor": "street_turn_vendor",
            "street_turn_total_cost": "street_turn_total_cost",
            "round_trip_imp_vndr": "round_trip_import_vendor",
            "round_trip_xpt_vndr": "round_trip_export_vendor",
            "round_trip_total_cost": "round_trip_total_cost",
            "cost_save": "cost_saved",
            "optimal": "is_optimal",
        }
        export_df = export_df.rename(columns=column_mapping)
        export_df = export_df.drop(
            columns=["match_id", "trigger_timestamp", "maximum_distance_km", "time_tolerance", "cost_formula"],
            errors="ignore",
        )
        # convert time_gap_in_hour more visually appealing
        export_df["time_gap_in_hours"] = export_df["time_gap_in_hours"].apply(
            lambda x: f"{int(x // 24)} days {int(x % 24)} hours" if abs(x) >= 24 else f"{int(x)} hours"
        )
        export_df = export_df.rename(columns={"time_gap_in_hours": "time_gap"})
        artifact_service.write_matches(settings.spreadsheet_id, export_df, metadata)

        logger.info(f"Completed matching with ID {match_id} and cached enriched pairs")

        # Return success response
        return {"status": "success", "message": "Matching completed successfully", "match_id": match_id}
    except Exception as e:
        logger.error(f"Error during matching: {str(e)}", exc_info=True)
        raise e


def prepare_export_data(enrich_matches: pd.DataFrame) -> pd.DataFrame:
    """Prepare matched data for export."""
    # Select only necessary columns for export
    export_columns = [
        "port_cd",
        "imp_cop_no",
        "imp_bkg_no",
        "imp_cntr_no",
        "xpt_cop_no",
        "xpt_bkg_no",
        "imp_cntr_tpsz_cd",
        "xpt_cntr_tpsz_cd",
        "imp_loc_cd",
        "xpt_loc_cd",
        "imp_loc_city",
        "xpt_loc_city",
        "imp_availability_at_final_cy",
        "imp_estimated_delivery_date",
        "xpt_first_receiving_date",
        "xpt_cut_off_date",
        "time_gap_in_hour",
        "imp_st_addr",
        "xpt_st_addr",
        "imp_to_xpt_dist_km",
        "street_turn_route_dist_km",
        "round_trip_route_dist_km",
        "dist_sav_km",
        "street_turn_vndr",
        "street_turn_total_cost",
        "round_trip_imp_vndr",
        "round_trip_xpt_vndr",
        "round_trip_total_cost",
        "cost_save",
        "optimal",
        "cost_formula",
    ]
    export_df = enrich_matches[export_columns].copy()
    export_df.rename(columns={"cost_save": "cost_sav"}, inplace=True)
    # Filter columns and sort by optimal status (True first)
    export_df = export_df.sort_values(by=["imp_cop_no", "cost_sav"], ascending=[False, False])
    export_df.insert(1, "selected_import_cop_for_excluded", False)
    export_df.insert(5, "selected_export_cop_for_excluded", False)

    return export_df
