import logging

from root import LOG_DIR
from src.const import get_settings

from .handlers import Handlers

settings = get_settings()


class LogHandler:
    def __init__(self):
        """Initialize the log handler."""
        self.handler = Handlers()
        self.available_handlers: list = self.handler.get_handlers()

    def get_logger(self, name: str = "app"):
        """Get a logger.

        Args:
            name (str, optional): The name of the logger. Defaults to "app".

        Returns:
            logging.Logger: The logger instance.
        """
        logdir = LOG_DIR / name
        logdir.mkdir(parents=True, exist_ok=True)

        self.available_handlers.append(self.handler.get_file_handler(logdir / f"{name}.log"))

        logger = logging.getLogger(name)
        logger.setLevel(settings.ENV.LOG_LEVEL)
        if logger.hasHandlers():
            logger.handlers.clear()
        for handler in self.available_handlers:
            logger.addHandler(handler)
        logger.propagate = False

        self.available_handlers.clear()
        self.available_handlers = self.handler.get_handlers()
        return logger
