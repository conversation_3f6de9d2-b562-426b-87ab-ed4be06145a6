import datetime as dt
from pathlib import Path

from src.const import CONST
from src.data_loader import RateLaneMappingLoader, ReportPreprocessingDataLoader, ReportDataLoader
from src.thirdparty.gcp import BigQueryManager
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


class DataService:
    """Service for coordinating data loading and preprocessing operations.

    This class manages multiple specialized data loaders and coordinates their operations
    to provide a unified interface for data access. It ensures data consistency and
    handles the preprocessing steps needed for the street-turn matching process.

    Attributes:
        _report_loader: Loader for booking and delivery reports
        _ratelane_loader: Loader for transportation rates
        _ratelane_mapping_loader: Loader for rate-lane mappings
        _preprocessing_loader: Loader for preprocessing data
    """

    def __init__(self, bigquery_manager: BigQueryManager):
        """Initialize the DataService with required data loaders.

        Args:
            bigquery_manager: Manager for BigQuery operations used by data loaders
        """
        self._ratelane_mapping_loader = RateLaneMappingLoader(bigquery_manager)
        self._preprocessing_loader = ReportPreprocessingDataLoader(bigquery_manager)
        self._report_loader = ReportDataLoader(bigquery_manager)

        logger.info(f"{self.__class__.__name__} initialized")

    async def _fetch_ratelane_mapping_data(
        self, trigger_dt: dt.datetime, node_cd: tuple[str, ...], save_dir: Path | None = None
    ):
        """Fetch and process rate lane mapping data.

        Args:
            trigger_dt: Current datetime for filtering data
            node_cd: Tuple of node codes to filter by
            save_dir: Optional directory to save processed data

        Returns:
            DataFrame containing mappings between rates and lanes
        """
        await self._ratelane_mapping_loader.fetch_data(trigger_dt, node_cd)
        await self._ratelane_mapping_loader.process_data()

        if save_dir:
            await self._ratelane_mapping_loader.save_data(save_dir)

        data_rl = await self._ratelane_mapping_loader.get_data()

        return data_rl

    async def _fetch_preprocessing_data(
        self,
        node_cd: tuple[str, ...],
        save_dir: Path | None = None,
    ):
        """Fetch and process preprocessing data.

        Args:
            node_cd: Tuple of node codes to filter by
            save_dir: Optional directory to save processed data

        Returns:
            DataFrame containing preprocessing data
        """
        await self._preprocessing_loader.fetch_data(node_cd)
        await self._preprocessing_loader.process_data()

        if save_dir:
            await self._preprocessing_loader.save_data(save_dir)

        return await self._preprocessing_loader.get_data()

    async def _fetch_report_data(
        self,
        trigger_dt: dt.datetime,
        door_cy_value: str,
        hazmat: str,
        customer_nominated_trucker: bool,
        drop_and_pick: str | None,
        node_cd: tuple[str, ...],
        save_dir: Path | None = None,
    ):
        """Fetch and process report data.

        Args:
            trigger_dt: Current datetime for filtering data
            door_cy_value: Type of delivery (DOOR/CY)
            hazmat: Hazardous material flag
            customer_nominated_trucker: Whether trucker is nominated by customer
            drop_and_pick: Drop and pick service type
            node_cd: Tuple of node codes to filter by
            save_dir: Optional directory to save processed data

        Returns:
            DataFrame containing report data
        """
        await self._report_loader.fetch_data(
            trigger_dt, door_cy_value, hazmat, customer_nominated_trucker, drop_and_pick, node_cd
        )
        await self._report_loader.process_data()

        if save_dir:
            await self._report_loader.save_data(save_dir)

        return await self._report_loader.get_data()

    async def fetch_all_data(
        self,
        trigger_dt: dt.datetime,
        door_cy_value: str,
        hazmat: str,
        customer_nominated_trucker: bool,
        drop_and_pick: str | None,
        node_cd: tuple[str, ...],
        save_dir: Path | None = None,
    ):
        """Fetch and process all required data for street-turn matching.

        This method coordinates the fetching and processing of all data types needed
        for the matching process. It ensures data consistency across different sources
        and prepares the data in the format required by the matching service.

        Args:
            trigger_dt: Current datetime for filtering data
            door_cy_value: Type of delivery (DOOR/CY)
            hazmat: Hazardous material flag
            customer_nominated_trucker: Whether trucker is nominated by customer
            drop_and_pick: Drop and pick service type
            node_cd: Tuple of node codes to filter by
            save_dir: Optional directory to save processed data

        Returns:
            dict: Dictionary containing all processed data with keys:
                - ratelane_data: Transportation rate information
                - location_data: Location and address data
                - cntr_tpsz_data: Container specifications
                - ratelane_mapping_data: Rate-lane mapping data
                - preprocessing_data: Preprocessing data from RPT_PROC
                - distance_data: Pre-calculated distances between locations
                - report_data: Raw report data
        """
        if save_dir:
            save_dir = save_dir / "input"
            save_dir.mkdir(parents=True, exist_ok=True)

        ratelane_mapping_data = await self._fetch_ratelane_mapping_data(
            trigger_dt, tuple([_[: CONST.LOCATION_CODE_PREFIX_LENGTH] for _ in node_cd]), save_dir
        )
        report_preprocessing_data = await self._fetch_preprocessing_data(node_cd, save_dir)
        report_data = await self._fetch_report_data(
            trigger_dt, door_cy_value, hazmat, customer_nominated_trucker, drop_and_pick, node_cd, save_dir
        )

        return {
            "report_data": report_data,
            "ratelane_mapping_data": ratelane_mapping_data,
            "report_preprocessing_data": report_preprocessing_data,
        }
