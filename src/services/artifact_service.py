from pathlib import Path
from typing import Any

import pandas as pd
from gspread_formatting import BooleanCondition, DataValidationRule, set_data_validation_for_cell_range

# from root import TMP_DIR
from src.thirdparty.gcp.bq_manager import BigQueryManager
from src.thirdparty.gcp.gcs_manager import GCSManager
from src.thirdparty.gcp.gdrive_manager import GoogleDriveManager
from src.thirdparty.gcp.gsheet_manager import GoogleSheetsManager
from src.utils.decorators import measure_time

# from src.utils.exceptions import ArtifactServiceError
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


class ArtifactService:
    """Service for managing artifacts across various Google Cloud services.

    This service provides a unified interface for storing and retrieving artifacts
    (data files, results, reports) across multiple Google Cloud services including:
    - Google Cloud Storage
    - BigQuery
    - Google Drive
    - Google Sheets

    The service handles proper data serialization, access control, and error handling
    for each storage service.
    """

    def __init__(self, bg_manager, gcs_manager, gdrive_manager, gsheet_manager):
        """Initialize the ArtifactService with required service managers.

        Args:
            bg_manager (BigQueryManager): For BigQuery operations
            gcs_manager (GCSManager): For Cloud Storage operations
            gdrive_manager (GoogleDriveManager): For Drive operations
            gsheet_manager (GoogleSheetsManager): For Sheets operations
        """
        self._bq_manager: BigQueryManager = bg_manager
        self._gcs_manager: GCSManager = gcs_manager
        self._gdrive_manager: GoogleDriveManager = gdrive_manager
        self._gsheet_manager: GoogleSheetsManager = gsheet_manager
        logger.info(f"{self.__class__.__name__} initialized")

    @measure_time(logger=logger)
    def save_to_gcs(
        self,
        bucket_name: str,
        file_path: str | Path,
        destination_blob_name: str | None = None,
        folder_path: str | Path | None = None,
        destination_prefix: str = "",
    ) -> None | str | list[str]:
        """Save a file or folder to Google Cloud Storage.

        Args:
            bucket_name: Name of the GCS bucket
            file_path: Path to the file to upload (if uploading a single file)
            destination_blob_name: Destination blob name (for single file upload)
            folder_path: Path to the folder to upload (if uploading a folder)
            destination_prefix: Prefix to add to destination blob names (for folder upload)

        Returns:
            None for single file upload, list of blob names for folder upload

        Raises:
            ArtifactServiceError: If there's an error during the upload
        """
        raise NotImplementedError("This function is not currently implemented. Only having the dummy code here.")
        # try:
        #     logger.info(f"Saving to Google Cloud Storage bucket: {bucket_name}")

        #     if file_path and not folder_path:
        #         # Single file upload
        #         file_path_obj = Path(file_path) if isinstance(file_path, str) else file_path

        #         if not destination_blob_name:
        #             destination_blob_name = file_path_obj.name

        #         self._gcs_manager.upload_file(
        #             bucket_name=bucket_name, source_file_path=file_path_obj, destination_blob_name=destination_blob_name
        #         )

        #         logger.info(f"Successfully uploaded file to gs://{bucket_name}/{destination_blob_name}")
        #         return destination_blob_name

        #     elif folder_path and not file_path:
        #         # Folder upload
        #         folder_path_obj = Path(folder_path) if isinstance(folder_path, str) else folder_path

        #         blob_names = self._gcs_manager.upload_folder(
        #             bucket_name=bucket_name, source_folder_path=folder_path_obj, destination_prefix=destination_prefix
        #         )

        #         logger.info(f"Successfully uploaded {len(blob_names)} files to gs://{bucket_name}/{destination_prefix}")
        #         return blob_names

        #     else:
        #         raise ValueError("Must provide either file_path or folder_path, but not both")

        # except Exception as e:
        #     error_msg = f"Failed to save to GCS: {str(e)}"
        #     logger.error(error_msg, exc_info=True)
        #     raise ArtifactServiceError(error_msg) from e

    @measure_time(logger=logger)
    def save_to_gdrive(self, file_path: str | Path, parent_folder_ids: list[str]) -> dict[str, Any]:
        """Save a file to Google Drive.

        Args:
            file_path: Path to the file to upload
            parent_folder_ids: List of parent folder IDs to upload the file to

        Returns:
            Dict with file ID and name if successful

        Raises:
            ArtifactServiceError: If there's an error during the upload
        """
        raise NotImplementedError("This function is not currently implemented. Only having the dummy code here.")
        # try:
        #     logger.info(f"Saving file to Google Drive: {file_path}")
        #     file_path_obj = Path(file_path) if isinstance(file_path, str) else file_path

        #     result = self._gdrive_manager.upload_file(file_path=file_path_obj, parent_ids=parent_folder_ids)

        #     if not result:
        #         raise ArtifactServiceError(f"Upload to Google Drive failed for {file_path}")

        #     logger.info(f"Successfully uploaded file to Google Drive with ID: {result.get('id')}")
        #     return result

        # except Exception as e:
        #     error_msg = f"Failed to save to Google Drive: {str(e)}"
        #     logger.error(error_msg, exc_info=True)
        #     raise ArtifactServiceError(error_msg) from e

    @measure_time(logger=logger)
    def save_to_gsheet(self, dataframe: pd.DataFrame, sheet_id: str) -> str:
        """Save a DataFrame to Google Sheets.

        Args:
            dataframe: DataFrame to save
            sheet_id(str): ID of the spreadsheet to save to

        Returns:
            URL of the created/updated spreadsheet

        Raises:
            ArtifactServiceError: If there's an error during the operation
        """
        raise NotImplementedError("This function is not currently implemented. Only having the dummy code here.")
        # try:
        #     logger.info(f"Saving DataFrame to Google Sheets: {sheet_id}")

        #     sheet_url = self._gsheet_manager.create_or_update_sheet(sheet_id=sheet_id, dataframe=dataframe)

        #     logger.info(f"Successfully saved DataFrame to Google Sheets: {sheet_url}")
        #     return sheet_url

        # except Exception as e:
        #     error_msg = f"Failed to save to Google Sheets: {str(e)}"
        #     logger.error(error_msg, exc_info=True)
        #     raise ArtifactServiceError(error_msg) from e

    @measure_time(logger=logger)
    def append_to_gsheet(self, dataframe: pd.DataFrame, sheet_id: str, worksheet_name: str) -> None:
        """Append data to an existing Google Sheet.

        Args:
            dataframe: DataFrame to append
            sheet_id: ID of the existing spreadsheet
            worksheet_name: Name of the worksheet to append to

        Raises:
            ArtifactServiceError: If there's an error during the operation
        """
        raise NotImplementedError("This function is not currently implemented. Only having the dummy code here.")
        # try:
        #     logger.info(f"Appending DataFrame to Google Sheet ID: {sheet_id}, Worksheet: {worksheet_name}")

        #     self._gsheet_manager.append_to_sheet(sheet_id=sheet_id, worksheet_name=worksheet_name, dataframe=dataframe)

        #     logger.info(f"Successfully appended {len(dataframe)} rows to Google Sheet")

        # except Exception as e:
        #     error_msg = f"Failed to append to Google Sheet: {str(e)}"
        #     logger.error(error_msg, exc_info=True)
        #     raise ArtifactServiceError(error_msg) from e

    @measure_time(logger=logger)
    async def save_to_bigquery(self, dataframe: pd.DataFrame, query: str) -> pd.DataFrame:
        """Execute a BigQuery query or save DataFrame results.

        Args:
            dataframe: DataFrame to process (if applicable)
            query: SQL query to execute

        Returns:
            DataFrame with query results

        Raises:
            ArtifactServiceError: If there's an error during the operation
        """
        raise NotImplementedError("This function is not currently implemented. Only having the dummy code here.")
        # try:
        #     logger.info("Executing BigQuery query")

        #     result_df = await self._bq_manager.execute_query(query=query)

        #     logger.info(f"Successfully executed BigQuery query, returned {len(result_df)} rows")
        #     return result_df

        # except Exception as e:
        #     error_msg = f"Failed to execute BigQuery query: {str(e)}"
        #     logger.error(error_msg, exc_info=True)
        #     raise ArtifactServiceError(error_msg) from e

    @measure_time(logger=logger)
    def save_json_to_gcs(self, data: dict[str, Any], bucket_name: str, destination_blob_name: str) -> str:
        """Save JSON data to Google Cloud Storage.

        Args:
            data: Dictionary to save as JSON
            bucket_name: Name of the GCS bucket
            destination_blob_name: Destination blob name

        Returns:
            Destination blob name if successful

        Raises:
            ArtifactServiceError: If there's an error during the upload
        """
        raise NotImplementedError("This function is not currently implemented. Only having the dummy code here.")
        # try:
        #     logger.info(f"Saving JSON data to Google Cloud Storage: gs://{bucket_name}/{destination_blob_name}")

        #     # Create a temporary file to store the JSON data
        #     temp_path = TMP_DIR / f"{destination_blob_name}"

        #     # Ensure the parent directory exists
        #     temp_path.parent.mkdir(parents=True, exist_ok=True)

        #     # Write the JSON data to the file
        #     with open(temp_path, "w") as f:
        #         json.dump(data, f, indent=2)

        #     # Upload the file
        #     self._gcs_manager.upload_file(
        #         bucket_name=bucket_name, source_file_path=temp_path, destination_blob_name=destination_blob_name
        #     )

        #     # Remove the temporary file
        #     temp_path.unlink()

        #     logger.info(f"Successfully saved JSON data to gs://{bucket_name}/{destination_blob_name}")
        #     return destination_blob_name

        # except Exception as e:
        #     error_msg = f"Failed to save JSON to GCS: {str(e)}"
        #     logger.error(error_msg, exc_info=True)
        #     raise ArtifactServiceError(error_msg) from e

    @measure_time(logger=logger)
    def download_from_gcs(self, bucket_name: str, source_blob_name: str, destination_file_path: str | Path) -> Path:
        """Download a file from Google Cloud Storage.

        Args:
            bucket_name: Name of the GCS bucket
            source_blob_name: Source blob name
            destination_file_path: Local path to save the file

        Returns:
            Path to the downloaded file

        Raises:
            ArtifactServiceError: If there's an error during the download
        """
        raise NotImplementedError("This function is not currently implemented. Only having the dummy code here.")
        # try:
        #     logger.info(f"Downloading from Google Cloud Storage: gs://{bucket_name}/{source_blob_name}")
        #     dest_path = Path(destination_file_path) if isinstance(destination_file_path, str) else destination_file_path

        #     self._gcs_manager.download_file(
        #         bucket_name=bucket_name, source_blob_name=source_blob_name, destination_file_path=dest_path
        #     )

        #     logger.info(f"Successfully downloaded file to {dest_path}")
        #     return dest_path

        # except Exception as e:
        #     error_msg = f"Failed to download from GCS: {str(e)}"
        #     logger.error(error_msg, exc_info=True)
        #     raise ArtifactServiceError(error_msg) from e

    @measure_time(logger=logger)
    def download_from_gdrive(self, file_id: str, destination_file_path: str | Path) -> Path:
        """Download a file from Google Drive.

        Args:
            file_id: ID of the file in Google Drive
            destination_file_path: Local path to save the file

        Returns:
            Path to the downloaded file

        Raises:
            ArtifactServiceError: If there's an error during the download
        """
        raise NotImplementedError("This function is not currently implemented. Only having the dummy code here.")
        # try:
        #     logger.info(f"Downloading from Google Drive: {file_id}")
        #     dest_path = Path(destination_file_path) if isinstance(destination_file_path, str) else destination_file_path

        #     self._gdrive_manager.download_file(file_id=file_id, dest_fpath=dest_path)

        #     logger.info(f"Successfully downloaded file to {dest_path}")
        #     return dest_path

        # except Exception as e:
        #     error_msg = f"Failed to download from Google Drive: {str(e)}"
        #     logger.error(error_msg, exc_info=True)
        #     raise ArtifactServiceError(error_msg) from e

    def _format_as_checkboxes(self, sheet_id: str, sheet_name: str, range_notation: str) -> bool:
        """Format a range of cells as checkboxes in Google Sheets.

        Args:
            sheet_id: Google Sheet ID
            sheet_name: Name of the sheet
            range_notation: Range notation in A1 format (e.g. "A2:A10")

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Formatting range {range_notation} as checkboxes in sheet {sheet_name}")

            # Get the spreadsheet and worksheet
            spreadsheet = self._gsheet_manager.client.open_by_key(sheet_id)
            worksheet = spreadsheet.worksheet(sheet_name)
            validation_rule = DataValidationRule(
                BooleanCondition(
                    "BOOLEAN", ["TRUE", "FALSE"]
                ),  # condition'type' and 'values', defaulting to TRUE/FALSE
                showCustomUi=True,
            )

            set_data_validation_for_cell_range(worksheet, range_notation, validation_rule)

            logger.info(f"Successfully formatted range {range_notation} as checkboxes")
            return True

        except Exception as e:
            logger.error(f"Error formatting cells as checkboxes: {str(e)}")
            return False

    @measure_time(logger=logger)
    def write_matches(self, sheet_id, df, metadata=None, sheet_name="Dashboard"):
        """Write DataFrame and metadata to Google Sheets.

        Args:
            sheet_id: Google Sheet ID
            df: DataFrame to write
            metadata: Optional dictionary with metadata to write as header
            sheet_name: Name of the sheet to write to

        Returns:
            bool: True if successful, False otherwise
        """
        metric_start_col, metric_start_row = "B", 16
        detail_start_col, detail_start_row = "A", 28

        try:
            # Clean worksheet before writing
            try:
                # Get the spreadsheet and worksheet
                spreadsheet = self._gsheet_manager.client.open_by_key(sheet_id)
                worksheet = spreadsheet.worksheet(sheet_name)

                # Clear the worksheet
                logger.info(f"Clearing worksheet '{sheet_name}' before writing new data")
                worksheet.batch_clear([f"{detail_start_col}{detail_start_row}:AH50000"])
            except Exception as e:
                logger.warning(f"Could not clear worksheet: {str(e)}. Will attempt to write data anyway.")

            # Write metadata if provided
            current_row = metric_start_row
            if metadata:
                metadata_values = []
                for key, value in metadata.items():
                    key = key.replace("_", " ").capitalize()
                    metadata_values.append([key, "", float(value)])
                metadata_values.append(["", ""])

                # Write metadata header
                meta_range = f"{metric_start_col}{current_row}:{chr(ord(metric_start_col) + 2)}{current_row + len(metadata_values) - 1}"
                self._gsheet_manager.update_values(sheet_id, sheet_name, meta_range, metadata_values)

                # Update current row for DataFrame start
                current_row += len(metadata_values) + 1

            # Create data values with specific data type handling
            data_values = []
            for _, row in df.iterrows():
                # Handle different data types based on column names
                row_values = []
                for col, val in row.items():
                    if col in ["selected_import_cop_for_excluded", "selected_export_cop_for_excluded"]:
                        row_values.append(bool(val))  # Keep as boolean
                    elif col in [
                        "import_to_export_distance_km",
                        "street_turn_route_distance_km",
                        "round_trip_route_distance_km",
                        "distance_saved_km",
                        "street_turn_total_cost",
                        "round_trip_total_cost",
                        "cost_saved",
                    ]:
                        row_values.append(float(val))  # Keep as float
                    else:
                        row_values.append(str(val))  # Convert rest to string
                data_values.append(row_values)

            # Convert DataFrame to list of lists (header + data)
            header_values = [_.replace("_", " ").capitalize() for _ in df.columns.tolist()]

            # Combine header and data
            values = [header_values]
            values.extend(data_values)

            # Define range for DataFrame
            start_col_index = 0
            for i, char in enumerate(detail_start_col[::-1]):
                start_col_index += (ord(char) - ord("A") + 1) * (26**i)
            end_col_index = start_col_index + len(df.columns) - 1
            end_col_str = self._get_column_letter(end_col_index)
            df_range = f"{detail_start_col}{detail_start_row}:{end_col_str}{detail_start_row + len(values) - 1}"

            # Write DataFrame
            self._gsheet_manager.update_values(sheet_id, sheet_name, df_range, values)

            # Format checkbox column (first column, skipping the header row)
            checkbox_start_row = detail_start_row + 1  # Skip header row
            checkbox_end_row = detail_start_row + len(values) - 1
            checkbox_range = f"B{checkbox_start_row}:B{checkbox_end_row}"
            self._format_as_checkboxes(sheet_id, sheet_name, checkbox_range)
            checkbox_range = f"F{checkbox_start_row}:F{checkbox_end_row}"
            self._format_as_checkboxes(sheet_id, sheet_name, checkbox_range)

            logger.info(f"Successfully wrote {len(df)} rows to Google Sheets with checkbox formatting")
            return True

        except Exception as e:
            logger.error(f"Error writing to Google Sheets: {str(e)}")
            return False

    def _get_column_letter(self, column_index):
        """Convert a column index to Excel-style column letter (A, B, C, ..., Z, AA, AB, etc.).

        Args:
            column_index: 1-based index of the column (1 = A, 2 = B, etc.)

        Returns:
            Excel-style column letter
        """
        result = ""
        while column_index > 0:
            column_index, remainder = divmod(column_index - 1, 26)
            result = chr(remainder + ord("A")) + result
        return result
