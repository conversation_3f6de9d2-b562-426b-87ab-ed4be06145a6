# import itertools
# from datetime import datetime

# import pandas as pd
# from pydantic import BaseModel, Field
# from tqdm import tqdm

# from src.const import get_settings
# from src.data_loader.distance_loader import DistanceDataLoader
# from src.data_loader.loc_loader import LocationDataLoader
# from src.data_loader.rl_mapping_loader import RateLaneMappingLoader
# from src.data_loader.rpt_loader import ReportDataLoader
# from src.thirdparty.gcp import BigQueryManager
# from src.thirdparty.gcp.gmaps_manager import GMapsManager
# from src.utils.decorators import measure_time
# from src.utils.logger import log_handler
# from src.utils.preprocess import find_key_by_location_name, upload_to_bq

# logger = log_handler.get_logger(name=__name__)
# settings = get_settings()


# class StreetTurnPair(BaseModel):
#     """Schema for a street turn pair in the database."""

#     PORT_CD: str = Field(description="Port code")
#     IMP_LOC_CD: str = Field(description="Import location code")
#     XPT_LOC_CD: str = Field(description="Export location code")
#     IMP_ST_ADDR: str = Field(description="Import street address")
#     XPT_ST_ADDR: str = Field(description="Export street address")
#     DIST_PORT_IMP: float = Field(description="Distance between port and import location")
#     DIST_PORT_XPT: float = Field(description="Distance between port and export location")
#     DIST_DOR_DOR: float = Field(description="Distance between import and export locations")
#     DIST_SAV: float = Field(description="Total distance saved")
#     MAX_COST_SAV: float | None = Field(default=None, description="Maximum cost savings")
#     CRE_AT: datetime = Field(default_factory=datetime.utcnow)
#     UPD_AT: datetime = Field(default_factory=datetime.utcnow)
#     DELT_AT: datetime | None = Field(default=None)


# class StreetTurnDBUpdater:
#     """Service for updating the street turn database with new pairs."""

#     def __init__(
#         self,
#         bq_manager: BigQueryManager,
#         trigger_dt: datetime,
#         door_cy_value: str,
#         hazmat: str,
#         customer_nominated_trucker: bool,
#         drop_and_pick: str | None,
#         node_cd: tuple[str, ...],
#     ):
#         """Initialize StreetTurnDBUpdater with required loaders.

#         Args:
#             bq_manager: Instance of BigQueryManager
#             trigger_dt: Current datetime for filtering data
#             door_cy_value: Type of delivery (DOOR/CY)
#             hazmat: Hazardous material flag
#             customer_nominated_trucker: Whether trucker is nominated by customer
#             drop_and_pick: Drop and pick service type
#             node_cd: Tuple of node codes to filter by
#         """
#         # initialize loaders
#         report_loader = ReportDataLoader(bq_manager)
#         location_loader = LocationDataLoader(bq_manager)
#         distance_loader = DistanceDataLoader(bq_manager)
#         rate_lane_loader = RateLaneMappingLoader(bq_manager)

#         # Fetch and process report data
#         report_loader.fetch_data(trigger_dt, door_cy_value, hazmat, customer_nominated_trucker, drop_and_pick, node_cd)
#         report_loader.process_data()
#         self.report_data = report_loader.get_data()

#         # Fetch and process location data
#         location_loader.fetch_data()
#         location_loader.process_data()
#         self.location_data = location_loader.get_data()

#         # Fetch and process distance data
#         distance_loader.fetch_data()
#         distance_loader.process_data()
#         self.distance_data = distance_loader.get_data()

#         # Fetch and process rate lane mapping data
#         rate_lane_loader.fetch_data()
#         rate_lane_loader.process_data()
#         self.rl_mapping_data = rate_lane_loader.get_data()
#         self.df_rl_mapping_data = self.indexing_rate_lane_mapping()
#         self.gmaps_manager = GMapsManager()

#     def find_optimal_cost_saving(self, node_cd: str, import_loc_cd: str, export_loc_cd: str) -> float | None:
#         """Find the optimal cost saving for a street turn between locations.

#         Args:
#             node_cd: Node code
#             import_loc_cd: Import location code
#             export_loc_cd: Export location code

#         Returns:
#             float | None: Optimal cost saving amount or None if no valid matches
#         """
#         # Get relevant rate lane mappings using index
#         try:
#             # First try exact match on import and export location
#             idx = pd.IndexSlice
#             matched_routes = self.df_rl_mapping_data.loc[idx[import_loc_cd, export_loc_cd], :]
#             # Then filter by port code prefix
#             matched_routes = matched_routes[matched_routes["port_cd"].str.startswith(node_cd[:5])]
#         except KeyError:
#             # If no matching index found
#             matched_routes = pd.DataFrame()

#         if matched_routes.empty:
#             return None

#         # Calculate total costs and filter for street turn conditions
#         matched_routes = matched_routes.assign(total_cost=lambda df: df["import_base_rate"] + df["export_base_rate"])

#         street_turns = matched_routes[
#             (matched_routes["import_trip_type"] == "RETURN")
#             & (matched_routes["export_trip_type"] == "ONE WAY")
#             & (matched_routes["import_vendor"] == matched_routes["export_vendor"])
#         ]

#         if street_turns.empty:
#             return None

#         # Find round trips with the same vendors as in street_turns
#         round_trips = matched_routes[
#             (matched_routes["import_trip_type"] == "RETURN")
#             & (matched_routes["export_trip_type"] == "RETURN")
#             & (matched_routes["import_vendor"].isin(street_turns["import_vendor"]))
#         ]

#         if round_trips.empty:
#             return None

#         # Calculate cost savings for each vendor and find maximum
#         max_cost_save = None
#         for vendor in street_turns["import_vendor"].unique():
#             vendor_street_turn_cost = street_turns[street_turns["import_vendor"] == vendor]["total_cost"].min()
#             vendor_round_trips = round_trips[round_trips["import_vendor"] == vendor]

#             if not vendor_round_trips.empty:
#                 vendor_round_trip_cost = vendor_round_trips["total_cost"].min()
#                 cost_save = vendor_round_trip_cost - vendor_street_turn_cost

#                 max_cost_save = cost_save if max_cost_save is None else max(max_cost_save, cost_save)

#         return max_cost_save

#     @measure_time(logger=logger)
#     def find_new_pairs(self, node_codes: tuple[str, ...]) -> list[StreetTurnPair]:
#         """Find new pairs from report data that don't exist in distance data.

#         Args:
#             node_codes: List of node codes to process

#         Returns:
#             List of new StreetTurnPair objects
#         """
#         try:
#             new_pairs = []
#             for node_cd in node_codes:
#                 df_report = self.report_data
#                 df_report = df_report[df_report["interchange_location"] == node_cd]

#                 lst_imports = df_report[df_report["bound"] == "IMPORT"]
#                 lst_imports = lst_imports.drop_duplicates(subset=["location_city"])

#                 lst_exports = df_report[df_report["bound"] == "EXPORT"]
#                 lst_exports = lst_exports.drop_duplicates(subset=["location_city"])

#                 pairs = list(itertools.product(lst_imports.index, lst_exports.index))

#                 logger.info(
#                     f"Found {len(lst_imports)} unique import locations and {len(lst_exports)} unique export locations for node {node_cd}"
#                 )

#                 for im, ex in tqdm(pairs):
#                     port = self.report_data.loc[im, "interchange_location_city"]
#                     if self.location_data is None:
#                         raise ValueError("Location data is not loaded. Call load_data() first.")
#                     im_loc_cd = find_key_by_location_name(
#                         self.report_data.loc[im, "location_city"],
#                         self.location_data,
#                     )
#                     ex_loc_cd = find_key_by_location_name(
#                         self.report_data.loc[ex, "location_city"],
#                         self.location_data,
#                     )
#                     im_address = self.report_data.loc[im, "street_address"]  # noqa: F841
#                     ex_address = self.report_data.loc[ex, "street_address"]  # noqa: F841

#                     matching_distances = self.distance_data[
#                         (self.distance_data["port_cd"] == node_cd)
#                         & (self.distance_data["import_loc_cd"] == im_loc_cd)
#                         & (self.distance_data["export_loc_cd"] == ex_loc_cd)
#                         # & (self.df_distance["import_street_address"] == im_address)
#                         # & (self.df_distance["export_street_address"] == ex_address)
#                     ]

#                     if matching_distances.empty:
#                         distance_port_import, _ = self.gmaps_manager.get_distance_duration(
#                             origin=port,
#                             destination=self.location_data[im_loc_cd]["location_name"],
#                         )

#                         distance_port_export, _ = self.gmaps_manager.get_distance_duration(
#                             origin=port,
#                             destination=self.location_data[ex_loc_cd]["location_name"],
#                         )

#                         distance_door_door, _ = self.gmaps_manager.get_distance_duration(
#                             origin=self.location_data[im_loc_cd]["location_name"],
#                             destination=self.location_data[ex_loc_cd]["location_name"],
#                         )

#                         port_import = float(distance_port_import) if distance_port_import is not None else None
#                         port_export = float(distance_port_export) if distance_port_export is not None else None
#                         distance_saved = (port_import * 2 + port_export * 2) - (
#                             port_import + port_export + distance_door_door
#                         )

#                         # Handle potential None values for location codes
#                         if im_loc_cd is not None and ex_loc_cd is not None:
#                             max_cost_save = self.find_optimal_cost_saving(
#                                 node_cd=node_cd,
#                                 import_loc_cd=im_loc_cd,
#                                 export_loc_cd=ex_loc_cd,
#                             )
#                         else:
#                             max_cost_save = None

#                         new_pairs.append(
#                             StreetTurnPair(
#                                 PORT_CD=node_cd,
#                                 IMP_LOC_CD=im_loc_cd,
#                                 XPT_LOC_CD=ex_loc_cd,
#                                 IMP_ST_ADDR=im_loc_cd,
#                                 XPT_ST_ADDR=ex_loc_cd,
#                                 DIST_PORT_IMP=port_import,
#                                 DIST_PORT_XPT=port_export,
#                                 DIST_DOR_DOR=distance_door_door,
#                                 DIST_SAV=distance_saved,
#                                 MAX_COST_SAV=max_cost_save,
#                             )
#                         )
#             return new_pairs

#         except Exception as e:
#             logger.error(f"Error finding new pairs: {str(e)}")
#             raise

#     @measure_time(logger=logger)
#     def update_street_turn_db(self, node_codes: tuple[str, ...]) -> None:
#         """Update street turn database with new pairs.

#         Args:
#             trigger_dt: Datetime to filter report data
#             node_codes: Optional list of node codes to filter by
#         """
#         try:
#             # Find new pairs using node codes
#             new_pairs = self.find_new_pairs(node_codes)

#             if not new_pairs:
#                 logger.info("No new pairs found to add")
#                 return

#             # Convert to DataFrame for upload
#             pairs_df = pd.DataFrame([pair.model_dump() for pair in new_pairs])

#             # # Upload to BigQuery
#             upload_to_bq(
#                 df=pairs_df,
#                 project_id=settings.ENV.PROJECT_ID,
#                 dataset_id=settings.DB.DE_OUT_DATASET_ID,
#                 table_id=settings.DB.DIST_TABLE_ID,
#                 method="append",
#             )

#             logger.info(f"Successfully added {len(new_pairs)} new pairs to street turn database")

#         except Exception as e:
#             logger.error(f"Error updating street turn database: {str(e)}")
#             raise

#     def indexing_rate_lane_mapping(self) -> pd.DataFrame:
#         """Initialize rate lane mapping data with optimized index structure.

#         Returns:
#             pd.DataFrame: Rate lane mapping data with multi-index
#         """
#         try:
#             # Create RateLaneMappingLoader instance
#             df = self.rl_mapping_data
#             # Create multi-index for faster lookups
#             df = df.set_index(["import_loc_cd", "export_loc_cd"])
#             df = df.sort_index()  # Sort index for faster access

#             logger.info("Rate lane mapping data initialized with optimized index")
#             return df
#         except Exception as e:
#             logger.error(f"Error initializing rate lane mapping data: {str(e)}")
#             raise


# if __name__ == "__main__":
#     # Initialize the BigQuery manager and loaders
#     bq_manager = BigQueryManager()
#     report_loader = ReportDataLoader(bq_manager)
#     location_loader = LocationDataLoader(bq_manager)
#     distance_loader = DistanceDataLoader(bq_manager)

#     # Example parameters
#     trigger_dt = datetime.utcnow()
#     door_cy_value = "DOOR"
#     hazmat = "N"
#     customer_nominated_trucker = False
#     drop_and_pick = None
#     node_cd = ("USATL63",)

#     # Create updater - this will fetch and process all data
#     updater = StreetTurnDBUpdater(
#         bq_manager,
#         trigger_dt,
#         door_cy_value,
#         hazmat,
#         customer_nominated_trucker,
#         drop_and_pick,
#         node_cd,
#     )

#     # Update street turn database
#     updater.update_street_turn_db(node_codes=["USATL63"])
