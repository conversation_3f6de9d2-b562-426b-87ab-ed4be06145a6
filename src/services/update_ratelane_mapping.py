from datetime import datetime

import numpy as np
import pandas as pd
from pydantic import BaseModel, Field

from src.const import get_settings
from src.data_loader.rl_loader import RateLaneLoader
from src.thirdparty.gcp import BigQueryManager
from src.utils.decorators import measure_time
from src.utils.logger import log_handler
from src.utils.preprocess import upload_to_bq

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class RateLanePair(BaseModel):
    """Schema for a rate lane pair."""

    PORT_CD: str = Field(description="Port code")
    IMP_LOC_CD: str = Field(description="Import location code")
    XPT_LOC_CD: str = Field(description="Export location code")
    IMP_BSE_RT: float = Field(description="Base rate for import")
    XPT_BSE_RT: float = Field(description="Base rate for export")
    IMP_TRP_TP: str = Field(description="Trip type for import")
    XPT_TRP_TP: str = Field(description="Trip type for export")
    IMP_VNDR: str = Field(description="Vendor code for import")
    XPT_VNDR: str = Field(description="Vendor code for export")
    IMP_LANE_DESC: str = Field(description="Lane description for import")
    XPT_LANE_DESC: str = Field(description="Lane description for export")
    IMP_CRR_CD: str = Field(description="Carrier code for import")
    XPT_CRR_CD: str = Field(description="Carrier code for export")
    CRE_AT: datetime = Field(default_factory=datetime.utcnow)
    UPD_AT: datetime = Field(default_factory=datetime.utcnow)
    DELT_AT: datetime | None = Field(default=None)


class RateLaneUpdater:
    """Service for updating rate lane mapping in BigQuery."""

    def __init__(self, bq_manager: BigQueryManager, trigger_dt: datetime, node_cds: tuple[str, ...] = ()):
        """Initialize RateLaneUpdater.

        Args:
            bq_manager: Instance of BigQueryManager
            trigger_dt: Current datetime for filtering data
            node_cds: Tuple of node codes to filter by
        """
        self.bq_manager = bq_manager
        self.trigger_dt = trigger_dt
        self.node_cds = tuple(node[:5] for node in node_cds)  # Truncate to 5 characters
        self.rate_lane_data = None

    async def initialize(self) -> None:
        """Initialize the rate lane data asynchronously."""
        rate_lane_loader = RateLaneLoader(bq_manager=self.bq_manager)
        await rate_lane_loader.fetch_data(self.trigger_dt, self.node_cds)
        await rate_lane_loader.process_data()
        self.rate_lane_data = await rate_lane_loader.get_data()

    @staticmethod
    def determine_bound(row: pd.Series) -> str:
        """Determine if a rate lane is for export or import.

        Args:
            row: DataFrame row containing lane information

        Returns:
            str: "export", "import", or "n/a"
        """
        lane_desc = row["lane_description"]
        origin_loc = row["origin_location_code"]
        dest_loc = row["destination_location_code"]

        parts = lane_desc.split("_")
        if len(parts) != 3:
            return "n/a"

        middle_value = parts[1][:5]

        if middle_value == origin_loc:
            return "export"
        elif middle_value == dest_loc:
            return "import"
        return "n/a"

    @measure_time(logger=logger)
    def generate_pairs(self, df_rl: pd.DataFrame, node_cd: str) -> pd.DataFrame:
        """Generate pairs from the rate lane DataFrame.

        Args:
            df_rl: DataFrame containing rate lane data
            node_cd: Node code to filter by

        Returns:
            pd.DataFrame: Generated pairs with rate information
        """
        try:
            # Convert column names to lowercase
            df_rl.columns = df_rl.columns.str.lower()

            # Determine export/import
            df_rl["bound"] = df_rl.apply(self.determine_bound, axis=1)
            df_rl["port_cd"] = df_rl["lane_description"].str.split("_").str[0]
            df_rl["loc_cd"] = df_rl["lane_description"].str.split("_").str[1].str[:5]

            # Filter by the provided node_cd
            df_rl = df_rl[df_rl["port_cd"].str.startswith(node_cd)]

            # Separate imports and exports
            df_rl_import = df_rl[df_rl["bound"] == "import"]
            df_rl_export = df_rl[df_rl["bound"] == "export"]

            logger.info(f"Found {len(df_rl_import)} imports and {len(df_rl_export)} exports")

            # Create pairs using merge
            # Use pandas cross merge which is more efficient than the dummy key approach
            df_pairs = pd.merge(df_rl_import, df_rl_export, how="cross", suffixes=("_import", "_export"))

            # Process and clean the pairs
            df_pairs["base_rate_import"] = df_pairs["base_rate_import"].astype(float).round(2)
            df_pairs["base_rate_export"] = df_pairs["base_rate_export"].astype(float).round(2)

            # Convert vendor codes and carrier codes to strings
            string_columns = ["vendor_cd_import", "vendor_cd_export", "carrier_cd_import", "carrier_cd_export"]
            for col in string_columns:
                df_pairs[col] = df_pairs[col].astype(str)

            # Select and rename columns
            result_df = df_pairs[
                [
                    "port_cd_import",
                    "loc_cd_import",
                    "loc_cd_export",
                    "base_rate_import",
                    "base_rate_export",
                    "trip_type_import",
                    "trip_type_export",
                    "vendor_cd_import",
                    "vendor_cd_export",
                    "lane_description_import",
                    "lane_description_export",
                    "carrier_cd_import",
                    "carrier_cd_export",
                ]
            ].rename(
                columns={
                    "port_cd_import": "PORT_CD",
                    "loc_cd_import": "IMP_LOC_CD",
                    "loc_cd_export": "XPT_LOC_CD",
                    "base_rate_import": "IMP_BSE_RT",
                    "base_rate_export": "XPT_BSE_RT",
                    "trip_type_import": "IMP_TRP_TP",
                    "trip_type_export": "XPT_TRP_TP",
                    "vendor_cd_import": "IMP_VNDR",
                    "vendor_cd_export": "XPT_VNDR",
                    "lane_description_import": "IMP_LANE_DESC",
                    "lane_description_export": "XPT_LANE_DESC",
                    "carrier_cd_import": "IMP_CRR_CD",
                    "carrier_cd_export": "XPT_CRR_CD",
                }
            )

            # Remove duplicates and add timestamp
            result_df = result_df.drop_duplicates()
            result_df["created_at"] = datetime.utcnow()

            logger.info(f"Generated {len(result_df)} unique rate lane pairs")
            return result_df

        except Exception as e:
            logger.error(f"Error generating rate lane pairs: {str(e)}")
            raise

    @measure_time(logger=logger)
    async def update_rate_lane_table(self, node_cd: tuple[str, ...]) -> None:
        """Update the rate lane table in BigQuery."""
        try:
            if self.rate_lane_data is None:
                await self.initialize()

            if not isinstance(self.rate_lane_data, pd.DataFrame):
                raise ValueError("Rate lane data initialization failed")

            df_rl = self.rate_lane_data
            logger.info(f"Loaded {len(df_rl)} rate lane records")

            # Generate pairs
            # Create an empty list to store dataframes for each node code
            all_validated_dfs = []

            # Process each node code separately
            for node in node_cd:
                logger.info(f"Processing node: {node}")
                pairs_df = self.generate_pairs(df_rl, node[:5])

                if not pairs_df.empty:
                    # Validate data through Pydantic
                    validated_pairs = []
                    for record in pairs_df.to_dict("records"):
                        # Convert numeric types to Python native types for Pydantic
                        cleaned_record = {}
                        for k, v in record.items():
                            if isinstance(v, np.floating | float):
                                cleaned_record[str(k)] = float(v)
                            elif isinstance(v, np.integer | int):
                                cleaned_record[str(k)] = int(v)
                            else:
                                cleaned_record[str(k)] = v

                        validated_pairs.append(RateLanePair(**cleaned_record).dict())

                    node_validated_df = pd.DataFrame(validated_pairs)
                    all_validated_dfs.append(node_validated_df)
                else:
                    logger.warning(f"No pairs generated for node {node}")

            # Combine all dataframes
            if all_validated_dfs:
                validated_df = pd.concat(all_validated_dfs, ignore_index=True)
                logger.info(f"Combined {len(validated_df)} validated records across all nodes")
            else:
                validated_df = pd.DataFrame()
                logger.warning("No valid pairs generated for any nodes")

            # Upload to BigQuery using pandas_gbq
            upload_to_bq(
                validated_df,
                project_id=settings.ENV.PROJECT_ID,
                dataset_id=settings.DB.DE_OUT_DATASET_ID,
                table_id=settings.DB.RATELANE_TABLE_MAPPING_ID,
                method="replace",
            )

        except Exception as e:
            logger.error(f"Error updating rate lane table: {str(e)}")
            raise


if __name__ == "__main__":

    async def main() -> None:
        """Run the rate lane update process."""
        # Initialize the BigQuery manager and rate lane loader
        bq_manager = BigQueryManager()

        # Example node codes and trigger datetime
        trigger_dt = datetime.utcnow()
        node_cds = ("USATL63",)  # Example node code

        # Create updater and initialize
        updater = RateLaneUpdater(bq_manager, trigger_dt, node_cds)
        await updater.initialize()

        # Update rate lane mapping with custom ports
        await updater.update_rate_lane_table(node_cds)

    # Run the async main function
    import asyncio

    asyncio.run(main())
