import asyncio
import datetime as dt
import pickle
import tempfile
import uuid
from datetime import datetime
from pathlib import Path
from typing import cast

import aiofiles
import pandas as pd

from root import CACHE_DIR, RUN_DIR
from src.const import get_settings
from src.const.col_map import CANDIDATE_PAIRS_SCHEMA
from src.thirdparty.gcp import BigQueryManager
from src.utils.decorators import measure_time
from src.utils.logger import log_handler

from .distance_service import get_cached_distance, upload_distance_cache
from .lp_service import LinearProgrammingOptimizer
from .score_service import calculate_pair_scores

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


def safe_sum(series: pd.Series) -> float:
    """Safely sum a series, handling potential type issues."""
    try:
        return float(series.sum())
    except (TypeError, AttributeError):
        return 0.0


def get_matching_metrics(result_df: pd.DataFrame, optimal_mask: pd.Series) -> list[dict]:
    """Calculate metrics from matching results."""
    tmp_df = result_df[optimal_mask].copy()
    metrics = (
        tmp_df.groupby("cost_formula")
        .agg(
            total_cost_save=("cost_save", safe_sum),
            total_distance_save=("dist_sav_km", safe_sum),
            container_reuse=("imp_cntr_no", "nunique"),
        )
        .reset_index()
    )
    return metrics.to_dict(orient="records") if not metrics.empty else []


def calculate_time_gap(row: pd.Series) -> int:
    """Calculate the number of days between export and import dates.

    Computes the time gap between when an import container becomes available
    and when it needs to be at the export location.

    Args:
        row (pd.Series): Row containing export_first_receiving_date and
                        import_estimated_delivery_date

    Returns:
        int: number of hours between the two dates
    """
    gap = cast(pd.Timestamp, row["xpt_first_receiving_date"]) - cast(pd.Timestamp, row["imp_estimated_delivery_date"])
    gap = gap.total_seconds() / 3600.0
    return round(gap)


class StreetTurnMatchingService:
    """Service for optimizing container reuse through street-turn matching."""

    def __init__(self, bg_manager):
        """Initialize the matching service."""
        self.bq_manager: BigQueryManager = bg_manager
        self.lp_optimizer = LinearProgrammingOptimizer()
        logger.info(f"{self.__class__.__name__} initialized")

    @measure_time(logger=logger)
    def setup(self, trigger_timestamp: dt.datetime) -> tuple[str, Path]:
        """Initialize a new matching run.

        Creates a uniquely identified temporary directory for storing matching results
        and attempts to use the configured temporary directory, falling back to
        system temp directory if needed.

        Args:
            trigger_timestamp (dt.datetime): Timestamp for the matching run

        Returns:
            tuple containing:
            - str: Unique identifier (UUID) for this matching run
            - Path: Path to created temporary directory
        """
        _id = str(uuid.uuid4())
        try:
            folder_name = f"matching_results_{trigger_timestamp.strftime('%Y%m%dT%H%M%S')}_{_id[:8]}"
            base_dir = RUN_DIR
            temp_dir = base_dir / folder_name
            temp_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created temporary result folder at: {temp_dir}")
            return _id, temp_dir
        except Exception as e:
            logger.error(f"Failed to create temporary folder: {e}")
            fallback_dir = tempfile.mkdtemp(prefix="matching_results_")
            logger.info(f"Using fallback temporary directory: {fallback_dir}")
            return _id, Path(fallback_dir)

    @measure_time(logger=logger)
    async def __filter_non_overlapping_imports_exports(
        self, report_data: pd.DataFrame, current_dt_str: str
    ) -> tuple[list[str], list[str]]:
        """Filter out import/export pairs with non-overlapping time windows.

        Args:
            report_data (pd.DataFrame): Combined import and export booking data
            current_dt_str (str): Current datetime in YYYY-MM-DDTHH:MM:SS format

        Returns:
            tuple: Lists of COP numbers for non-overlapping imports and exports
                (non_overlapping_imports, non_overlapping_exports)

        Note:
            Identifies bookings that cannot be paired due to:
            - Time window conflicts
            - Past delivery/cutoff dates
            - No temporal overlap between import delivery and export receiving
        """
        current_dt = datetime.strptime(current_dt_str, "%Y-%m-%dT%H:%M:%S")

        # Dictionary to store bookings
        bookings = {}

        # Process imports and exports
        for _, row in report_data.iterrows():
            if row["bound"] == "IMPORT":
                if current_dt > datetime.strptime(str(row["estimated_import_delivery_date"]), "%Y-%m-%d %H:%M:%S"):
                    continue
                bookings[row["cop_no"]] = [
                    datetime.strptime(str(row["import_availability_at_final_cy"]), "%Y-%m-%d %H:%M:%S"),
                    datetime.strptime(str(row["estimated_import_delivery_date"]), "%Y-%m-%d %H:%M:%S"),
                    0,  # Marker for import
                ]
            elif row["bound"] == "EXPORT":
                if current_dt > datetime.strptime(str(row["export_cut_off_date"]), "%Y-%m-%d %H:%M:%S"):
                    continue
                bookings[row["cop_no"]] = [
                    datetime.strptime(str(row["export_first_receiving_date"]), "%Y-%m-%d %H:%M:%S"),
                    datetime.strptime(str(row["export_cut_off_date"]), "%Y-%m-%d %H:%M:%S"),
                    1,  # Marker for export
                ]

        # Sort bookings by start date
        sorted_bookings = sorted(bookings.items(), key=lambda x: x[1][0])  # type: ignore [arg-type, return-value]

        # Separate into imports and exports
        imports = [b for b in sorted_bookings if b[1][2] == 0]
        exports = [b for b in sorted_bookings if b[1][2] == 1]

        # Filter non-overlapping imports and exports
        non_overlapping_imports = []
        non_overlapping_exports = []

        for imp in imports:
            if all(imp[1][1] <= exp[1][0] or imp[1][0] >= exp[1][1] for exp in exports):  # type: ignore [operator]
                non_overlapping_imports.append(imp[0])  # Append cop_no

        for exp in exports:
            if all(exp[1][1] <= imp[1][0] or exp[1][0] >= imp[1][1] for imp in imports):  # type: ignore [operator]
                non_overlapping_exports.append(exp[0])  # Append cop_no

        return non_overlapping_imports, non_overlapping_exports

    @measure_time(logger=logger)
    async def __create_candidate_pairs(self, report_preprocessing_data: pd.DataFrame) -> pd.DataFrame:
        """Generate all possible import-export pairs for matching and apply filters.

        Args:
            report_preprocessing_data: DataFrame containing import and export data

        Returns:
            DataFrame containing filtered and enriched pairs with distance calculations
        """
        # Match ports
        filtered_pairs = report_preprocessing_data[
            report_preprocessing_data["imp_port_cd"] == report_preprocessing_data["xpt_port_cd"]
        ].reset_index(drop=True)
        filtered_pairs = filtered_pairs.drop(columns=["xpt_port_cd"])
        filtered_pairs = filtered_pairs.rename(columns={"imp_port_cd": "port_cd"})

        # Calculate distances for valid pairs
        from src.api.dependencies import services

        distance_results = pd.DataFrame(
            [await self.__calculate_distances_async(row) for _, row in filtered_pairs.iterrows()]
        )
        await upload_distance_cache(services.distance_cache)

        # Combine pair data with distance calculations
        filtered_pairs = pd.concat([filtered_pairs, distance_results], axis=1)

        return filtered_pairs

    async def __calculate_distances_async(self, row: pd.Series) -> pd.Series:
        """Calculate all required distances for a candidate match pair asynchronously."""
        # Get the locations
        port_cd = row["port_cd"]
        port_addr = row["imp_port_city"]
        imp_loc_cd = row["imp_loc_cd"]
        imp_loc_addr = row["imp_loc_city"]
        xpt_loc_cd = row["xpt_loc_cd"]
        xpt_loc_addr = row["xpt_loc_city"]

        # Calculate distances with thread safety
        # Use a lock to ensure thread-safe access to the distance cache
        from src.api.dependencies import services

        async with asyncio.Lock():
            # Calculate all distances, collecting cache updates
            import_to_port, updated_cache1 = get_cached_distance(
                {"cd": imp_loc_cd, "addr": imp_loc_addr},
                {"cd": port_cd, "addr": port_addr},
                services.distance_cache,
            )
            services.distance_cache.update(updated_cache1)

            export_to_port, updated_cache2 = get_cached_distance(
                {"cd": xpt_loc_cd, "addr": xpt_loc_addr},
                {"cd": port_cd, "addr": port_addr},
                services.distance_cache,
            )
            services.distance_cache.update(updated_cache2)

            import_to_export, updated_cache3 = get_cached_distance(
                {"cd": imp_loc_cd, "addr": imp_loc_addr},
                {"cd": xpt_loc_cd, "addr": xpt_loc_addr},
                services.distance_cache,
            )
            services.distance_cache.update(updated_cache3)

        # Calculate route distances and savings
        round_trip_route_distance = import_to_port * 2 + export_to_port * 2
        street_turn_route_distance = import_to_port + import_to_export + export_to_port
        distance_savings = round_trip_route_distance - street_turn_route_distance

        return pd.Series(
            {
                "imp_to_port_dist_km": import_to_port,
                "xpt_to_port_dist_km": export_to_port,
                "imp_to_xpt_dist_km": import_to_export,
                "round_trip_route_dist_km": round_trip_route_distance,
                "street_turn_route_dist_km": street_turn_route_distance,
                "dist_sav_km": distance_savings,
            }
        )

    @measure_time(logger=logger)
    async def create_all_matches(self, match_id: str, current_dt: dt.datetime, data_collection: dict) -> pd.DataFrame:
        """Create all possible matches between import deliveries and export pickups."""
        # Create candidate pairs
        all_pairs = await self.__create_candidate_pairs(
            data_collection["report_preprocessing_data"],
        )

        if all_pairs.empty:
            logger.info("No valid pairs found after compatibility filtering")
            return pd.DataFrame()

        # Save pairs to BigQuery
        all_pairs["match_id"] = match_id
        all_pairs["trigger_timestamp"] = current_dt

        # Convert timestamp columns to datetime
        timestamp_columns = [
            "imp_availability_at_final_cy",
            "imp_estimated_delivery_date",
            "xpt_first_receiving_date",
            "xpt_cut_off_date",
        ]
        for col in timestamp_columns:
            all_pairs[col] = pd.to_datetime(all_pairs[col])

        port_cd = all_pairs["port_cd"].iloc[0][:5]
        table_id = f"{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.POSSIBLE_CANDIDATE_PAIRS_TABLE_ID}"

        await self.bq_manager.upsert_dataframe(
            dataframe=all_pairs,
            table_id=f"{table_id}_{port_cd}",
            schema=CANDIDATE_PAIRS_SCHEMA,
            if_exists="replace",
        )
        await self.bq_manager.upsert_dataframe(
            dataframe=all_pairs,
            table_id=table_id + "_HIS",
            schema=CANDIDATE_PAIRS_SCHEMA,
            if_exists="append",
        )

        return all_pairs

    async def find_matches(
        self, max_distance_thres_km: float, time_tolerance_thres: int, all_pairs: pd.DataFrame
    ) -> list[dict]:
        """Find optimal matches using linear programming optimization.

        Applies final distance and time filters, then uses linear programming to
        find the optimal set of matches that maximizes total savings while ensuring
        each import and export is matched at most once.

        Args:
            max_distance_thres_km (float): Maximum allowed distance between locations
            time_tolerance_thres (int): Maximum allowed days between delivery and pickup
            all_pairs (pd.DataFrame): Candidate pairs to optimize

        Returns:
            list[dict]: Optimal matches, each containing pair details and metrics
        """
        return await self.lp_optimizer.find_matches(max_distance_thres_km, time_tolerance_thres, all_pairs)

    async def lookup_rl_mapping(self, df, rl_mapping):
        """Lookup the rate lane mapping for vendor and location codes in the DataFrame.

        Adds import and export prefixes to matched data.

        Args:
            df (pd.DataFrame): DataFrame containing 'vendor_cd', 'loc_cd_imp', 'loc_cd_xpt' columns.
            rl_mapping (pd.DataFrame): Rate lane mapping DataFrame with 'VNDR_CD', 'LOC_CD' columns.

        Returns:
            pd.DataFrame: DataFrame with additional columns from the rate lane mapping.
        """
        # First merge for import data
        merged_df = pd.merge(
            df, rl_mapping, left_on=["import_vendor_cd", "import_loc_cd"], right_on=["VNDR_CD", "LOC_CD"], how="left"
        )

        # Add import prefix to columns from rl_mapping
        for col in rl_mapping.columns:
            if col not in ["VNDR_CD", "LOC_CD"]:
                merged_df = merged_df.rename(columns={col: f"import_{col.lower()}"})

        # Drop duplicate columns
        merged_df = merged_df.drop(columns=["VNDR_CD", "LOC_CD"])

        # Second merge for export data
        merged_df = pd.merge(
            merged_df,
            rl_mapping,
            left_on=["export_vendor_cd", "export_loc_cd"],
            right_on=["VNDR_CD", "LOC_CD"],
            how="left",
        )

        # Add export prefix to columns from rl_mapping
        for col in rl_mapping.columns:
            if col not in ["VNDR_CD", "LOC_CD"]:
                merged_df = merged_df.rename(columns={col: f"export_{col.lower()}"})

        # Drop duplicate columns
        merged_df = merged_df.drop(columns=["VNDR_CD", "LOC_CD"])

        return merged_df

    async def __merge_result(
        self,
        node_cd: str,
        imp_loc: str,
        xpt_loc: str,
        imp_xpt_dist: float,
        st_table: pd.DataFrame,
        rt_table: pd.DataFrame,
        rl_mapping: pd.DataFrame,
        is_optimal: bool,
        viap_priority: bool = False,
        cost_formula: str = "FORMULA_2",
        imp_scg_percentage: float = 0.5,
        xpt_scg_percentage: float = 0.5,
    ) -> pd.DataFrame:
        def _get_street_turn_rates(st_table: pd.DataFrame, imp_loc: str, xpt_loc: str) -> pd.DataFrame:
            """Get street turn rates from the street turn table."""
            try:
                imp_df = st_table.loc[
                    (slice(None), imp_loc),
                    ["import_base_rate_return", "import_base_rate_one_way", "surcharge_street_turn_one_way"],
                ].reset_index()
                xpt_df = st_table.loc[
                    (slice(None), xpt_loc), ["export_base_rate_one_way", "surcharge_street_turn_one_way"]
                ].reset_index()
            except KeyError as e:
                logger.warning(f"Location not found in street turn table: {str(e)}")
                return pd.DataFrame()

            street_turn = pd.merge(imp_df, xpt_df, on="vendor_cd", suffixes=("_imp", "_xpt"))
            street_turn.rename(
                columns={
                    "vendor_cd": "import_vendor_cd",
                    "loc_cd_imp": "import_loc_cd",
                    "surcharge_street_turn_one_way_imp": "import_surcharge_street_turn_one_way",
                    "loc_cd_xpt": "export_loc_cd",
                    "surcharge_street_turn_one_way_xpt": "export_surcharge_street_turn_one_way",
                },
                inplace=True,
            )
            return street_turn

        def _calculate_round_trip_rates(
            rt_table: pd.DataFrame, street_turn: pd.DataFrame, cost_formula: str, imp_loc: str, xpt_loc: str
        ) -> pd.DataFrame:
            """Calculate round trip rates based on the cost formula."""
            formula_configs = {
                "FORMULA_1": {
                    "columns": ["import_vendor_cd", "import_loc_cd", "export_loc_cd"],
                    "needs_import_rate": True,
                    "needs_export_rate": True,
                },
                "FORMULA_2": {
                    "columns": ["import_vendor_cd", "import_loc_cd", "export_loc_cd", "import_base_rate_return"],
                    "needs_import_rate": False,
                    "needs_export_rate": True,
                },
                "FORMULA_3": {
                    "columns": [
                        "import_vendor_cd",
                        "import_loc_cd",
                        "export_loc_cd",
                        "import_base_rate_return",
                        "export_base_rate_return",
                    ],
                    "needs_import_rate": False,
                    "needs_export_rate": False,
                },
                "FORMULA_4": {
                    "columns": ["import_vendor_cd", "import_loc_cd", "export_loc_cd", "import_base_rate_return"],
                    "needs_import_rate": False,
                    "needs_export_rate": True,
                },
            }

            config = formula_configs.get(cost_formula)
            if not config:
                logger.error(f"Invalid cost formula: {cost_formula}")
                return pd.DataFrame()

            round_trip = street_turn[config["columns"]].copy()

            if config["needs_import_rate"]:
                try:
                    round_trip["import_base_rate_return"] = rt_table.loc[imp_loc]["min_import_base_rate"]
                except KeyError:
                    logger.warning(f"Import location {imp_loc} not found in round trip table.")
                    return pd.DataFrame()

            if config["needs_export_rate"]:
                try:
                    round_trip["export_vendor_cd"] = rt_table.loc[xpt_loc]["min_export_vendor_cd"]
                    round_trip["export_base_rate_return"] = rt_table.loc[xpt_loc]["min_export_base_rate"]
                except KeyError:
                    logger.warning(f"Export location {xpt_loc} not found in round trip table.")
                    return pd.DataFrame()

            round_trip["total_cost"] = round_trip["import_base_rate_return"] + round_trip["export_base_rate_return"]
            return round_trip

        # Get street turn rates
        street_turn = _get_street_turn_rates(st_table, imp_loc, xpt_loc)
        if street_turn.empty:
            return pd.DataFrame()

        # Calculate street turn total cost
        if cost_formula in ["FORMULA_1", "FORMULA_2", "FORMULA_3"]:
            street_turn["total_cost"] = street_turn["import_base_rate_return"] + street_turn["export_base_rate_one_way"]
        if cost_formula == "FORMULA_4":
            street_turn["total_cost"] = (
                street_turn["import_base_rate_one_way"]
                + street_turn["export_base_rate_one_way"]
                + street_turn["import_surcharge_street_turn_one_way"] * imp_xpt_dist * imp_scg_percentage
                + street_turn["export_surcharge_street_turn_one_way"] * imp_xpt_dist * xpt_scg_percentage
            )
        street_turn["total_cost"] = street_turn["total_cost"].round(2)

        # Calculate round trip rates
        round_trip = _calculate_round_trip_rates(rt_table, street_turn, cost_formula, imp_loc, xpt_loc)
        if round_trip.empty:
            return pd.DataFrame()

        # Merge results
        final_result = pd.merge(
            street_turn,
            round_trip,
            on=["import_vendor_cd", "import_loc_cd", "export_loc_cd"],
            suffixes=("_street_turn", "_round_trip"),
        )

        # Rename columns for clarity
        final_result = final_result.rename(
            columns={
                "total_cost_street_turn": "street_turn_total_cost",
                "total_cost_round_trip": "round_trip_total_cost",
            }
        )

        # Select and arrange relevant columns
        final_result = final_result[
            [
                "import_loc_cd",
                "export_loc_cd",
                "import_vendor_cd",
                "export_vendor_cd",
                "street_turn_total_cost",
                "round_trip_total_cost",
                # "import_base_rate_return_street_turn",
                # "import_base_rate_one_way",
                # "import_surcharge_street_turn_one_way",
                # "export_base_rate_one_way",
                # "export_surcharge_street_turn_one_way",
                # "import_base_rate_return_round_trip",
                # "export_base_rate_return",
            ]
        ]

        # Calculate cost savings
        final_result["cost_save"] = final_result["round_trip_total_cost"] - final_result["street_turn_total_cost"]

        # Determine optimal matches
        max_save = final_result["cost_save"].max()
        if max_save > 0:
            mask = final_result["cost_save"] == max_save
            min_cost = final_result[mask]["street_turn_total_cost"].min()
            final_result["optimal"] = mask & (final_result["street_turn_total_cost"] == min_cost)
        elif max_save == 0:
            min_cost = final_result["street_turn_total_cost"].min()
            final_result["optimal"] = final_result["street_turn_total_cost"] == min_cost
        else:
            final_result["optimal"] = False

        # Handle optimal flag from parameters
        final_result_filtered = final_result.copy()
        if not is_optimal:
            final_result_filtered = final_result_filtered[final_result_filtered["optimal"]]
            final_result_filtered["optimal"] = False

        # Clean and merge with rate lane mapping
        df_merge = final_result_filtered.dropna()
        # df_merge = await self.lookup_rl_mapping(df_merge, rl_mapping)

        # Select final columns and add vendor information
        df_merge["street_turn_vndr"] = df_merge["import_vendor_cd"]
        df_merge["round_trip_imp_vndr"] = df_merge["import_vendor_cd"]
        df_merge["round_trip_xpt_vndr"] = df_merge["export_vendor_cd"]
        df_merge["cost_formula"] = cost_formula
        df_merge = df_merge[
            [
                "import_loc_cd",
                "export_loc_cd",
                "street_turn_vndr",
                "street_turn_total_cost",
                "round_trip_imp_vndr",
                "round_trip_xpt_vndr",
                "round_trip_total_cost",
                "cost_save",
                "optimal",
                "cost_formula",
            ]
        ]
        return df_merge

    @measure_time(logger=logger)
    async def find_actual_route(
        self,
        matches: list[dict],
        ratelane_mapping_data: dict[str, pd.DataFrame],
        street_turn_cost_options: list[str],
        ranking_priorities: list[dict],
        dev_mode: bool = False,
        ranking_coefficients: tuple[float, ...] = (0.25, 0.25, 0.25, 0.25),
    ) -> tuple[pd.DataFrame, list[dict]]:
        """Find optimal routing solutions for matched import-export pairs.

        Args:
            matches: List of matched pairs
            ratelane_mapping_data: Rate lane mapping data
            street_turn_cost_options: List of street turn cost options.
            ranking_priorities: List of ranking criteria names.
            dev_mode: If True, uses a simplified scoring method.
            ranking_coefficients: Coefficients for scoring in dev mode.

        Returns:
            tuple: A tuple containing:
                - pd.DataFrame with optimal matches and their metrics
                - List of dictionaries with cost formula and savings for each option
        """
        if not matches:
            return pd.DataFrame(), [
                {
                    "cost_formula": _,
                    "total_cost_save": 0.0,
                    "total_distance_save": 0.0,
                    "container_reuse": 0,
                }
                for _ in street_turn_cost_options
            ]

        # Convert matches to pd.DataFrame if not already
        matches_df = pd.DataFrame(matches) if not isinstance(matches, pd.DataFrame) else matches

        # Get optimal matches from the is_optimal flag set by linear programming
        optimal_matches = matches_df[matches_df["is_optimal"]]
        optimal_match_keys = set(zip(optimal_matches["imp_cop_no"], optimal_matches["xpt_cop_no"], strict=True))

        # Create a dictionary to store results - more efficient than concatenation
        results = []

        # Create a cache for merge results to avoid redundant calculations
        merge_result_cache = {}
        cache_file = CACHE_DIR / "merge_result_cache.pkl"

        # Load existing cache if available
        if cache_file.exists():
            logger.info(f"Loading merge result cache from {cache_file}")
            try:
                async with aiofiles.open(cache_file, mode="rb") as f:
                    content = await f.read()
                    merge_result_cache = pickle.loads(content)  # noqa: S301
                logger.info(f"Loaded merge result cache with {len(merge_result_cache)} entries")
            except Exception as e:
                logger.error(f"Failed to load merge result cache: {e}")
                merge_result_cache = {}

        # Process each match with async operations
        tasks = []
        for _, match in matches_df.iterrows():
            port_prefix = match["port_cd"][:5]
            imp_loc = match["imp_loc_cd"]
            xpt_loc = match["xpt_loc_cd"]
            match_key = (match["imp_cop_no"], match["xpt_cop_no"])
            is_optimal_match = match_key in optimal_match_keys

            # Try each cost formula
            for formula in street_turn_cost_options:
                # Use cache key for merge results that includes formula
                cache_key = (port_prefix, imp_loc, xpt_loc, formula)

                # Check if we already calculated this result
                if cache_key in merge_result_cache:
                    df_final = merge_result_cache[cache_key].copy()
                    if not df_final.empty:
                        tasks.append(self.__process_match_result(df_final, match, is_optimal_match))
                else:
                    # Create task for getting the matching results for this formula
                    tasks.append(
                        self.__process_new_match(
                            port_prefix,
                            imp_loc,
                            xpt_loc,
                            ratelane_mapping_data["rl_map"],
                            ratelane_mapping_data["st"],
                            ratelane_mapping_data["rt"],
                            match,
                            is_optimal_match,
                            merge_result_cache,
                            cache_key,
                            cost_formula=formula,
                        )
                    )

        # Wait for all tasks to complete
        processed_results = await asyncio.gather(*tasks)
        results.extend([r for r in processed_results if r is not None])

        # Save updated cache
        if merge_result_cache:
            async with aiofiles.open(cache_file, mode="wb") as f:
                pickled_data = pickle.dumps(merge_result_cache)
                await f.write(pickled_data)
            logger.info(f"Saved merge result cache with {len(merge_result_cache)} entries")

        if not results:
            return pd.DataFrame(), [
                {
                    "cost_formula": _,
                    "total_cost_save": 0.0,
                    "total_distance_save": 0.0,
                    "container_reuse": 0,
                }
                for _ in street_turn_cost_options
            ]

        result_df = pd.concat(results, ignore_index=True)

        # Process each formula's results separately
        formula_results = []
        for formula in street_turn_cost_options:
            formula_df = result_df[result_df["cost_formula"] == formula]
            if not dev_mode:
                prioritized_df = await self.__prioritize_rate_lane_matches(
                    formula_df, ranking_priorities=ranking_priorities
                )
            else:
                prioritized_df = await self.__set_optimal_by_score(
                    formula_df, ranking_coefficients=ranking_coefficients
                )
            formula_results.append(prioritized_df)

        # Combine all formula results
        result_df = pd.concat(formula_results, ignore_index=True)

        # Only drop columns that exist
        if not result_df.empty:
            meta = get_matching_metrics(result_df, result_df["optimal"])
            return result_df, meta

        return pd.DataFrame(), [
            {
                "cost_formula": _,
                "total_cost_save": 0.0,
                "total_distance_save": 0.0,
                "container_reuse": 0,
            }
            for _ in street_turn_cost_options
        ]

    async def __set_optimal_by_score(self, df: pd.DataFrame, ranking_coefficients: tuple[float, ...]):
        # Apply per group
        df = df.copy()
        df["optimal"] = False  # initialize
        grouped = df.groupby("imp_cop_no")

        result = []
        weights = {
            "time": ranking_coefficients[3],
            "distance": ranking_coefficients[2],
            "operation": ranking_coefficients[0],
            "saved": ranking_coefficients[1],
        }
        for imp_cop_no, group in grouped:
            group = calculate_pair_scores(group, weights)
            max_score_idx = group["score"].idxmax()
            group.loc[max_score_idx, "optimal"] = True
            result.append(group)

        return pd.concat(result, ignore_index=True)

    async def __prioritize_rate_lane_matches(
        self, df_merge: pd.DataFrame, ranking_priorities: list[dict]
    ) -> pd.DataFrame:
        """Apply multiple priority criteria to determine optimal matches.

        Args:
            df_merge (pd.DataFrame): DataFrame containing merged street turn and round trip data
            ranking_priorities (list[dict]): List of ranking criteria names, if None uses settings
        Returns:
            pd.DataFrame: Updated df_merge with optimal matches determined
        """
        # Group by imp_cop_no and apply criteria only for groups with no optimal
        grouped = df_merge.groupby("imp_cop_no")
        for imp_cop_no, group in grouped:
            if group["optimal"].sum() == 0:
                # Convert string columns to numeric
                group = group.copy()
                group["street_turn_total_cost"] = pd.to_numeric(group["street_turn_total_cost"])
                group["cost_save"] = pd.to_numeric(group["cost_save"])

                # Filter for positive savings
                positive_savings = group[group["cost_save"] > 0]
                if not positive_savings.empty:
                    candidates = positive_savings

                    # Apply each criterion in order
                    for criterion in ranking_priorities:
                        if criterion["type"] == "min":
                            min_val = candidates[criterion["column"]].min()
                            candidates = candidates[candidates[criterion["column"]] == min_val]

                        elif criterion["type"] == "max":
                            max_val = candidates[criterion["column"]].max()
                            candidates = candidates[candidates[criterion["column"]] == max_val]

                        elif criterion["type"] == "min_abs":
                            min_abs_val = candidates[criterion["column"]].abs().min()
                            candidates = candidates[candidates[criterion["column"]].abs() == min_abs_val]

                        # If only one candidate remains, set it as optimal and break
                        if len(candidates) == 1:
                            df_merge.loc[candidates.index[0], "optimal"] = True
                            break

                    # If multiple candidates remain after all criteria, pick one randomly
                    if len(candidates) > 1:
                        random_row_index = candidates.sample(n=1).index[0]
                        df_merge.loc[random_row_index, "optimal"] = True
                else:
                    # If no positive savings, pick a random row as optimal
                    random_row_index = group.sample(n=1).index[0]
                    df_merge.loc[random_row_index, "optimal"] = True

        return df_merge.reset_index(drop=True)

    async def __process_match_result(
        self, df_final: pd.DataFrame, match: pd.Series, is_optimal_match: bool
    ) -> pd.DataFrame | None:
        """Process a match result, whether from cache or newly calculated."""
        if df_final.empty:
            return None

        # Prepare match data once
        match_df = pd.DataFrame([match])

        # Use more efficient join
        merged_rows = pd.merge(match_df, df_final, how="cross").drop_duplicates()
        return merged_rows

    async def __process_new_match(
        self,
        port_prefix: str,
        imp_loc: str,
        xpt_loc: str,
        rl_mapping: pd.DataFrame,
        st_table: pd.DataFrame,
        rt_table: pd.DataFrame,
        match: pd.Series,
        is_optimal_match: bool,
        merge_result_cache: dict,
        cache_key: tuple,
        cost_formula: str = "FORMULA_2",
    ) -> pd.DataFrame | None:
        """Process a new match that isn't in the cache."""
        # Try different viap priorities for the given formula
        df_final = await self.__merge_result(
            port_prefix,
            imp_loc,
            xpt_loc,
            match["imp_to_xpt_dist_km"],
            st_table,
            rt_table,
            rl_mapping,
            is_optimal_match,
            viap_priority=True,
            cost_formula=cost_formula,
        )
        if df_final.empty:
            df_final = await self.__merge_result(
                port_prefix,
                imp_loc,
                xpt_loc,
                match["imp_to_xpt_dist_km"],
                st_table,
                rt_table,
                rl_mapping,
                is_optimal_match,
                viap_priority=False,
                cost_formula=cost_formula,
            )

        # Cache the result
        merge_result_cache[cache_key] = df_final.copy() if not df_final.empty else pd.DataFrame()

        if df_final.empty:
            logger.debug(
                f"No street turn and round trip matches found for {match['imp_cop_no']} and {match['xpt_cop_no']}"
            )
            return None

        return await self.__process_match_result(df_final, match, is_optimal_match)
