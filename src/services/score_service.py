from typing import Any

import pandas as pd


def calculate_time_score(value: float) -> float:
    """Calculate time overlap score.

    Args:
        value (float): Time overlap in hours (negative = sooner = better)

    Returns: Score (higher is better)
    """
    if value > 0:  # Positive = delayed execution = BAD
        return -value * 1.5  # 50% penalty
    else:
        abs_value = abs(value)
        if abs_value <= 8:  # 0 to -8 hours: Perfect timing
            return 100.0
        elif abs_value <= 24:  # -8 to -24 hours: Good (same day)
            return 100 - (abs_value - 8) * 2
        elif abs_value <= 72:  # -24 to -72 hours: Acceptable (within 3 days)
            return 68 - (abs_value - 24) * 0.5
        else:  # Beyond -72 hours: Too early
            score = 44 - (abs_value - 72) * 0.1
            return max(score, 0)  # Floor at 0


def calculate_distance_score(value: float) -> float:
    """Calculate distance saving score.

    Args:
        value (float): Distance saved in kilometers (positive = good)

    Returns: Score (higher is better)
    """
    # Direct benefit - no modification needed
    return value


def calculate_operation_score(value: float) -> float:
    """Calculate operation cost score.

    Args:
        value (float): Operation cost in currency (lower = better)

    Returns: Score (higher is better, so we return negative)
    """
    # Return negative because lower cost should give higher score
    return -value


def calculate_cost_saved_score(value: float) -> float:
    """Calculate cost saved score with scaling penalties.

    Args:
        value(float): Cost saved in currency (positive = good, negative = bad)

    Returns: Score (higher is better)
    """
    if value > 0:
        return value  # Positive savings are good
    elif value == 0:
        return -10.0  # Small penalty for break-even
    else:  # D < 0
        # Scaling penalty that gets worse with larger losses
        penalty_factor = 1 + (abs(value) / 100)
        return value * penalty_factor


def normalize_within_pair(row_scores: list[dict[str, Any]]):
    """Normalize each component within the pair to 0-1 scale."""
    normalized = []

    # Get min/max for each component within this pair
    components = ["time", "distance", "saved", "operation"]

    for component in components:
        values = [row[component] for row in row_scores]
        min_val = min(values)
        max_val = max(values)

        # Handle case where both values are the same
        if max_val == min_val:
            for row in row_scores:
                row[f"{component}_norm"] = 0.5
        else:
            for row in row_scores:
                row[f"{component}_norm"] = (row[component] - min_val) / (max_val - min_val)

    # Create normalized score dictionaries
    for row in row_scores:
        normalized.append(
            {
                "time": row["time_norm"],
                "distance": row["distance_norm"],
                "operation": row["operation_norm"],
                "saved": row["saved_norm"],
                "raw": row["raw_data"],
            }
        )

    return normalized


def apply_threshold_penalties(norm_score):
    """Apply penalties if any normalized component is below 50%."""
    penalty_multiplier = 1.0

    if norm_score["time"] < 0.5:
        penalty_multiplier *= 0.90  # 10% penalty
    if norm_score["distance"] < 0.5:
        penalty_multiplier *= 0.95  # 5% penalty
    if norm_score["operation"] < 0.5:
        penalty_multiplier *= 0.85  # 15% penalty (harshest)
    if norm_score["saved"] < 0.5:
        penalty_multiplier *= 0.90  # 10% penalty

    return penalty_multiplier


def calculate_pair_scores(data: pd.DataFrame, weights: dict[str, float]) -> pd.DataFrame:
    """Main scoring function using the component functions.

    Args:
        data (pd.DataFrame): DataFrame containing pairs of rows to compare.
        weights (dict[str, float]): Weights for each component score.

    Returns:
        tuple: Final scores for each row and the index of the winning row.
    """
    # Calculate component scores for each row
    row_scores = []
    for _, row in data.iterrows():
        scores = {
            "time": calculate_time_score(row["time_gap_in_hour"]),
            "distance": calculate_distance_score(row["dist_sav_km"]),
            "operation": calculate_operation_score(row["street_turn_total_cost"]),
            "saved": calculate_cost_saved_score(row["cost_save"]),
            "raw_data": row,
        }
        row_scores.append(scores)

    # Normalize within pair
    normalized_scores = normalize_within_pair(row_scores)

    # Calculate final scores
    final_scores = []
    for norm_score in normalized_scores:
        # Base weighted score
        weighted = sum(weights[key] * norm_score[key] for key in weights.keys())

        # Apply threshold penalties
        penalty = apply_threshold_penalties(norm_score)

        # Final score (1-10 scale)
        final_score = weighted * penalty * 10
        final_scores.append(round(final_score, 2))

    # Add the scores to the original DataFrame
    data["score"] = final_scores
    data = data.sort_values(by="score", ascending=False).reset_index(drop=True)
    return data
