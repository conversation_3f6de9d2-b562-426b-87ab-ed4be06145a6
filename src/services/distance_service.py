import pandas as pd

from src.const import get_settings
from src.const.col_map import DISTANCE_SCHEMA
from src.thirdparty.gcp import BigQueryManager, GMapsManager
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


bq_manager = BigQueryManager()
gmaps_manager = GMapsManager()


def calculate_distance(origin: str, destination: str) -> tuple[float, float]:
    """Calculate the distance and duration between two locations using Google Maps.

    Enhances geocoding accuracy by appending country information if not present.
    Handles API errors gracefully by returning a large default value.

    Args:
        origin (str): Starting location (city name)
        destination (str): Ending location (city name)

    Returns:
        tuple[float, float]: Distance in kilometers and duration in hours.
                            Returns (9999999999, 9999999999) if calculation fails.
    """
    try:
        # Add country to improve geocoding accuracy
        if not origin.lower().endswith(", usa") and not origin.lower().endswith(", us"):
            origin = f"{origin}, USA"
        if not destination.lower().endswith(", usa") and not destination.lower().endswith(", us"):
            destination = f"{destination}, USA"

        result = gmaps_manager.get_distance_matrix(origin, destination)

        if result and "rows" in result and result["rows"] and "elements" in result["rows"][0]:
            element = result["rows"][0]["elements"][0]
            if "status" in element and element["status"] == "OK" and "distance" in element:
                # Convert meters to kilometers and seconds to hours
                return element["distance"]["value"] / 1000.0, element["duration"]["value"] / 3600.0

        logger.warning(f"Failed to get distance from {origin} to {destination}")
        return 9999999999, 9999999999

    except Exception as e:
        logger.error(f"Error calculating distance from {origin} to {destination}: {e}")
        return 9999999999, 9999999999


def get_cached_distance(
    origin: dict[str, str], destination: dict[str, str], distance_cache: dict
) -> tuple[float, dict]:
    """Get distance between locations, using cache when available.

    Maintains a cache of previously calculated distances to reduce API calls.
    Creates a consistent cache key by sorting location names alphabetically.

    Args:
        origin (str): Starting location (city name)
        destination (str): Ending location (city name)
        distance_cache (dict): Dictionary to store cached distances

    Returns:
        float: Distance in kilometers between the locations
    """
    # valid origin and destination
    if not origin or "cd" not in origin or "addr" not in origin:
        raise ValueError(
            f"Invalid origin provided for distance calculation. origin must contain cd and addr but has {origin}"
        )
    if not destination or "cd" not in destination or "addr" not in destination:
        raise ValueError(
            f"Invalid destination provided for distance calculation. destination must contain cd and addr but has {destination}"
        )

    # normalize the origin and destination value
    origin["cd"] = origin["cd"].upper()
    origin["addr"] = origin["addr"].upper()
    destination["cd"] = destination["cd"].upper()
    destination["addr"] = destination["addr"].upper()

    # Create a unique key for the origin-destination pair
    # Ensure the key is always in the same order regardless of input order by sort origin and destination by addr
    sorted_locations = sorted([origin, destination], key=lambda x: (x["addr"]))
    key = (sorted_locations[0]["addr"], sorted_locations[1]["addr"])

    if key in distance_cache:
        return distance_cache[key][2], distance_cache

    distance, duration = calculate_distance(sorted_locations[0]["addr"], sorted_locations[1]["addr"])
    distance_cache[key] = (sorted_locations[0]["cd"], sorted_locations[1]["cd"], distance, duration)
    return distance, distance_cache


async def download_distance_cache() -> dict:
    """Download the distance cache from BigQuery.

    Returns:
        dict: A dictionary mapping (origin, destination) tuples to (distance, duration) tuples.
    """
    query = """
        SELECT DISTINCT ORIG_CD, DEST_CD, ORIG_NM, DEST_NM, DIST, DUR
        FROM `{table_id}`
        WHERE DELT_FLG = 'N'
        AND DIST IS NOT NULL
    """
    table_id = f"{settings.ENV.PROJECT_ID}.{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.DISTANCE_TABLE_ID}"
    query = query.format(table_id=table_id)
    result = await bq_manager.execute_query(query)
    return {
        tuple(row[["ORIG_NM", "DEST_NM"]]): (row["ORIG_CD"], row["DEST_CD"], row["DIST"], row["DUR"])
        for idx, row in result.iterrows()
    }


async def upload_distance_cache(current_cache: dict) -> None:
    """Upload the distance cache to BigQuery.

    Args:
        current_cache (dict): A dictionary mapping (origin, destination) tuples to (distance, duration) tuples.
    """
    data = [
        {
            "ORIG_CD": values[0],
            "ORIG_NM": origin,
            "DEST_CD": values[1],
            "DEST_NM": destination,
            "DIST": values[2],
            "DUR": values[3],
        }
        for (origin, destination), values in current_cache.items()
    ]
    df = pd.DataFrame(data)
    df["ORIG_LAT"] = None
    df["ORIG_LON"] = None
    df["DEST_LAT"] = None
    df["DEST_LON"] = None

    df["DELT_FLG"] = "N"

    curr_timestamp = pd.to_datetime("now")
    df["CRE_DT"] = curr_timestamp
    df["UPD_DT"] = curr_timestamp

    df = df.convert_dtypes()

    table_id: str = f"{settings.ENV.PROJECT_ID}.{settings.DB.DE_OUT_DATASET_ID}.{settings.DB.DISTANCE_TABLE_ID}"
    await bq_manager.upsert_dataframe(
        dataframe=df,
        table_id=table_id,
        schema=DISTANCE_SCHEMA,
        if_exists="replace",
    )
