import asyncio

import pandas as pd
import pulp

from src.const import get_settings
from src.utils.decorators import measure_time
from src.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)
settings = get_settings()


class LinearProgrammingOptimizer:
    @measure_time(logger=logger)
    async def find_matches(
        self, max_distance_thres_km: float, time_tolerance_thres: int, all_pairs: pd.DataFrame
    ) -> list[dict]:
        """Find optimal matches using linear programming optimization.

        Args:
            max_distance_thres_km (float): Maximum allowed distance between locations
            time_tolerance_thres (int): Maximum allowed days between delivery and pickup
            all_pairs (pd.DataFrame): Candidate pairs to optimize

        Returns:
            list[dict]: Optimal matches, each containing pair details and metrics

        Note:
            Uses the PuLP library for linear programming optimization
        """
        # Filter out pairs
        filtered_pairs = await asyncio.to_thread(
            lambda: all_pairs[all_pairs["imp_to_xpt_dist_km"] <= max_distance_thres_km].reset_index(drop=True)
        )
        filtered_pairs = await asyncio.to_thread(
            lambda: filtered_pairs[filtered_pairs["time_gap_in_hour"] <= time_tolerance_thres * 24].reset_index(
                drop=True
            )
        )
        logger.info(
            f"Filtered pairs: {len(filtered_pairs)} pairs with distance <= {max_distance_thres_km} km and time gap <= {time_tolerance_thres * 24} hours ({time_tolerance_thres} days)"
        )
        if filtered_pairs.empty:
            logger.info("No pairs found within the given constraints.")
            return []

        logger.info("Initiating linear programming optimization for matching...")
        # Create a linear programming problem
        prob = pulp.LpProblem("Maximize_Savings", pulp.LpMaximize)

        # Create decision variables for each pair (one variable per row in filtered_pairs)
        pair_vars = {i: pulp.LpVariable(f"pair_{i}", cat="Binary") for i in filtered_pairs.index}

        # Objective function: maximize total savings
        prob += pulp.lpSum(pair_vars[i] * filtered_pairs.loc[i, "dist_sav_km"] for i in filtered_pairs.index)

        # Add constraints for exclusive matching
        import_groups = await asyncio.to_thread(lambda: filtered_pairs.groupby("imp_cop_no").indices)
        for imp_cop_no, indices in import_groups.items():
            prob += (
                pulp.lpSum(pair_vars[i] for i in indices) <= settings.SYSTEM.MAX_IMPORT_MATCHES,
                f"Import_{imp_cop_no}_constraint",
            )

        export_groups = await asyncio.to_thread(lambda: filtered_pairs.groupby("xpt_cop_no").indices)
        for xpt_cop_no, indices in export_groups.items():
            prob += (
                pulp.lpSum(pair_vars[i] for i in indices) <= settings.SYSTEM.MAX_EXPORT_MATCHES,
                f"Export_{xpt_cop_no}_constraint",
            )

        logger.info("Solving the linear programming problem...")
        verbose = True if settings.ENV.ENVIRONMENT == "development" else False
        await asyncio.to_thread(prob.solve, pulp.PULP_CBC_CMD(msg=verbose))

        logger.info(f"Status: {pulp.LpStatus[prob.status]}")
        logger.info(f"Objective value: {pulp.value(prob.objective)}")
        logger.info(f"Number of filtered pairs: {len(filtered_pairs)}")

        # Extract the optimal matches from linear programming solution
        optimal_matches = []
        for i in filtered_pairs.index:
            if pair_vars[i].value() == 1:
                match_dict = filtered_pairs.loc[i].to_dict()
                match_dict["weight"] = filtered_pairs.loc[i, "dist_sav_km"]
                match_dict["is_optimal"] = True
                optimal_matches.append(match_dict)

        # Find sub-optimal matches by sorting on distance savings
        # Start with creating a copy to avoid modifying the original
        remaining_pairs = filtered_pairs.copy()

        # Remove the indices that were already selected as optimal
        if optimal_matches:
            optimal_indices = [filtered_pairs.index[i] for i in range(len(filtered_pairs)) if pair_vars[i].value() == 1]
            remaining_pairs = remaining_pairs[~remaining_pairs.index.isin(optimal_indices)]

        # Find sub-optimal matches for each optimal match
        sub_optimal_matches = []
        for match in optimal_matches:
            imp_cop_no = match["imp_cop_no"]

            # Get candidates with the same import COP number
            candidates = filtered_pairs[filtered_pairs["imp_cop_no"] == imp_cop_no]

            # Sort by distance savings and get top matches (excluding the optimal one)
            candidates = candidates[~candidates.index.isin(optimal_indices)]
            candidates = candidates.sort_values(by="dist_sav_km", ascending=False).head(4)

            # Convert each candidate to a dictionary and add to sub-optimal matches
            for idx in candidates.index:
                match_dict = candidates.loc[idx].to_dict()
                match_dict["is_optimal"] = False
                match_dict["weight"] = candidates.loc[idx, "dist_sav_km"]
                sub_optimal_matches.append(match_dict)

        # Combine optimal and sub-optimal matches
        matches = optimal_matches + sub_optimal_matches
        logger.info(f"Found {len(optimal_matches)} optimal matches and {len(sub_optimal_matches)} sub-optimal matches")

        return matches
