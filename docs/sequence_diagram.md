# Street Turn Matching System - Sequence Diagram

```mermaid
sequenceDiagram
    participant Client
    participant PubSubRouter
    participant MatchingUtils
    participant MatchingService
    participant DataService
    participant ArtifactService
    participant BigQuery
    participant GSheets

    Client->>PubSubRouter: POST /exec-code
    Note right of Client: Sends PubSubEnvelope

    PubSubRouter->>MatchingUtils: execute_matching_process()

    activate MatchingUtils
        MatchingUtils->>MatchingService: setup()
        MatchingService-->>MatchingUtils: match_id, temp_dir

        alt No filtered pairs provided
            MatchingUtils->>DataService: fetch_data(trigger_timestamp)
            DataService->>BigQuery: Query data
            BigQuery-->>DataService: Raw data
            DataService-->>MatchingUtils: Data collection

            MatchingUtils->>MatchingService: create_all_matches()
            activate MatchingService
                MatchingService->>MatchingService: __pre_filtering()
                MatchingService->>MatchingService: __create_candidate_pairs()
                MatchingService->>MatchingService: __filter_and_enrich_pairs()
                MatchingService->>MatchingService: find_matches()
            deactivate MatchingService
        end

        MatchingUtils->>MatchingService: find_actual_route()
        activate MatchingService
            MatchingService->>MatchingService: __merge_result()
            Note right of MatchingService: Calculate costs and routes
        deactivate MatchingService

        MatchingUtils->>BigQuery: Store results

        MatchingUtils->>ArtifactService: write_matches()
        ArtifactService->>GSheets: Update spreadsheet
    deactivate MatchingUtils

    MatchingUtils-->>PubSubRouter: Success response
    PubSubRouter-->>Client: HTTP 200 OK
```

## Process Flow Description

1. The process initiates when a client sends a POST request to `/exec-code` endpoint with a PubSubEnvelope

2. The main process is orchestrated by `execute_matching_process()` in MatchingUtils which:

   - Initializes the matching process with `setup()`
   - Either uses provided filtered pairs or fetches new data
   - Creates matches using several sub-processes in MatchingService
   - Enriches matches with route information
   - Stores results in BigQuery
   - Writes results to Google Sheets

3. Key components involved:

   - `PubSubRouter`: Handles incoming requests
   - `MatchingUtils`: Orchestrates the overall process
   - `MatchingService`: Core matching logic
   - `DataService`: Data access layer
   - `ArtifactService`: Handles output to Google Sheets
   - `BigQuery`: Database storage
   - `GSheets`: Results visualization

4. The main matching process includes:
   - Pre-filtering candidate pairs
   - Creating possible match pairs
   - Filtering and enriching pairs
   - Finding optimal matches
   - Calculating routes and costs
   - Storing and presenting results
