# Troubleshooting Guide

This guide helps new team members diagnose and resolve common issues when setting up and running the GHQ BPIT NCOP IRO Street Turn Matching Service.

## Quick Diagnosis Checklist

Before diving into specific issues, run through this quick checklist:

### ✅ Basic Setup Verification

1. **Python Version**: `python --version` (should be 3.11-3.12)
2. **Poetry Installation**: `poetry --version`
3. **Configuration Files**: Both `.env` and `config/config.toml` exist
4. **Dependencies**: `poetry install` completed successfully
5. **Environment Variables**: All required variables set in `.env`

### ✅ Service Health Check

```bash
# If running locally
curl -H "X-API-Key: [your_key_in_here]" http://localhost:8001/api/v1/health

# If deployed to Cloud Run
curl -H "X-API-Key: [your_key_in_here]" https://your-service-url/api/v1/health
```

## Installation and Setup Issues

### Python Version Issues

#### Problem: Wrong Python Version

```bash
ERROR: This project requires Python >=3.11.0,<3.13.0
```

**Solution**:

```bash
# Check current version
python --version

# Install correct Python version
# On macOS with Homebrew:
brew install python@3.12

# On Ubuntu:
sudo apt update
sudo apt install python3.12 python3.12-venv

# Create virtual environment with specific version
python3.12 -m venv .venv
source .venv/bin/activate  # On macOS/Linux
# or
.venv\Scripts\activate     # On Windows
```

#### Problem: Multiple Python Versions

```bash
ERROR: python points to wrong version
```

**Solution**:

```bash
# Check available Python versions
ls -la /usr/bin/python*

# Use specific version
python3.12 -m venv .venv

# Or create alias in your shell profile
echo 'alias python=python3.12' >> ~/.zshrc  # or ~/.bashrc
source ~/.zshrc
```

### Poetry Installation Issues

#### Problem: Poetry Not Found

```bash
poetry: command not found
```

**Solution**:

```bash
# Install Poetry using official installer
curl -sSL https://install.python-poetry.org | python3 -

# Add Poetry to PATH (add to your shell profile)
export PATH="$HOME/.local/bin:$PATH"

# Verify installation
poetry --version
```

#### Problem: Poetry Dependencies Conflict

```bash
ERROR: Because project depends on package A (>=1.0.0) and package B (>=2.0.0), version solving failed.
```

**Solution**:

```bash
# Clear Poetry cache
poetry cache clear pypi --all

# Update Poetry
poetry self update

# Try installing with verbose output
poetry install -vvv

# If still failing, check pyproject.toml for conflicting versions
```

### Configuration File Issues

#### Problem: Missing Configuration Files

```bash
FileNotFoundError: [Errno 2] No such file or directory: '.env'
```

**Solution**:

```bash
# Create configuration files from samples
cp .env.sample .env
cp config/config.toml.sample config/config.toml

# Edit files with your values
nano .env  # or your preferred editor
nano config/config.toml
```

#### Problem: Invalid Configuration Format

```bash
ERROR: Invalid TOML format in config/config.toml
```

**Solution**:

```bash
# Validate TOML syntax online: https://www.toml-lint.com/
# Common issues:
# - Missing quotes around strings
# - Incorrect nested structure
# - Invalid characters

# Example correct format:
[GCP]
[GCP.STORAGE]
BUCKET_NAME = "your-bucket-name"

[SYSTEM]
NODES = ["USATL63", "USORF01"]
```

#### Problem: Missing Environment Variables

```bash
KeyError: 'SERVICE_ACCOUNT_ENCODE'
```

**Solution**:

```bash
# Check your .env file has all required variables
cat .env

# Required variables:
ENVIRONMENT=development
SERVICE_ACCOUNT_ENCODE=your-base64-encoded-service-account
GMAP_API_KEY=your-google-maps-api-key
DB_REPORT_DATA_FOLDER_ID=your-google-drive-folder-id
API_KEY=your-secure-api-key
HOST=0.0.0.0
PORT=8001
FASTAPI_WORKER=1
```

## Application Runtime Issues

### Server Startup Problems

#### Problem: Port Already in Use

```bash
ERROR: [Errno 48] Address already in use
```

**Solution**:

```bash
# Find process using the port
lsof -i :8001

# Kill the process
kill -9 <PID>

# Or use different port
poetry run uvicorn app:app --reload --port 8002
```

#### Problem: Import Errors

```bash
ModuleNotFoundError: No module named 'src'
```

**Solution**:

```bash
# Ensure you're in the project root directory
pwd  # Should show path ending with ghq-bpit-ncop-iro

# Install dependencies
poetry install

# Run from correct location
poetry run uvicorn app:app --reload

# If still failing, check PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

#### Problem: FastAPI Application Won't Start

```bash
ERROR: Error loading ASGI app. Could not import module "app".
```

**Solution**:

```bash
# Check app.py exists in root directory
ls -la app.py

# Run with full module path
poetry run uvicorn app:app --reload

# Or specify the module explicitly
poetry run python -m uvicorn app:app --reload
```

### Authentication Issues

#### Problem: API Key Rejected

```bash
HTTP 401: Invalid API Key
```

**Solution**:

```bash
# Check API key in .env file
grep API_KEY .env

# Ensure API key is set and matches your requests
curl -H "X-API-Key: $(grep API_KEY .env | cut -d'=' -f2)" \
     http://localhost:8001/api/v1/health

# If testing, you can set a simple key for development
echo "API_KEY=dev-test-key-123" >> .env
```

#### Problem: Missing Authentication Header

```bash
HTTP 422: Field required
```

**Solution**:

```bash
# Always include X-API-Key header
curl -H "X-API-Key: [your_key_in_here]" http://localhost:8001/api/v1/health

# Check header name (case-sensitive)
# Correct: X-API-Key
# Incorrect: x-api-key, Api-Key
```

### Google Cloud Platform Issues

#### Problem: Service Account Authentication Failed

```bash
ERROR: Could not automatically determine credentials
```

**Solution**:

```bash
# Check service account JSON is properly base64 encoded
echo $SERVICE_ACCOUNT_ENCODE | base64 -d | jq .

# Verify service account has required permissions:
# - BigQuery Data Editor
# - Storage Object Admin
# - Sheets API access

# Test service account manually
gcloud auth activate-service-account --key-file=service-account.json
gcloud auth application-default login
```

#### Problem: BigQuery Access Denied

```bash
ERROR: 403 Access Denied: Project user does not have bigquery.datasets.get access
```

**Solution**:

```bash
# Verify service account permissions in GCP Console
# Required IAM roles:
# - BigQuery Data Editor
# - BigQuery Job User

# Check if datasets exist
bq ls --project_id=your-project-id

# Test connection
bq query --use_legacy_sql=false "SELECT 1 as test"
```

#### Problem: Google Maps API Quota Exceeded

```bash
ERROR: You have exceeded your daily request quota for this API
```

**Solution**:

```bash
# Check API usage in GCP Console
# Go to: APIs & Services > Credentials > API Key > Metrics

# Increase quota limits or:
# 1. Enable billing on your project
# 2. Request quota increase
# 3. Optimize API calls in code

# For development, you can use mock distance service
```

### Database and Data Issues

#### Problem: BigQuery Dataset Not Found

```bash
ERROR: Not found: Dataset project:dataset_id
```

**Solution**:

```bash
# Check dataset configuration in config/config.toml
grep -A 5 "\[DB\]" config/config.toml

# Verify datasets exist in BigQuery
bq ls --project_id=your-project-id

# Create missing datasets
bq mk --dataset your-project-id:SOURCE_DATA
bq mk --dataset your-project-id:OUTPUTS
bq mk --dataset your-project-id:INTERMEDIATE
```

#### Problem: Google Sheets Access Denied

```bash
ERROR: The caller does not have permission to access the Google Sheets
```

**Solution**:

```bash
# Share Google Sheet with service account email
# 1. Open Google Sheet
# 2. Click Share
# 3. Add service account email (found in service-account.json)
# 4. Grant Editor permissions

# Check sheet ID in config/config.toml
grep SPREADSHEET_ID config/config.toml
```

### Performance Issues

#### Problem: Slow API Responses

```bash
Request taking longer than expected (>30 seconds)
```

**Solution**:

```bash
# Check logs for bottlenecks
tail -f logs/app/app.log

# Monitor resource usage
htop  # or top on some systems

# Common causes:
# 1. Large dataset processing
# 2. Network latency to GCP services
# 3. Inefficient queries
# 4. Memory constraints

# Optimize by:
# 1. Adding pagination to large queries
# 2. Using data caching
# 3. Increasing memory limits
```

#### Problem: Memory Issues

```bash
ERROR: Process killed (out of memory)
```

**Solution**:

```bash
# Monitor memory usage
free -h

# Increase Docker memory limits (if using Docker)
docker run --memory=4g your-image

# Or adjust Cloud Run memory limits
gcloud run services update your-service --memory=4Gi

# Optimize code:
# 1. Process data in chunks
# 2. Clear large variables after use
# 3. Use generators instead of loading all data
```

## Development Environment Issues

### Docker Issues

#### Problem: Docker Build Failures

```bash
ERROR: failed to build docker image
```

**Solution**:

```bash
# Clear Docker cache
docker system prune -af

# Build with verbose output
docker build --no-cache --progress=plain -t your-image .

# Check Dockerfile syntax
docker build --dry-run -t test .

# Common issues:
# 1. Missing files in build context
# 2. Incorrect base image
# 3. Network issues during build
```

#### Problem: Container Won't Start

```bash
ERROR: container exited with code 1
```

**Solution**:

```bash
# Check container logs
docker logs container-name

# Run container interactively
docker run -it your-image /bin/bash

# Check environment variables
docker run --env-file .env your-image env

# Verify file permissions
docker run your-image ls -la /app
```

### Testing Issues

#### Problem: Tests Failing

```bash
ERROR: tests/test_api.py::test_health FAILED
```

**Solution**:

```bash
# Run tests with verbose output
poetry run pytest -v

# Run specific test
poetry run pytest tests/test_api.py::test_health -v

# Check test configuration
cat pytest.ini

# Common issues:
# 1. Missing test dependencies
# 2. Environment not set up for testing
# 3. External service dependencies in tests
```

#### Problem: Import Errors in Tests

```bash
ModuleNotFoundError: No module named 'src'
```

**Solution**:

```bash
# Install package in development mode
poetry install

# Or add project root to PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Check pytest configuration
cat pytest.ini

# Should contain:
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = --tb=short
```

## Cloud Deployment Issues

### Cloud Run Deployment Problems

#### Problem: Deployment Fails

```bash
ERROR: Cloud Run deployment failed
```

**Solution**:

```bash
# Check Cloud Run service logs
gcloud logging tail "resource.type=cloud_run_revision"

# Verify image exists in Artifact Registry
gcloud artifacts docker images list --repository=your-repo

# Check service configuration
gcloud run services describe your-service --region=your-region

# Common issues:
# 1. Insufficient IAM permissions
# 2. Invalid environment variables
# 3. Resource limits too low
# 4. Health check failures
```

#### Problem: Service Unhealthy

```bash
ERROR: Cloud Run service is not receiving traffic
```

**Solution**:

```bash
# Check health check endpoint
curl https://your-service-url/api/v1/health

# Verify service is listening on correct port
# Check PORT environment variable matches your app

# Check Cloud Run configuration
gcloud run services describe your-service \
  --region=your-region \
  --format="value(spec.template.spec.containers[0].ports[0].containerPort)"
```

### CI/CD Pipeline Issues

#### Problem: GitHub Actions Workflow Fails

```bash
ERROR: Workflow run failed
```

**Solution**:

```bash
# Check workflow logs in GitHub Actions tab

# Common issues:
# 1. Missing secrets (DEV_GCP_CREDS)
# 2. Insufficient permissions
# 3. Docker build failures
# 4. Artifact Registry authentication

# Verify secrets are set:
# GitHub Repository > Settings > Secrets and variables > Actions
```

#### Problem: Image Push to Artifact Registry Fails

```bash
ERROR: denied: Permission "artifactregistry.repositories.uploadArtifacts" denied
```

**Solution**:

```bash
# Check service account has correct IAM roles:
# - Artifact Registry Writer
# - Storage Object Viewer

# Verify authentication
gcloud auth configure-docker asia-southeast1-docker.pkg.dev

# Test manual push
docker push asia-southeast1-docker.pkg.dev/project/repo/image:tag
```

## Performance Optimization

### Memory Optimization

```python
# Use generators for large datasets
def process_large_dataset():
    for batch in get_data_in_batches(batch_size=1000):
        yield process_batch(batch)

# Clear large variables
large_dataframe = None
gc.collect()
```

### Query Optimization

```python
# Use pagination for large queries
def get_paginated_results(query, page_size=1000):
    offset = 0
    while True:
        paginated_query = f"{query} LIMIT {page_size} OFFSET {offset}"
        results = execute_query(paginated_query)
        if not results:
            break
        yield results
        offset += page_size
```

## Getting Help

### Log Analysis

#### Application Logs

```bash
# View recent application logs
tail -f logs/app/app.log

# Search for specific errors
grep "ERROR" logs/app/app.log

# Monitor logs in real-time with filtering
tail -f logs/app/app.log | grep -E "(ERROR|WARNING)"
```

#### Cloud Run Logs

```bash
# View recent Cloud Run logs
gcloud logging read "resource.type=cloud_run_revision" --limit=50

# Filter by severity
gcloud logging read "resource.type=cloud_run_revision AND severity>=ERROR"

# Real-time log monitoring
gcloud logging tail "resource.type=cloud_run_revision"
```

### Debug Mode

#### Enable Debug Logging

```bash
# Set environment variable
export LOG_LEVEL=DEBUG

# Or in .env file
echo "LOG_LEVEL=DEBUG" >> .env
```

#### Verbose API Responses

```python
# Add to your API calls for debugging
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Contact and Support

1. **Check Documentation**: Review all docs in `docs/` folder
2. **Search Issues**: Look for similar problems in project issues
3. **Create Issue**: Use the issue template with:
    - Environment details (OS, Python version, Poetry version)
    - Steps to reproduce
    - Error messages and logs
    - Configuration (without sensitive data)

### Emergency Rollback

#### Application Rollback

```bash
# Rollback to previous Cloud Run revision
gcloud run services update-traffic your-service \
  --to-revisions=your-previous-revision=100 \
  --region=your-region

# Or deploy previous working image
gcloud run deploy your-service \
  --image=previous-working-image-tag \
  --region=your-region
```

#### Configuration Rollback

```bash
# Restore from git
git checkout HEAD~1 -- config/config.toml .env.sample

# Or restore from backup
cp config/config.toml.backup config/config.toml
```

---

**Last Updated**: July 9, 2025
**Version**: 1.0
**Maintained By**: Development Team

## Related Documentation

-   [README](../README.md) - Main project documentation
-   [API Reference](./api_reference.md) - Complete API documentation
-   [Deployment Guide](./deployment_guide.md) - Deployment procedures
-   [Project Structure](./project_structure.md) - Codebase organization
