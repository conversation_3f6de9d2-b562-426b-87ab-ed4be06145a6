# Developer Guide

This guide provides comprehensive information for developers contributing to the GHQ BPIT NCOP IRO Street Turn Matching Service.

## Getting Started

### Prerequisites

Before you begin, ensure you have:

-   Python 3.11 or higher (< 3.13)
-   [Poetry](https://python-poetry.org/) for dependency management
-   [Git](https://git-scm.com/) for version control
-   [Docker](https://www.docker.com/) (optional, for containerized development)
-   Access to required Google Cloud Platform services

### Initial Setup

1. **Clone the repository**:

    ```bash
    <NAME_EMAIL>:ocean-network-express/ghq-bpit-ncop-iro.git
    cd ghq-bpit-ncop-iro
    ```

2. **Set up configuration files**:

    ```bash
    # Copy environment configuration template
    cp .env.sample .env

    # Copy application configuration template
    cp config/config.toml.sample config/config.toml
    ```

3. **Install dependencies**:

    ```bash
    poetry install
    ```

4. **Set up pre-commit hooks**:

    ```bash
    poetry run pre-commit install
    ```

5. **Configure your environment**:
    - Edit `.env` with your specific environment variables
    - Edit `config/config.toml` with your GCP and system configuration
    - Refer to the [Troubleshooting Guide](./troubleshooting_guide.md) for common setup issues

## Development Workflow

### Branch Strategy

We follow a Git Flow-inspired branching model:

#### Main Branches

-   **`main`**: Production-ready code, stable and deployed
-   **`staging`**: Pre-production testing environment
-   **`develop`**: Latest development code, integration branch

#### Feature Branches

-   **`feature/ISSUE-XXX-description`**: New features
-   **`feat/ISSUE-XXX-description`**: Alternative naming for features
-   **`test/ISSUE-XXX-description`**: Testing-related branches
-   **`bug/ISSUE-XXX-description`**: Bug fixes
-   **`hotfix/ISSUE-XXX-description`**: Critical production fixes

### Development Process

1. **Create a feature branch**:

    ```bash
    git checkout develop
    git pull origin develop
    git checkout -b feature/ISSUE-123-add-new-matching-algorithm
    ```

2. **Make your changes**:

    - Write code following the coding standards
    - Add/update tests for new functionality
    - Update documentation as needed

3. **Test your changes**:

    ```bash
    # Run tests
    poetry run pytest

    # Run tests with coverage
    poetry run pytest --cov=src tests/

    # Run linting
    poetry run pre-commit run --all-files
    ```

4. **Commit your changes**:

    ```bash
    git add .
    git commit -m "feat: add new matching algorithm for improved accuracy"
    ```

5. **Push and create pull request**:
    ```bash
    git push origin feature/ISSUE-123-add-new-matching-algorithm
    ```

### Commit Message Standards

We follow conventional commit format:

```
<type>: <description>

[optional body]

[optional footer]
```

#### Types:

-   **feat**: New feature
-   **fix**: Bug fix
-   **docs**: Documentation changes
-   **style**: Code style changes (formatting, no logic changes)
-   **refactor**: Code refactoring
-   **test**: Adding or modifying tests
-   **build**: Build system or dependency changes
-   **ci**: CI/CD pipeline changes
-   **chore**: Maintenance tasks

#### Examples:

```bash
feat: add distance caching for improved performance
fix: resolve null pointer exception in matching service
docs: update API documentation with new endpoints
test: add unit tests for matching algorithm
```

## Code Standards

### Python Code Style

We follow [PEP 8](https://pep8.org/) with some modifications:

-   **Line length**: 88 characters (Black formatter default)
-   **Import organization**: Use `isort` for consistent import sorting
-   **Type hints**: Required for all public functions and methods
-   **Docstrings**: Use Google-style docstrings for all public modules, classes, and functions

### Code Organization

```python
# Standard library imports
import os
import sys
from typing import Dict, List, Optional

# Third-party imports
import pandas as pd
from fastapi import FastAPI, HTTPException

# Local imports
from src.services.matching_service import MatchingService
from src.utils.logging_utils import setup_logger
```

### Function/Method Documentation

```python
def calculate_distance(
    origin: str,
    destination: str,
    method: str = "google_maps"
) -> Optional[float]:
    """Calculate distance between two points.

    Args:
        origin: Starting point address or coordinates
        destination: End point address or coordinates
        method: Calculation method ('google_maps', 'haversine')

    Returns:
        Distance in kilometers, or None if calculation fails

    Raises:
        ValueError: If method is not supported
        APIError: If external API call fails
    """
```

## Testing

### Test Structure

```
tests/
├── unit/                    # Unit tests
│   ├── test_services/
│   ├── test_utils/
│   └── test_api/
├── integration/             # Integration tests
│   ├── test_api_endpoints/
│   └── test_data_flow/
└── fixtures/                # Test fixtures and mock data
```

### Running Tests

```bash
# Run all tests
poetry run pytest

# Run specific test file
poetry run pytest tests/unit/test_services/test_matching_service.py

# Run tests with coverage
poetry run pytest --cov=src --cov-report=html

# Run tests with verbose output
poetry run pytest -v

# Run tests matching a pattern
poetry run pytest -k "test_matching"
```

### Writing Tests

```python
import pytest
from unittest.mock import Mock, patch

from src.services.matching_service import MatchingService


class TestMatchingService:
    """Test cases for MatchingService."""

    @pytest.fixture
    def mock_config(self):
        """Mock configuration for testing."""
        return {
            "distance_threshold": 100,
            "max_matches": 5
        }

    @pytest.fixture
    def matching_service(self, mock_config):
        """Create MatchingService instance for testing."""
        return MatchingService(config=mock_config)

    def test_find_matches_returns_expected_results(self, matching_service):
        """Test that find_matches returns expected format."""
        # Arrange
        test_data = {...}

        # Act
        result = matching_service.find_matches(test_data)

        # Assert
        assert isinstance(result, list)
        assert len(result) <= 5
```

## Local Development

### Running the Application

```bash
# Start development server with hot reload
poetry run uvicorn app:app --reload --host 0.0.0.0 --port 8001

# Start with specific environment
ENVIRONMENT=development poetry run uvicorn app:app --reload

# Run with debug logging
poetry run uvicorn app:app --reload --log-level debug
```

### Environment Variables for Development

Create a `.env` file with development-specific values:

```bash
# Use development environment
ENVIRONMENT=development

# Local development settings
HOST=0.0.0.0
PORT=8001
FASTAPI_WORKER=1

# Mock or test API keys (ensure they're not production keys)
GMAP_API_KEY=development_key
API_KEY=development_api_key

# Test GCP resources
DB_REPORT_DATA_FOLDER_ID=test_folder_id
```

### Database and External Services

For local development, consider:

1. **Mock external APIs**: Use mocks for Google Maps, Google Sheets APIs
2. **Test data**: Use sample datasets in the `data/` directory
3. **Local testing**: Use development BigQuery datasets

## Docker Development

### Building Local Images

```bash
# Build development image
docker build -t ghq-iro-dev:latest .

# Build with specific tag
docker build -t ghq-iro-dev:feature-branch .
```

### Running with Docker

```bash
# Run with environment file
docker run --env-file .env -p 8001:8001 ghq-iro-dev:latest

# Run with mounted volumes for development
docker run \
  --env-file .env \
  -v $(pwd)/src:/app/src \
  -p 8001:8001 \
  ghq-iro-dev:latest
```

## Debugging

### Local Debugging

1. **Use debugger**:

    ```python
    import pdb; pdb.set_trace()  # Add breakpoint
    ```

2. **Enable debug logging**:

    ```python
    import logging
    logging.getLogger().setLevel(logging.DEBUG)
    ```

3. **Use development tools**:

    ```bash
    # Check API health
    curl -H "X-API-Key: [your_key_in_here]" http://localhost:8001/api/v1/health

    # View API documentation
    open http://localhost:8001/docs
    ```

### Common Issues

Refer to the [Troubleshooting Guide](./troubleshooting_guide.md) for solutions to common development issues.

## Performance Optimization

### Profiling

```bash
# Profile application startup
poetry run python -m cProfile -o profile.stats app.py

# Memory profiling
poetry run python -m memory_profiler your_script.py
```

### Best Practices

1. **Use caching**: Implement caching for expensive operations
2. **Async operations**: Use `async/await` for I/O operations
3. **Database optimization**: Use efficient queries and indexing
4. **Memory management**: Avoid memory leaks in long-running processes

## Documentation

### Updating Documentation

When adding new features or making changes:

1. **Update API documentation**: Modify docstrings and OpenAPI schemas
2. **Update user guides**: Revise relevant documentation in `docs/`
3. **Update README**: If installation or setup changes
4. **Add examples**: Include usage examples for new features

### Documentation Standards

-   Use Markdown for all documentation
-   Include code examples where applicable
-   Keep documentation up-to-date with code changes
-   Use clear, concise language

## CI/CD Integration

### Automated Checks

Our CI/CD pipeline runs:

1. **Linting**: `black`, `isort`, `flake8`
2. **Type checking**: `mypy`
3. **Testing**: `pytest` with coverage reporting
4. **Security scanning**: `bandit`, `safety`
5. **Docker building**: Multi-stage builds for optimization

### Manual CI/CD Triggers

For feature/feat/test branches, CI/CD is manual-only:

1. Go to GitHub Actions
2. Select "Build and Push Docker Image" workflow
3. Click "Run workflow"
4. Select your branch type and trigger

Refer to the [CI/CD Guide](./cicd_guide.md) for detailed information.

## Release Process

### Version Management

We use [Semantic Versioning](https://semver.org/):

-   **MAJOR**: Breaking changes
-   **MINOR**: New features (backward compatible)
-   **PATCH**: Bug fixes (backward compatible)

### Creating Releases

1. **Update version**: Modify `pyproject.toml`
2. **Update changelog**: Document changes
3. **Create release branch**: `release/v1.2.0`
4. **Test thoroughly**: Ensure all tests pass
5. **Merge to main**: Create pull request
6. **Tag release**: `git tag v1.2.0`
7. **Deploy**: Follow deployment procedures

## Getting Help

### Resources

-   **[Project Documentation](../README.md)**: Main project overview
-   **[API Reference](./api_reference.md)**: Complete API documentation
-   **[Troubleshooting Guide](./troubleshooting_guide.md)**: Common issues and solutions
-   **[Deployment Guide](./deployment_guide.md)**: Deployment procedures

### Support Channels

1. **Documentation**: Check existing documentation first
2. **Issues**: Search existing GitHub issues
3. **Team Chat**: Use project communication channels
4. **Code Review**: Ask for help during pull request reviews

### Contributing Guidelines

1. **Follow coding standards**: Maintain code quality
2. **Write tests**: Ensure good test coverage
3. **Document changes**: Update relevant documentation
4. **Be responsive**: Participate in code reviews actively
5. **Ask questions**: Don't hesitate to seek clarification

---

**Last Updated**: January 15, 2025
**Version**: 1.0
**Maintained By**: Development Team
