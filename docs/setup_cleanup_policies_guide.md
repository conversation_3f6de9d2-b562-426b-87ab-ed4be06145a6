# Artifact Registry Cleanup Policies Guide

This guide provides instructions for using the `setup_cleanup_policies.bash.sh` script to manage cleanup policies for Google Cloud Artifact Registry repositories.

## Overview

The script automatically applies predefined cleanup policies to your Artifact Registry repositories, helping you:

- Remove old development, feature branch, and PR images
- Clean up release candidates and beta images
- Maintain important production versions
- Delete untagged images

## Prerequisites

- Google Cloud SDK installed and configured
- `envsubst` utility (usually comes with `gettext` package)
- Appropriate permissions to manage Artifact Registry repositories
- JSON policy file (`artifact_repo_cleanup_policies.json`)

## Installation

1. Clone or download this repository
2. Ensure the bash script is executable: `chmod +x bash/setup_cleanup_policies.bash.sh`
3. Verify the policy file exists in `gcp/artifact_repo_cleanup_policies.json` or specify a custom path

## Parameters

| Parameter | Description | Required | Default |
|-----------|-------------|----------|---------|
| `--project` | GCP project ID | Yes | - |
| `--location` | Artifact Registry location (e.g., us-central1) | Yes | - |
| `--repository` | Name of the repository | Yes | - |
| `--policy` | Path to the JSON policy file | No | `../gcp/artifact_repo_cleanup_policies.json` |

## Usage

### List current cleanup policies

```bash
gcloud artifacts repositories list-cleanup-policies REPOSITORY \
    --project=PROJECT_ID \
    --location=LOCATION
```

### Apply cleanup policies with dry run (validation only)

```bash
gcloud artifacts repositories set-cleanup-policies REPOSITORY \
    --project=PROJECT_ID \
    --location=LOCATION \
    --policy=PATH_TO_POLICY_FILE \
    --dry-run
```

### Check validation logs

```bash
gcloud logging read 'protoPayload.serviceName="artifactregistry.googleapis.com" AND protoPayload.request.parent="projects/PROJECT_ID/locations/LOCATION/repositories/REPOSITORY" AND protoPayload.request.validateOnly=true' \
    --resource-names="projects/PROJECT_ID" \
    --project=PROJECT_ID
```

### Apply cleanup policies for real

```bash
gcloud artifacts repositories set-cleanup-policies REPOSITORY \
    --project=PROJECT_ID \
    --location=LOCATION \
    --policy=PATH_TO_POLICY_FILE \
    --no-dry-run
```

### Delete specific cleanup policies

```bash
gcloud artifacts repositories delete-cleanup-policies REPOSITORY \
    --policynames=POLICY_NAMES \
    --project=PROJECT_ID \
    --location=LOCATION
```

## Default Cleanup Policies

The default policy file includes the following rules:

- **dev-cleanup**: Deletes development and feature branch images after 30 days
- **rc-cleanup**: Deletes release candidates and beta images after 30 days (2592000s)
- **prod-cleanup**: Deletes patch versions older than 1 year while keeping major/minor versions
- **keep-important**: Always preserves major and minor versions, prod and stable tags
- **untagged-cleanup**: Deletes untagged images after 7 days (604800s)

## Policy Format

Cleanup policies follow this structure:

```json
{
  "name": "POLICY_NAME",
  "action": {"type": "Delete"},
  "description": "POLICY_DESCRIPTION",
  "condition": {
    "tagState": "TAG_STATUS",
    "tagPrefixes": ["TAG_PREFIXES"],
    "versionNamePrefixes": ["VERSION_PREFIXES"],
    "packageNamePrefixes": ["PACKAGE_PREFIXES"],
    "olderThan": "OLDER_THAN_DURATION",
    "newerThan": "NEWER_THAN_DURATION"
  }
}
```

Where:

- `tagState`: Can be `TAGGED` or `UNTAGGED`
- `olderThan`: Duration in seconds (e.g., `604800s`) or days (e.g., `30d`)
- `newerThan`: Optional duration to specify a time window
- `tagPrefixes`: List of tag prefixes to match
- `versionNamePrefixes`: List of version name prefixes to match
- `packageNamePrefixes`: List of package name prefixes to match

## Troubleshooting

- **"Error: Policy file not found"**: Ensure the JSON policy file exists at the specified location
- **"Failed to apply cleanup policies"**: Check your permissions and verify the repository exists

## Notes

- You can customize the policy file to match your specific retention requirements
- The script supports variable substitution using environment variables
- Run the script regularly or set it up in your CI/CD pipeline to maintain repository hygiene

For more information on Artifact Registry cleanup policies, refer to the [Google Cloud documentation](https://cloud.google.com/artifact-registry/docs/repositories/cleanup-policy).
