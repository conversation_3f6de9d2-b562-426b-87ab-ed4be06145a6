# API Reference Guide

This document provides a comprehensive reference for all API endpoints available in the GHQ BPIT NCOP IRO Street Turn Matching Service.

## Overview

The API is built using FastAPI and provides endpoints for:

-   **Street Turn Matching**: Core matching algorithm operations
-   **System Management**: Configuration and health monitoring
-   **Pub/Sub Integration**: Message queue processing
-   **Data Operations**: Report loading and processing

## Base URL

-   **Local Development**: `http://localhost:8001`
-   **Production**: `https://your-cloud-run-service.run.app`

## Authentication

All API endpoints require authentication using an API key passed in the header:

```http
X-API-Key: [your_key_in_here]
```

## API Endpoints

### Base Endpoints

#### Welcome Message

```http
GET /api/v1/
```

**Description**: Returns basic API information and welcome message.

**Headers**:

-   `X-API-Key: string` (required)

**Response**:

```json
{
    "message": "Hello from the API"
}
```

#### Health Check

```http
GET /api/v1/health
```

**Description**: Verifies the API is running properly.

**Headers**:

-   `X-API-Key: string` (required)

**Response**:

```json
{
    "status": "OK"
}
```

### Matching Endpoints

#### Create Street Turn Match

```http
POST /api/v1/matching/match
```

**Description**: Performs street-turn matching between import deliveries and export pickups.

**Headers**:

-   `X-API-Key: string` (required)
-   `Content-Type: application/json`

**Request Body**: None (uses system configuration)

**Response**:

```json
{
    "match_id": "matching_results_20250709T123456_abc123",
    "status": "completed",
    "message": "Matching process completed successfully",
    "results": {
        "total_matches": 45,
        "export_path": "gs://bucket/results/matching_results_20250709T123456_abc123/"
    }
}
```

**Process Flow**:

1. Sets up temporary result folder
2. Fetches necessary data for matching
3. Creates possible matches based on distance/time thresholds
4. Enriches matches with route information
5. Exports results to Google Sheets

#### Reload Data

```http
POST /api/v1/matching/reload
```

**Description**: Reloads data from data sources for specific nodes.

**Headers**:

-   `X-API-Key: string` (required)
-   `Content-Type: application/json`

**Request Body**:

```json
{
    "node_cd": "USATL63,USORF01" // Optional: comma-separated node codes
}
```

**Response**:

```json
{
    "status": "success",
    "message": "Data reloaded successfully",
    "nodes_processed": ["USATL63", "USORF01"]
}
```

#### Reoptimize Matches

```http
POST /api/v1/matching/reoptimize
```

**Description**: Reoptimizes existing matches with new parameters.

**Headers**:

-   `X-API-Key: string` (required)
-   `Content-Type: application/json`

**Request Body**:

```json
{
    "maximum_distance_thres_km": 643.74,
    "time_tolerance_thres": 0,
    "cop_list": ["COP123", "COP456"],
    "node_cd": "USATL63,USORF01",
    "street_turn_cost_option": "FORMULA_2",
    "primary_ranking_options": "street_turn_total_cost"
}
```

**Response**:

```json
{
    "match_id": "matching_results_20250709T123456_def789",
    "status": "completed",
    "message": "Reoptimization completed successfully",
    "parameters_used": {
        "maximum_distance_thres_km": 643.74,
        "time_tolerance_thres": 0,
        "street_turn_cost_option": "FORMULA_2"
    }
}
```

#### Reoptimize V2

```http
POST /api/v1/matching/reoptimize-v2
```

**Description**: Enhanced reoptimization with additional features.

**Headers**:

-   `X-API-Key: string` (required)
-   `Content-Type: application/json`

**Request Body**:

```json
{
    "maximum_distance_thres_km": 643.74,
    "time_tolerance_thres": 0,
    "cop_list": ["COP123", "COP456"],
    "node_cd": "USATL63,USORF01",
    "street_turn_cost_option": "FORMULA_2",
    "primary_ranking_options": "street_turn_total_cost",
    "additional_filters": {}
}
```

### Configuration Endpoints

#### Get System Configuration

```http
GET /api/v1/system-config
```

**Description**: Retrieves current system configuration.

**Headers**:

-   `X-API-Key: string` (required)

**Response**:

```json
{
    "nodes": ["USATL63", "USORF01", "USORF03"],
    "distance_method": "google_maps",
    "maximum_distance_thres_km": 643.74,
    "max_import_matches": 1,
    "max_export_matches": 1,
    "criteria": "distance",
    "time_tolerance_thres": 0,
    "street_turn_cost_options": ["FORMULA_2"],
    "ranking_priority_options": ["street_turn_total_cost", "cost_save"],
    "primary_ranking_options": "street_turn_total_cost"
}
```

#### Update System Configuration

```http
PUT /api/v1/system-config
```

**Description**: Updates system configuration parameters.

**Headers**:

-   `X-API-Key: string` (required)
-   `Content-Type: application/json`

**Request Body**:

```json
{
    "nodes": ["USATL63", "USORF01"],
    "maximum_distance_thres_km": 500.0,
    "time_tolerance_thres": 2,
    "primary_ranking_options": "cost_save"
}
```

**Response**:

```json
{
    "status": "success",
    "message": "System configuration updated successfully",
    "updated_fields": ["nodes", "maximum_distance_thres_km", "time_tolerance_thres"]
}
```

### Pub/Sub Endpoints

#### Execute Code via Pub/Sub

```http
POST /api/v1/pubsub/exec-code
```

**Description**: Processes Pub/Sub messages for batch operations.

**Headers**:

-   `X-API-Key: string` (required)
-   `Content-Type: application/json`

**Request Body**:

```json
{
    "message": {
        "data": "base64-encoded-payload",
        "attributes": {
            "action": "match",
            "trigger_timestamp": "2025-07-09T12:34:56Z"
        }
    },
    "subscription": "projects/project-id/subscriptions/subscription-name"
}
```

## Request/Response Formats

### Common Data Types

#### Node Codes

Valid node codes include:

-   `USATL63` - Atlanta
-   `USORF01`, `USORF03`, `USORF61` - Norfolk
-   `USJAX01`, `USJAX03`, `USJAX65` - Jacksonville
-   `USLAX01`, `USLAX03` - Los Angeles
-   `USCHI79` - Chicago

#### Street Turn Cost Options

-   `FORMULA_2` - Standard cost calculation formula

#### Ranking Priority Options

-   `street_turn_total_cost` - Total cost optimization
-   `cost_save` - Cost savings optimization
-   `dist_sav_km` - Distance savings optimization
-   `time_gap` - Time gap optimization

### Error Responses

#### 400 Bad Request

```json
{
    "detail": "Invalid request parameters",
    "errors": [
        {
            "field": "maximum_distance_thres_km",
            "message": "Must be a positive number"
        }
    ]
}
```

#### 401 Unauthorized

```json
{
    "detail": "Invalid API Key"
}
```

#### 422 Validation Error

```json
{
    "detail": [
        {
            "loc": ["body", "node_cd"],
            "msg": "field required",
            "type": "value_error.missing"
        }
    ]
}
```

#### 500 Internal Server Error

```json
{
    "detail": "Failed to perform matching: Database connection error"
}
```

## Example Usage

### Python with requests

```python
import requests

# Configuration
BASE_URL = "http://localhost:8001"
API_KEY = "[your_key_in_here]"
HEADERS = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}

# Health check
response = requests.get(f"{BASE_URL}/api/v1/health", headers=HEADERS)
print(response.json())

# Start matching process
response = requests.post(f"{BASE_URL}/api/v1/matching/match", headers=HEADERS)
print(response.json())

# Reoptimize with custom parameters
payload = {
    "maximum_distance_thres_km": 500.0,
    "node_cd": "USATL63,USORF01",
    "street_turn_cost_option": "FORMULA_2"
}
response = requests.post(
    f"{BASE_URL}/api/v1/matching/reoptimize",
    headers=HEADERS,
    json=payload
)
print(response.json())
```

### cURL Examples

```bash
# Health check
curl -X GET "http://localhost:8001/api/v1/health" \
  -H "X-API-Key: [your_key_in_here]"

# Start matching
curl -X POST "http://localhost:8001/api/v1/matching/match" \
  -H "X-API-Key: [your_key_in_here]" \
  -H "Content-Type: application/json"

# Reload data for specific nodes
curl -X POST "http://localhost:8001/api/v1/matching/reload" \
  -H "X-API-Key: [your_key_in_here]" \
  -H "Content-Type: application/json" \
  -d '{"node_cd": "USATL63,USORF01"}'

# Update system configuration
curl -X PUT "http://localhost:8001/api/v1/system-config" \
  -H "X-API-Key: [your_key_in_here]" \
  -H "Content-Type: application/json" \
  -d '{
    "maximum_distance_thres_km": 500.0,
    "time_tolerance_thres": 2
  }'
```

### JavaScript/Node.js

```javascript
const axios = require("axios");

const API_CONFIG = {
    baseURL: "http://localhost:8001",
    headers: {
        "X-API-Key": "[your_key_in_here]",
        "Content-Type": "application/json",
    },
};

// Health check
async function healthCheck() {
    try {
        const response = await axios.get("/api/v1/health", API_CONFIG);
        console.log(response.data);
    } catch (error) {
        console.error("Health check failed:", error.response?.data);
    }
}

// Start matching process
async function startMatching() {
    try {
        const response = await axios.post("/api/v1/matching/match", {}, API_CONFIG);
        console.log("Matching started:", response.data);
    } catch (error) {
        console.error("Matching failed:", error.response?.data);
    }
}
```

## Rate Limiting and Performance

### Request Limits

-   **Rate Limit**: 100 requests per minute per API key
-   **Concurrent Requests**: Maximum 5 concurrent matching operations
-   **Timeout**: 300 seconds for matching operations

### Performance Considerations

-   **Matching Operations**: Can take 2-10 minutes depending on data size
-   **Data Reloading**: Typically completes within 30 seconds
-   **Configuration Updates**: Immediate effect

### Monitoring Endpoints

Monitor API performance using:

-   Health check endpoint for basic availability
-   CloudWatch/GCP Monitoring for detailed metrics
-   Application logs for detailed operation tracking

## WebSocket Support (Future)

_Note: WebSocket support for real-time updates is planned for future releases._

## API Versioning

-   **Current Version**: v1
-   **Versioning Strategy**: URL path versioning (`/api/v1/`)
-   **Backward Compatibility**: Maintained for at least 6 months after new version release

---

**Last Updated**: July 9, 2025
**Version**: 1.0
**Maintained By**: Development Team

## Related Documentation

-   [README](../README.md) - Main project documentation
-   [Project Structure](./project_structure.md) - Codebase organization
-   [Deployment Guide](./deployment_guide.md) - Application deployment procedures
-   [Troubleshooting Guide](./troubleshooting_guide.md) - Common issues and solutions
