# Deployment Guide

This document provides comprehensive instructions for deploying the GHQ BPIT NCOP IRO application both locally using Docker and to Google Cloud Run for testing and production environments.

## Overview

The application supports multiple deployment strategies:

-   **Local Docker**: For development and testing
-   **Google Cloud Run**: For staging and production environments
-   **CI/CD Pipeline**: Automated deployments via GitHub Actions

## Prerequisites

### Required Tools

-   **Docker**: Version 20.10 or higher
-   **Google Cloud SDK (gcloud)**: Latest version
-   **Poetry**: For dependency management
-   **Git**: For version control

### Required Access

-   **Google Cloud Project**: Access to target GCP project
-   **IAM Permissions**: Appropriate roles for Cloud Run and Artifact Registry
-   **GitHub Repository**: Access to the source code repository

### Environment Setup

-   **Configuration files**: `.env` and `config/config.toml` properly configured
-   **Service account**: GCP service account with required permissions
-   **API keys**: Google Maps API key and other required credentials

## Local Docker Deployment

### Building the Docker Image

#### 1. Prepare Configuration Files

Ensure your local configuration files are properly set up:

```bash
# Copy and configure environment variables
cp .env.sample .env

# Copy and configure application settings
cp config/config.toml.sample config/config.toml
```

Edit both files with your local development values.

#### 2. Build the Docker Image

Build the Docker image locally:

```bash
# Build with default tag
docker build -t ghq-bpit-ncop-iro:local .

# Build with specific environment
docker build --build-arg ENVIRONMENT=development -t ghq-bpit-ncop-iro:dev .

# Build with build information
docker build \
  --build-arg ENVIRONMENT=development \
  --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
  --build-arg GIT_SHA=$(git rev-parse --short HEAD) \
  -t ghq-bpit-ncop-iro:dev-$(git rev-parse --short HEAD) .
```

#### 3. Run the Container Locally

Run the container with environment variables:

```bash
# Basic run
docker run -p 8001:8001 ghq-bpit-ncop-iro:local

# Run with environment file
docker run -p 8001:8001 --env-file .env ghq-bpit-ncop-iro:local

# Run with volume mounts for development
docker run -p 8001:8001 \
  --env-file .env \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/logs:/app/logs \
  ghq-bpit-ncop-iro:local

# Run in detached mode
docker run -d -p 8001:8001 --env-file .env --name iro-app ghq-bpit-ncop-iro:local
```

#### 4. Verify Local Deployment

Check that the application is running:

```bash
# Check container status
docker ps

# View container logs
docker logs iro-app

# Follow logs in real-time
docker logs -f iro-app

# Access the application
curl http://localhost:8001/health

# Access API documentation
open http://localhost:8001/docs
```

### Docker Development Workflow

#### Development with Hot Reload

For development with code changes:

```bash
# Build development image
docker build -t ghq-bpit-ncop-iro:dev .

# Run with volume mount for live editing
docker run -p 8001:8001 \
  --env-file .env \
  -v $(pwd)/src:/app/src \
  -v $(pwd)/config:/app/config \
  ghq-bpit-ncop-iro:dev
```

#### Multi-stage Build for Optimization

The Dockerfile uses multi-stage builds for optimization:

```dockerfile
# Development stage
FROM python:3.12-slim as development
# Development dependencies and tools

# Production stage
FROM python:3.12-slim as production
# Only production dependencies and optimized image
```

Build specific stages:

```bash
# Build development stage
docker build --target development -t ghq-bpit-ncop-iro:dev .

# Build production stage (default)
docker build --target production -t ghq-bpit-ncop-iro:prod .
```

### Docker Compose (Optional)

Create a `docker-compose.yml` for easier local development:

```yaml
version: "3.8"

services:
    app:
        build:
            context: .
            target: development
        ports:
            - "8001:8001"
        env_file:
            - .env
        volumes:
            - ./src:/app/src
            - ./config:/app/config
            - ./logs:/app/logs
        restart: unless-stopped

    # Add additional services if needed (Redis, PostgreSQL, etc.)
```

Run with Docker Compose:

```bash
# Start services
docker-compose up

# Start in detached mode
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## Google Cloud Run Deployment

### Authentication Setup

#### 1. Install and Configure gcloud

```bash
# Install Google Cloud SDK (if not already installed)
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Initialize and authenticate
gcloud init
gcloud auth login

# Set default project
gcloud config set project your-project-id

# Configure Docker authentication
gcloud auth configure-docker asia-southeast1-docker.pkg.dev
```

#### 2. Verify Permissions

Ensure your account has the required IAM roles:

```bash
# Check current account
gcloud auth list

# Verify project access
gcloud projects describe your-project-id

# Check IAM roles (requires appropriate permissions)
gcloud projects get-iam-policy your-project-id
```

Required roles:

-   `Cloud Run Admin` or `Cloud Run Developer`
-   `Artifact Registry Writer`
-   `Service Account User` (for service accounts)

### Manual Deployment

#### 1. Build and Push to Artifact Registry

```bash
# Set environment variables
export PROJECT_ID="one-global-dilab-matchback-dev"
export REGION="asia-southeast1"
export REPO_NAME="iro-repo"
export IMAGE_NAME="street-turn-matching-service"

# Build and tag image
docker build -t ${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${IMAGE_NAME}:manual .

# Push to Artifact Registry
docker push ${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${IMAGE_NAME}:manual
```

#### 2. Deploy to Cloud Run

```bash
# Deploy new service
gcloud run deploy street-turn-matching-service \
  --image=${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${IMAGE_NAME}:manual \
  --platform=managed \
  --region=${REGION} \
  --allow-unauthenticated \
  --port=8001 \
  --memory=2Gi \
  --cpu=1 \
  --min-instances=0 \
  --max-instances=10 \
  --set-env-vars="ENVIRONMENT=production"

# Update existing service
gcloud run services update street-turn-matching-service \
  --image=${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${IMAGE_NAME}:manual \
  --region=${REGION}
```

#### 3. Configure Environment Variables

Set environment variables for Cloud Run:

```bash
# Set environment variables
gcloud run services update street-turn-matching-service \
  --region=${REGION} \
  --set-env-vars="ENVIRONMENT=production,PORT=8001,HOST=0.0.0.0" \
  --set-secrets="SERVICE_ACCOUNT_ENCODE=gcp-service-account:latest" \
  --set-secrets="GMAP_API_KEY=google-maps-api-key:latest" \
  --set-secrets="API_KEY=api-authentication-key:latest"
```

#### 4. Configure Service Account

Assign a service account to Cloud Run:

```bash
# Create service account (if not exists)
gcloud iam service-accounts create cloud-run-iro-service \
  --display-name="Cloud Run IRO Service Account"

# Grant necessary roles
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:cloud-run-iro-service@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:cloud-run-iro-service@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/storage.objectAdmin"

# Update Cloud Run service
gcloud run services update street-turn-matching-service \
  --region=${REGION} \
  --service-account="cloud-run-iro-service@${PROJECT_ID}.iam.gserviceaccount.com"
```

### Automated Deployment via CI/CD

The application includes automated deployment through GitHub Actions. See the [CI/CD Pipeline Guide](./cicd_guide.md) for detailed information.

#### Environment-Based Deployments

| Environment         | Trigger                           | Image Tag                    | Cloud Run Service              |
| ------------------- | --------------------------------- | ---------------------------- | ------------------------------ |
| **Development**     | `develop` branch push             | `develop`                    | `iro-service-dev`              |
| **Staging**         | `staging` branch push             | `staging`                    | `iro-service-staging`          |
| **Production**      | `main` branch push or version tag | `latest`, `prod`, or version | `iro-service-prod`             |
| **Feature Testing** | Manual workflow dispatch          | `feature-{branch}-{sha}`     | `iro-service-feature-{branch}` |

#### Triggering Manual Deployments

For feature branch testing:

1. Navigate to GitHub repository
2. Go to **Actions** tab
3. Select **"Build and Push Docker Image"** workflow
4. Click **"Run workflow"**
5. Select your feature branch
6. Choose appropriate branch type
7. Monitor deployment progress

### Environment-Specific Configurations

#### Development Environment

```bash
# Deploy to development
gcloud run deploy iro-service-dev \
  --image=${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${IMAGE_NAME}:develop \
  --platform=managed \
  --region=${REGION} \
  --allow-unauthenticated \
  --memory=1Gi \
  --cpu=0.5 \
  --min-instances=0 \
  --max-instances=3 \
  --set-env-vars="ENVIRONMENT=development"
```

#### Staging Environment

```bash
# Deploy to staging
gcloud run deploy iro-service-staging \
  --image=${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${IMAGE_NAME}:staging \
  --platform=managed \
  --region=${REGION} \
  --allow-unauthenticated \
  --memory=1.5Gi \
  --cpu=1 \
  --min-instances=0 \
  --max-instances=5 \
  --set-env-vars="ENVIRONMENT=staging"
```

#### Production Environment

```bash
# Deploy to production
gcloud run deploy iro-service-prod \
  --image=${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${IMAGE_NAME}:latest \
  --platform=managed \
  --region=${REGION} \
  --no-allow-unauthenticated \
  --memory=2Gi \
  --cpu=2 \
  --min-instances=1 \
  --max-instances=10 \
  --set-env-vars="ENVIRONMENT=production"
```

## Testing Deployments

### Health Checks

#### Local Docker Testing

```bash
# Health check
curl http://localhost:8001/health

# API functionality test
curl -H "Content-Type: application/json" \
     -H "X-API-Key: [your_key_in_here]" \
     -X POST \
     -d '{"test": "data"}' \
     http://localhost:8001/api/v1/test
```

#### Cloud Run Testing

```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe street-turn-matching-service \
  --region=${REGION} \
  --format="value(status.url)")

# Health check
curl ${SERVICE_URL}/health

# API functionality test
curl -H "Content-Type: application/json" \
     -H "X-API-Key: [your_key_in_here]" \
     -X POST \
     -d '{"test": "data"}' \
     ${SERVICE_URL}/api/v1/test
```

### Load Testing

#### Simple Load Test

```bash
# Install Apache Bench (if not available)
# On macOS: brew install httpd
# On Ubuntu: sudo apt-get install apache2-utils

# Basic load test
ab -n 100 -c 10 ${SERVICE_URL}/health

# Load test with authentication
ab -n 100 -c 10 -H "X-API-Key: [your_key_in_here]" ${SERVICE_URL}/api/v1/test
```

#### Advanced Load Testing

Use tools like:

-   **Artillery**: `npm install -g artillery`
-   **k6**: Load testing tool
-   **Locust**: Python-based load testing

### Monitoring and Logging

#### Cloud Run Logs

```bash
# View recent logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=street-turn-matching-service" \
  --limit=50 \
  --format="table(timestamp,textPayload)"

# Follow logs in real-time
gcloud logging tail "resource.type=cloud_run_revision AND resource.labels.service_name=street-turn-matching-service"

# Filter error logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=street-turn-matching-service AND severity>=ERROR" \
  --limit=20
```

#### Performance Monitoring

Monitor key metrics:

-   **Request latency**
-   **Memory usage**
-   **CPU utilization**
-   **Error rates**
-   **Instance scaling**

Access monitoring via:

-   Google Cloud Console → Cloud Run → Service → Metrics
-   Cloud Monitoring dashboards
-   Application logs and custom metrics

## Troubleshooting

### Common Docker Issues

#### Build Failures

```bash
# Clear Docker cache
docker system prune -af

# Build with no cache
docker build --no-cache -t ghq-bpit-ncop-iro:local .

# Check Dockerfile syntax
docker build --dry-run -t test .
```

#### Runtime Issues

```bash
# Check container logs
docker logs container-name

# Execute into running container
docker exec -it container-name /bin/bash

# Check resource usage
docker stats container-name
```

### Common Cloud Run Issues

#### Deployment Failures

```bash
# Check deployment status
gcloud run services describe street-turn-matching-service \
  --region=${REGION}

# View deployment history
gcloud run revisions list \
  --service=street-turn-matching-service \
  --region=${REGION}

# Check service configuration
gcloud run services describe street-turn-matching-service \
  --region=${REGION} \
  --format="export"
```

#### Authentication Issues

```bash
# Verify authentication
gcloud auth list

# Re-authenticate if needed
gcloud auth login

# Check project configuration
gcloud config list

# Verify Docker authentication
gcloud auth configure-docker asia-southeast1-docker.pkg.dev
```

#### Performance Issues

```bash
# Check service configuration
gcloud run services describe street-turn-matching-service \
  --region=${REGION} \
  --format="value(spec.template.spec.containers[0].resources)"

# Update resource limits
gcloud run services update street-turn-matching-service \
  --region=${REGION} \
  --memory=4Gi \
  --cpu=2
```

### Error Resolution

#### Container Won't Start

1. **Check application logs**
2. **Verify environment variables**
3. **Validate configuration files**
4. **Check resource limits**
5. **Review health check endpoints**

#### High Memory Usage

1. **Monitor memory patterns**
2. **Check for memory leaks**
3. **Optimize application code**
4. **Increase memory limits**
5. **Review caching strategies**

#### Slow Response Times

1. **Analyze request patterns**
2. **Check database performance**
3. **Review external API calls**
4. **Optimize algorithms**
5. **Consider caching strategies**

## Security Considerations

### Container Security

-   **Use minimal base images**
-   **Run as non-root user**
-   **Scan for vulnerabilities**
-   **Keep dependencies updated**
-   **Use read-only root filesystem**

### Cloud Run Security

-   **Use IAM for authentication**
-   **Configure VPC connectors if needed**
-   **Set appropriate ingress settings**
-   **Use Secret Manager for sensitive data**
-   **Enable audit logging**

### Network Security

-   **Configure firewall rules**
-   **Use HTTPS only**
-   **Implement rate limiting**
-   **Monitor for suspicious activity**
-   **Use VPC for internal services**

## Best Practices

### Development Workflow

1. **Test locally first**: Always test Docker builds locally
2. **Use feature branches**: Deploy feature branches to isolated environments
3. **Gradual rollouts**: Deploy to development → staging → production
4. **Monitor deployments**: Watch metrics during and after deployments
5. **Have rollback plans**: Prepare rollback procedures for issues

### Production Deployments

1. **Use semantic versioning**: Tag releases with version numbers
2. **Implement health checks**: Ensure proper health check endpoints
3. **Set resource limits**: Configure appropriate CPU and memory limits
4. **Monitor performance**: Set up monitoring and alerting
5. **Document changes**: Maintain deployment logs and change records

### Security Best Practices

1. **Rotate secrets regularly**: Update API keys and credentials
2. **Use least privilege**: Grant minimal required permissions
3. **Monitor access**: Track who deploys what and when
4. **Audit configurations**: Regularly review security settings
5. **Backup strategies**: Implement backup and recovery procedures

---

**Last Updated**: July 9, 2025
**Version**: 1.0
**Maintained By**: DevOps Team

## Related Documentation

-   [README](../README.md) - Main project documentation
-   [Project Structure](./project_structure.md) - Codebase organization
-   [CI/CD Pipeline Guide](./cicd_guide.md) - Automated deployment processes
-   [Docker Strategy Guide](./docker_strategy.md) - Container management strategy
-   [Architecture Diagram](./architecture_diagram.md) - System architecture overview
