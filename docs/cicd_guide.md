# CI/CD Pipeline Guide

This document provides a comprehensive guide for the CI/CD pipeline implemented in this project using GitHub Actions.

## Overview

The CI/CD pipeline automatically builds, tests, and deploys the Street Turn Matching Service Docker image to Google Cloud Platform (GCP) Artifact Registry based on different branch strategies and deployment environments.

## Workflow File Location

```
.github/workflows/build_dockerimage.yml
```

## Pipeline Architecture

### Environments

| Environment         | Branch/Trigger                           | Deployment Strategy        |
| ------------------- | ---------------------------------------- | -------------------------- |
| **Production**      | `main` branch, version tags (`v*.*.*`)   | Automatic deployment       |
| **Staging**         | `staging` branch                         | Automatic deployment       |
| **Development**     | `develop` branch                         | Automatic deployment       |
| **Feature Testing** | `feature/*`, `feat/*`, `test/*` branches | **Manual deployment only** |

## Triggers

### Automatic Triggers

The pipeline automatically runs on:

1. **Push Events**:

    - `main` branch
    - `staging` branch
    - `develop` branch
    - Version tags (`v*.*.*`)

2. **Pull Request Events**:
    - PRs targeting `main`, `staging`, or `develop` branches

### Manual Triggers

Feature branches (`feature/*`, `feat/*`, `test/*`) can only be triggered manually using GitHub's workflow dispatch feature.

#### How to Manually Trigger Feature Branch Builds

1. Navigate to your repository on GitHub
2. Go to the **Actions** tab
3. Select **"Build and Push Docker Image"** workflow
4. Click **"Run workflow"** button
5. Select the target branch from the dropdown
6. Choose the appropriate branch type:
    - `feature` - for feature/ branches
    - `feat` - for feat/ branches
    - `test` - for test/ branches
7. Click **"Run workflow"**

## Pipeline Stages

### 1. Lint Stage

**Purpose**: Code quality checks and static analysis

**Runs on**:

-   Ubuntu Latest
-   Python versions: 3.11, 3.12

**Steps**:

-   Code checkout
-   Python environment setup
-   Install linting tools (Ruff, MyPy)
-   Run Ruff linting with auto-fix
-   Run Ruff formatting
-   Run MyPy type checking

**Tools Used**:

-   **Ruff**: Fast Python linter and formatter
-   **MyPy**: Static type checker for Python

### 2. Build and Push Artifact Stage

**Purpose**: Build and push Docker images to GCP Artifact Registry

**Dependencies**: Requires `lint` stage to pass

**Steps**:

-   Code checkout with full git history
-   GCP authentication using service account
-   Configure Docker with gcloud credentials
-   Set up Docker Buildx
-   Generate dynamic Docker tags
-   Build and push Docker image with caching

**Docker Registry**:

```
asia-southeast1-docker.pkg.dev/one-global-dilab-matchback-dev/iro-repo/street-turn-matching-service
```

### 3. Deployment Stages

#### Development Deployment

-   **Triggers**: `develop` branch pushes or PRs targeting `develop`
-   **Environment**: `development`
-   **Strategy**: Automatic deployment

#### Staging Deployment

-   **Triggers**: `staging` branch pushes or PRs targeting `staging`
-   **Environment**: `staging`
-   **Strategy**: Automatic deployment

#### Production Deployment

-   **Triggers**: `main` branch pushes or version tags
-   **Environment**: `production`
-   **Strategy**: Automatic deployment with concurrency protection
-   **Concurrency**: Single deployment at a time (no cancellation)

#### Feature Testing Deployment

-   **Triggers**: Manual workflow dispatch for feature branches
-   **Environment**: `feature-testing`
-   **Strategy**: Manual deployment only
-   **Branches**: `feature/*`, `feat/*`, `test/*`

## Docker Image Tagging Strategy

### Production Tags (main branch)

-   `latest`
-   `prod`
-   Semantic version tags (`v1.0.0`, `v1.0`)

### Staging Tags

-   `staging`
-   `staging-{git-sha}`

### Development Tags

-   `develop`
-   `pr-{pr-number}` (for pull requests)
-   `sha-{short-sha}`

### Feature Branch Tags

-   `{branch-name}-{git-sha}`
-   `feature-{branch-name}` (for feature/ branches)
-   `feat-{branch-name}` (for feat/ branches)
-   `test-{branch-name}` (for test/ branches)

## Environment Variables

| Variable     | Value                            | Description                       |
| ------------ | -------------------------------- | --------------------------------- |
| `PROJECT_ID` | `one-global-dilab-matchback-dev` | GCP Project ID                    |
| `REGION`     | `asia-southeast1`                | GCP Region                        |
| `REPO_NAME`  | `iro-repo`                       | Artifact Registry repository name |
| `IMAGE_NAME` | `street-turn-matching-service`   | Docker image name                 |

## Required Secrets

### GitHub Secrets

| Secret Name     | Description                          | Required For                   |
| --------------- | ------------------------------------ | ------------------------------ |
| `DEV_GCP_CREDS` | GCP Service Account JSON credentials | Authentication to GCP services |

### GCP Service Account Permissions

The service account should have the following IAM roles:

-   `Artifact Registry Writer`
-   `Storage Object Viewer` (for build cache)

## Branch Strategy

### Gitflow-Based Strategy

```
main (production)
├── staging (pre-production)
├── develop (development)
├── feature/feature-name (manual deployment)
├── feat/feature-name (manual deployment)
└── test/test-name (manual deployment)
```

### Branch Protection Rules (Recommended)

1. **main branch**:

    - Require pull request reviews
    - Require status checks to pass
    - Restrict pushes to admins only

2. **staging branch**:

    - Require pull request reviews
    - Require status checks to pass

3. **develop branch**:
    - Require status checks to pass
    - Allow squash merging

## Deployment Flow

```mermaid
graph TD
    A[Code Push] --> B{Which Branch?}
    B -->|main| C[Production Deploy]
    B -->|staging| D[Staging Deploy]
    B -->|develop| E[Development Deploy]
    B -->|feature/*| F[Manual Trigger Required]

    F --> G[Workflow Dispatch]
    G --> H[Feature Testing Deploy]

    C --> I[Production Environment]
    D --> J[Staging Environment]
    E --> K[Development Environment]
    H --> L[Feature Testing Environment]
```

## Build Optimization

### Caching Strategy

-   **Docker Layer Caching**: Uses GitHub Actions cache for Docker build layers
-   **Python Dependencies**: Pip cache enabled for faster dependency installation

### Build Args

-   `BUILD_DATE`: Timestamp of the build
-   `GIT_SHA`: Short git commit SHA
-   `ENVIRONMENT`: Target environment (production/staging/development)

## Monitoring and Troubleshooting

### Common Issues

1. **Build Failures**:

    - Check lint stage output for code quality issues
    - Verify Python syntax and type annotations
    - Review Docker build logs

2. **Authentication Failures**:

    - Verify `DEV_GCP_CREDS` secret is properly configured
    - Check GCP service account permissions

3. **Manual Trigger Not Working**:
    - Ensure you're on a feature branch (`feature/`, `feat/`, `test/`)
    - Check that workflow dispatch is triggered from the correct branch

### Viewing Logs

1. Go to **Actions** tab in GitHub repository
2. Select the specific workflow run
3. Click on individual job steps to view detailed logs
4. Download logs for offline analysis if needed

## Best Practices

### For Developers

1. **Feature Development**:

    - Create feature branches with appropriate prefixes (`feature/`, `feat/`, `test/`)
    - Test manually before creating pull requests
    - Use descriptive branch names

2. **Code Quality**:

    - Run linting locally before pushing
    - Fix type hints and formatting issues
    - Follow Python coding standards

3. **Testing**:
    - Use manual deployment for feature branches to test in isolation
    - Validate functionality before merging to develop

### For DevOps

1. **Security**:

    - Regularly rotate GCP service account keys
    - Review and audit deployment permissions
    - Monitor failed authentication attempts

2. **Performance**:

    - Monitor build times and optimize Docker layers
    - Keep build cache healthy
    - Review resource usage in GCP

3. **Maintenance**:
    - Update GitHub Actions versions regularly
    - Review and clean up old Docker images
    - Monitor storage costs in Artifact Registry

## Rollback Strategy

### Production Rollback

1. Identify the last known good version tag
2. Create a hotfix branch from that tag
3. Push to main branch (triggers automatic deployment)

### Quick Rollback

1. Use manual workflow dispatch
2. Switch to the stable branch
3. Trigger deployment manually

## Cost Optimization

### Artifact Registry Management

-   Old images are automatically cleaned up based on retention policies
-   Feature branch images should be cleaned up after testing

### Build Optimization

-   Builds only run when necessary (not on feature branch pushes)
-   Docker layer caching reduces build times and costs
-   Parallel matrix builds for efficiency

## Support and Contact

For CI/CD pipeline issues:

1. Check this documentation first
2. Review GitHub Actions logs
3. Contact the DevOps team for infrastructure issues
4. Create an issue in the repository for workflow improvements

---

**Last Updated**: July 9, 2025
**Version**: 1.0
**Maintained By**: DevOps Team
