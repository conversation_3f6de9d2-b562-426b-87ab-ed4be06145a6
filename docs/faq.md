# Frequently Asked Questions (FAQ)

This document answers common questions about the GHQ BPIT NCOP IRO Street Turn Matching Service.

## General Questions

### What is this project about?

The GHQ BPIT NCOP IRO Street Turn Matching Service is a logistics optimization system that matches import and export shipments to optimize container movements, reduce empty container repositioning costs, and improve operational efficiency through street turn operations.

### What technologies does this project use?

-   **Backend**: Python 3.11+ with FastAPI
-   **Cloud Platform**: Google Cloud Platform (BigQuery, Cloud Storage, Cloud Run)
-   **Dependencies**: Poetry for package management
-   **Containerization**: Docker
-   **CI/CD**: GitHub Actions
-   **APIs**: Google Maps API, Google Sheets API, Google Drive API

### Who should use this system?

This system is designed for:

-   Logistics coordinators
-   Container yard operators
-   Shipping line operations teams
-   Supply chain optimization analysts

## Setup and Installation

### Q: What are the minimum system requirements?

**A:**

-   Python 3.11 or higher (but less than 3.13)
-   4GB RAM minimum, 8GB recommended
-   10GB free disk space
-   Internet connection for API access
-   Access to Google Cloud Platform services

### Q: I'm getting Python version errors. What should I do?

**A:** Ensure you have Python 3.11-3.12 installed:

```bash
# Check your Python version
python --version

# If you need to install Python 3.11
# On Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-venv

# On macOS with Homebrew
brew install python@3.11

# On Windows, download from python.org
```

Refer to the [Troubleshooting Guide](./troubleshooting_guide.md) for detailed solutions.

### Q: Poetry installation is failing. How do I fix this?

**A:** Try these steps:

```bash
# Install Poetry using the official installer
curl -sSL https://install.python-poetry.org | python3 -

# Or using pip
pip install poetry

# Verify installation
poetry --version

# If still having issues, try updating pip first
pip install --upgrade pip
```

### Q: How do I get the required API keys and credentials?

**A:**

1. **Google Cloud Platform Service Account**:

    - Go to GCP Console → IAM & Admin → Service Accounts
    - Create a new service account with required permissions
    - Download the JSON key file
    - Base64 encode it: `base64 -i service-account.json`

2. **Google Maps API Key**:

    - Go to GCP Console → APIs & Services → Credentials
    - Create API Key
    - Enable Maps JavaScript API and Distance Matrix API

3. **Google Drive/Sheets Access**:
    - Use the same service account
    - Share folders/sheets with the service account email

## Configuration

### Q: What's the difference between .env and config.toml files?

**A:**

-   **`.env`**: Contains environment variables, API keys, and secrets (not tracked by git)
-   **`config.toml`**: Contains application configuration, GCP resource IDs, and system settings (not tracked by git)

Both have `.sample` versions in the repository that serve as templates.

### Q: How do I know which values to put in the configuration files?

**A:**

1. **Copy the sample files**:

    ```bash
    cp .env.sample .env
    cp config/config.toml.sample config/config.toml
    ```

2. **Edit with your values**:

    - Replace all placeholder values (like `your_bucket_name_here`)
    - Use actual GCP resource names and IDs
    - Set appropriate environment (`development`, `staging`, `production`)

3. **Refer to comments**: Both sample files have detailed comments explaining each setting

### Q: Can I use this project without Google Cloud Platform?

**A:** No, this project is specifically designed for GCP integration. It requires:

-   BigQuery for data storage and processing
-   Google Cloud Storage for file storage
-   Google Maps API for distance calculations
-   Google Sheets/Drive APIs for data integration

## Development

### Q: How do I run the application locally?

**A:**

```bash
# Install dependencies
poetry install

# Set up configuration
cp .env.sample .env
cp config/config.toml.sample config/config.toml
# Edit both files with your values

# Run the application
poetry run uvicorn app:app --reload --host 0.0.0.0 --port 8001

# Access the API
open http://localhost:8001/docs
```

### Q: How do I run tests?

**A:**

```bash
# Run all tests
poetry run pytest

# Run with coverage report
poetry run pytest --cov=src --cov-report=html

# Run specific test file
poetry run pytest tests/unit/test_matching_service.py

# Run tests matching a pattern
poetry run pytest -k "test_distance"
```

### Q: What's the development workflow?

**A:**

1. **Create feature branch**: `git checkout -b feature/ISSUE-123-description`
2. **Make changes**: Follow coding standards and add tests
3. **Test locally**: Run tests and linting
4. **Commit**: Use conventional commit format
5. **Push and PR**: Create pull request for review
6. **CI/CD**: For feature branches, manually trigger deployment if needed

See the [Developer Guide](./developer_guide.md) for detailed workflow.

### Q: Why are my feature branch builds not running automatically?

**A:** By design, `feature/`, `feat/`, and `test/` branches require manual CI/CD triggering to prevent unnecessary builds and costs. To trigger manually:

1. Go to GitHub Actions
2. Select "Build and Push Docker Image" workflow
3. Click "Run workflow"
4. Choose your branch type and run

## API Usage

### Q: How do I authenticate with the API?

**A:** Include the API key in the request header:

```bash
curl -H "X-API-Key: [your_key_in_here]" http://localhost:8001/api/v1/health
```

### Q: Where can I find API documentation?

**A:**

-   **Interactive docs**: http://localhost:8001/docs (Swagger UI)
-   **Alternative docs**: http://localhost:8001/redoc
-   **Detailed reference**: [API Reference Guide](./api_reference.md)

### Q: What are the main API endpoints?

**A:** Key endpoints include:

-   `GET /api/v1/health` - Health check
-   `POST /api/v1/matching/street-turn` - Street turn matching
-   `POST /api/v1/load-report-data` - Load report data
-   `GET /api/v1/system/config` - System configuration

See [API Reference](./api_reference.md) for complete documentation.

## Deployment

### Q: How do I deploy to Google Cloud Run?

**A:**

1. **Local Docker testing**:

    ```bash
    docker build -t ghq-iro:latest .
    docker run --env-file .env -p 8001:8001 ghq-iro:latest
    ```

2. **CI/CD deployment**: Push to `main`, `staging`, or `develop` branches triggers automatic deployment

3. **Manual deployment**: Use the deployment guide procedures

See the [Deployment Guide](./deployment_guide.md) for detailed instructions.

### Q: How do I check if my deployment is working?

**A:**

```bash
# Health check
curl -H "X-API-Key: [your_key_in_here]" https://your-service-url/api/v1/health

# Check logs in GCP Console
gcloud logs read --service=your-service-name --limit=50
```

### Q: What environments are available?

**A:**

-   **Development**: Local development and testing
-   **Staging**: Pre-production testing environment
-   **Production**: Live production environment

Each environment has its own Cloud Run service and configuration.

## Troubleshooting

### Q: I'm getting "ModuleNotFoundError" errors. How do I fix this?

**A:**

```bash
# Ensure you're in the poetry virtual environment
poetry shell

# Or run commands with poetry
poetry run python your_script.py

# Reinstall dependencies if needed
poetry install --no-cache
```

### Q: The application is running but API calls are failing. What should I check?

**A:**

1. **Check API key**: Ensure `X-API-Key` header is included
2. **Verify configuration**: Check `.env` and `config.toml` values
3. **Check logs**: Look for error messages in application logs
4. **Test endpoints**: Start with simple health check endpoint
5. **Network connectivity**: Ensure external APIs are accessible

### Q: Google API calls are failing. What should I check?

**A:**

1. **Service account permissions**: Ensure proper IAM roles
2. **API enablement**: Enable required APIs in GCP Console
3. **Quota limits**: Check API quota usage
4. **Network access**: Ensure outbound internet connectivity
5. **Key encoding**: Verify service account key is properly base64 encoded

### Q: Where can I find more troubleshooting help?

**A:** Check these resources in order:

1. **[Troubleshooting Guide](./troubleshooting_guide.md)** - Comprehensive issue resolution
2. **Application logs** - Check `logs/` directory or Cloud Logging
3. **GitHub Issues** - Search existing issues or create new one
4. **Documentation** - Review all guides in `docs/` folder

## Performance and Optimization

### Q: The matching process is taking too long. How can I improve performance?

**A:**

1. **Check data volume**: Large datasets take longer to process
2. **Optimize parameters**: Adjust distance thresholds and matching criteria
3. **Use caching**: Enable result caching for repeated queries
4. **Monitor resources**: Check CPU and memory usage
5. **Database optimization**: Ensure BigQuery queries are optimized

### Q: How do I monitor application performance?

**A:**

1. **Cloud Monitoring**: Use GCP monitoring for Cloud Run metrics
2. **Application logs**: Monitor request response times
3. **Health checks**: Regular health endpoint monitoring
4. **Resource usage**: Monitor CPU, memory, and network usage

## Data Management

### Q: How do I load new data into the system?

**A:**

1. **Prepare data**: Format according to expected schema
2. **Upload to Google Drive**: Place in configured folder
3. **Use API endpoint**: Call `/api/v1/load-report-data`
4. **Monitor processing**: Check logs for processing status

### Q: What data formats are supported?

**A:** The system primarily works with:

-   CSV files for report data
-   JSON for API requests/responses
-   BigQuery tables for data storage
-   Google Sheets for configuration data

### Q: How do I backup my data?

**A:** Data is stored in Google Cloud services which provide built-in redundancy:

-   **BigQuery**: Automatic backups and point-in-time recovery
-   **Cloud Storage**: Multi-regional storage with versioning
-   **Export options**: Use BigQuery export or Cloud Storage download

## Security

### Q: How do I secure API keys and credentials?

**A:**

1. **Use environment variables**: Never hardcode keys in source code
2. **Rotate keys regularly**: Update API keys and service accounts periodically
3. **Limit permissions**: Use principle of least privilege for service accounts
4. **Monitor access**: Review access logs regularly
5. **Use secrets management**: Consider GCP Secret Manager for production

### Q: Is the application secure for production use?

**A:** The application includes several security measures:

-   API key authentication
-   HTTPS enforcement
-   Input validation
-   Secure headers
-   Dependency vulnerability scanning

However, additional security hardening may be needed based on your specific requirements.

## Contributing

### Q: How do I contribute to this project?

**A:**

1. **Read documentation**: Start with [Developer Guide](./developer_guide.md) and [CONTRIBUTING.md](../CONTRIBUTING.md)
2. **Set up development environment**: Follow installation instructions
3. **Find an issue**: Look for open issues or discuss new features
4. **Create feature branch**: Follow branching conventions
5. **Submit pull request**: Include tests and documentation

### Q: What should I do before submitting a pull request?

**A:**

1. **Run tests**: Ensure all tests pass locally
2. **Run linting**: Fix any code style issues
3. **Update documentation**: Update relevant docs for your changes
4. **Self-review**: Review your own changes thoroughly
5. **Write good commit messages**: Follow conventional commit format

### Q: How long does code review take?

**A:** Code review timing depends on:

-   **Complexity**: Simple changes review faster
-   **Team availability**: Reviews happen during business hours
-   **Quality**: Well-tested, documented changes review faster
-   **Size**: Smaller PRs review faster than large ones

Typical review time is 1-3 business days.

## Getting Additional Help

### Q: Where can I get help if my question isn't answered here?

**A:**

1. **Search documentation**: Check all docs in the `docs/` folder
2. **GitHub Issues**: Search existing issues or create a new one
3. **Team channels**: Use your organization's communication channels
4. **Code review**: Ask questions during pull request process

### Q: How do I report a bug or request a feature?

**A:**

1. **Search first**: Check if issue already exists
2. **Create GitHub issue**: Use appropriate issue template
3. **Provide details**: Include steps to reproduce, expected vs actual behavior
4. **Add context**: Include environment details, logs, configuration
5. **Follow up**: Respond to questions and test proposed fixes

### Q: How can I improve this documentation?

**A:** Documentation improvements are welcome! You can:

1. **Fix typos or errors**: Submit PR with corrections
2. **Add missing information**: Contribute new sections or clarifications
3. **Improve examples**: Add better code examples or use cases
4. **Update outdated info**: Keep documentation current with code changes

---

**Last Updated**: January 15, 2025
**Version**: 1.0
**Need more help?** Check the [Troubleshooting Guide](./troubleshooting_guide.md) or create a GitHub issue.
