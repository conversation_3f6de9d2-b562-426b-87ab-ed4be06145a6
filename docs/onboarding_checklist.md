# New Team Member Onboarding Checklist

Welcome to the GHQ BPIT NCOP IRO Street Turn Matching Service team! This checklist will help you get up and running quickly.

## Pre-requisites Setup

### Development Environment

-   [ ] **Install Python 3.11-3.12**

    -   Check: `python --version` should show 3.11.x or 3.12.x
    -   [Installation Guide](./troubleshooting_guide.md#python-version-issues) if needed

-   [ ] **Install Poetry**

    -   Install: `curl -sSL https://install.python-poetry.org | python3 -`
    -   Check: `poetry --version`

-   [ ] **Install Git**

    -   Check: `git --version`

-   [ ] **Install Docker** (optional but recommended)

    -   Check: `docker --version`

-   [ ] **Install VS Code or your preferred IDE**
    -   Recommended extensions: Python, Docker, YAML

### Access and Permissions

-   [ ] **GitHub Repository Access**

    -   Can you clone the repository?
    -   Can you create branches and push commits?

-   [ ] **Google Cloud Platform Access**

    -   [ ] GCP Project access: `one-global-dilab-matchback-dev`
    -   [ ] BigQuery access for development datasets
    -   [ ] Cloud Storage bucket access
    -   [ ] Cloud Run service access for deployments
    -   [ ] IAM permissions for service account management

-   [ ] **Google APIs Access**
    -   [ ] Google Maps API access
    -   [ ] Google Sheets API access
    -   [ ] Google Drive API access

## Project Setup

### Repository and Dependencies

-   [ ] **Clone Repository**

    ```bash
    <NAME_EMAIL>:ocean-network-express/ghq-bpit-ncop-iro.git
    cd ghq-bpit-ncop-iro
    ```

-   [ ] **Create Configuration Files**

    ```bash
    cp .env.sample .env
    cp config/config.toml.sample config/config.toml
    ```

-   [ ] **Install Dependencies**

    ```bash
    poetry install
    ```

-   [ ] **Set up Pre-commit Hooks**
    ```bash
    poetry run pre-commit install
    ```

### Configuration

-   [ ] **Configure Environment Variables (`.env`)**

    -   [ ] Set `ENVIRONMENT=development`
    -   [ ] Add your GCP service account key (base64 encoded)
    -   [ ] Add Google Maps API key
    -   [ ] Add Google Drive folder ID for test data
    -   [ ] Set API key for local development
    -   [ ] Configure server settings (HOST, PORT, WORKERS)

-   [ ] **Configure Application Settings (`config/config.toml`)**
    -   [ ] Set GCP bucket name for development
    -   [ ] Set Google Sheets spreadsheet ID for testing
    -   [ ] Configure system nodes to process
    -   [ ] Set database table and dataset IDs for development
    -   [ ] Review and adjust matching criteria settings

### Verification

-   [ ] **Test Local Setup**

    ```bash
    # Run the comprehensive setup verification script
    python verify_setup.py
    # Or with poetry
    poetry run python verify_setup.py
    ```

-   [ ] **Manual Health Check**

    ```bash
    # Run application
    poetry run uvicorn app:app --reload

    # In another terminal, test health endpoint
    curl -H "X-API-Key: [your_key_in_here]" http://localhost:8001/api/v1/health
    ```

-   [ ] **Run Tests**

    ```bash
    poetry run pytest
    ```

-   [ ] **Run Linting**

    ```bash
    poetry run pre-commit run --all-files
    ```

-   [ ] **Access API Documentation**
    -   Visit: http://localhost:8001/docs
    -   Verify you can see the interactive API documentation

## Knowledge Transfer

### Required Reading

-   [ ] **[README.md](../README.md)** - Project overview and quick start
-   [ ] **[Project Structure](./project_structure.md)** - Understand codebase organization
-   [ ] **[Developer Guide](./developer_guide.md)** - Development workflow and standards
-   [ ] **[API Reference](./api_reference.md)** - Understanding the API endpoints
-   [ ] **[CONTRIBUTING.md](../CONTRIBUTING.md)** - Contribution guidelines

### Optional but Recommended Reading

-   [ ] **[Architecture Diagram](./architecture_diagram.md)** - System design overview
-   [ ] **[Sequence Diagram](./sequence_diagram.md)** - Process flows
-   [ ] **[Deployment Guide](./deployment_guide.md)** - How deployments work
-   [ ] **[CI/CD Guide](./cicd_guide.md)** - Understanding the pipeline
-   [ ] **[Docker Strategy](./docker_strategy.md)** - Container management
-   [ ] **[Troubleshooting Guide](./troubleshooting_guide.md)** - Common issues and solutions

### Business Context

-   [ ] **Understand the Problem Domain**

    -   What are street turn operations in logistics?
    -   How does container optimization reduce costs?
    -   What are the key business metrics we're optimizing?

-   [ ] **Learn the Data Flow**

    -   Where does input data come from?
    -   How is data processed and stored?
    -   What are the outputs and how are they used?

-   [ ] **Review Existing Matching Logic**
    -   Understand the matching algorithms
    -   Review scoring and ranking criteria
    -   Learn about distance calculation methods

## Hands-on Tasks

### First Week Tasks

-   [ ] **Task 1: Make a Simple Change**

    -   Create a feature branch: `feature/onboarding-update-readme`
    -   Make a small documentation improvement
    -   Submit a pull request
    -   Go through the review process

-   [ ] **Task 2: Run a Complete Workflow**

    -   Load test data through the API
    -   Run a street turn matching operation
    -   Review the results and understand the output

-   [ ] **Task 3: Debug an Issue**

    -   Introduce a small bug in development
    -   Practice using debugging tools
    -   Fix the issue and verify the solution

-   [ ] **Task 4: Write a Test**
    -   Choose a utility function
    -   Write unit tests with good coverage
    -   Run the tests and verify they pass

### Second Week Tasks

-   [ ] **Task 5: Explore the Codebase**

    -   Trace through a complete API request
    -   Understand how configuration is loaded
    -   Review the data loading and processing pipeline

-   [ ] **Task 6: Performance Analysis**

    -   Profile a matching operation
    -   Identify potential bottlenecks
    -   Propose optimization strategies

-   [ ] **Task 7: Data Investigation**
    -   Explore sample datasets
    -   Understand data quality issues
    -   Review data validation and cleaning processes

## Team Integration

### Communication

-   [ ] **Join Team Communication Channels**

    -   Slack/Discord/Teams channels
    -   Email lists or groups
    -   Meeting invitations

-   [ ] **Schedule Knowledge Transfer Sessions**

    -   [ ] Architecture overview session
    -   [ ] Business logic deep dive
    -   [ ] Deployment and DevOps walkthrough
    -   [ ] Data pipeline explanation

-   [ ] **Shadow Experienced Team Members**
    -   Pair programming sessions
    -   Code review participation
    -   Problem-solving sessions

### Understanding Workflows

-   [ ] **Learn the Development Process**

    -   How are features planned and prioritized?
    -   What is the typical development cycle?
    -   How are releases managed?

-   [ ] **Understand Support Procedures**

    -   How are production issues handled?
    -   What monitoring and alerting is in place?
    -   Who to contact for different types of issues?

-   [ ] **Review Quality Standards**
    -   Code review expectations
    -   Testing requirements
    -   Documentation standards

## Advanced Setup (Optional)

### Local Development Enhancements

-   [ ] **Set up Docker Development Environment**

    ```bash
    docker build -t ghq-iro-dev:latest .
    docker run --env-file .env -p 8001:8001 ghq-iro-dev:latest
    ```

-   [ ] **Configure IDE for Optimal Development**

    -   Set up Python interpreter with Poetry
    -   Configure linting and formatting
    -   Set up debugging configuration

-   [ ] **Set up Local Monitoring**
    -   Configure logging for development
    -   Set up performance monitoring
    -   Install debugging tools

### GCP Development Setup

-   [ ] **Install and Configure gcloud CLI**

    ```bash
    gcloud auth login
    gcloud config set project one-global-dilab-matchback-dev
    ```

-   [ ] **Set up BigQuery Development Access**

    -   Test query execution
    -   Understand data schemas
    -   Practice with development datasets

-   [ ] **Explore Cloud Run Services**
    -   Understand deployment configurations
    -   Review logging and monitoring
    -   Test deployment process in development

## Completion Verification

### Self-Assessment

-   [ ] **Can you run the application locally without help?**
-   [ ] **Can you navigate the codebase confidently?**
-   [ ] **Do you understand the main data flow and business logic?**
-   [ ] **Can you create a branch, make changes, and submit a PR?**
-   [ ] **Can you run tests and understand the results?**
-   [ ] **Do you know where to find help when you're stuck?**

### Team Review

-   [ ] **Code Review with Senior Developer**

    -   Review a pull request together
    -   Discuss code quality and standards
    -   Address any knowledge gaps

-   [ ] **Architecture Discussion**

    -   Explain the system architecture back to a team member
    -   Discuss design decisions and tradeoffs
    -   Identify areas for potential improvement

-   [ ] **Practical Problem Solving**
    -   Work through a real issue or feature request
    -   Demonstrate debugging skills
    -   Show understanding of the development process

## Resources and Support

### Quick Reference

-   **Health Check**: `curl -H "X-API-Key: key" http://localhost:8001/api/v1/health`
-   **API Docs**: http://localhost:8001/docs
-   **Run Tests**: `poetry run pytest`
-   **Run Linting**: `poetry run pre-commit run --all-files`
-   **Start App**: `poetry run uvicorn app:app --reload`

### When You Need Help

1. **Check Documentation First**

    - [FAQ](./faq.md) for common questions
    - [Troubleshooting Guide](./troubleshooting_guide.md) for technical issues
    - [Developer Guide](./developer_guide.md) for development questions

2. **Search Existing Resources**

    - GitHub issues and pull requests
    - Code comments and docstrings
    - Existing team documentation

3. **Ask for Help**
    - Team chat channels for quick questions
    - Schedule time with experienced team members
    - Create GitHub issues for complex problems

### Useful Commands Reference

```bash
# Environment management
poetry shell                          # Activate virtual environment
poetry install                        # Install dependencies
poetry add package-name                # Add new dependency

# Development
poetry run uvicorn app:app --reload    # Start development server
poetry run pytest                     # Run tests
poetry run pytest --cov=src          # Run tests with coverage
poetry run pre-commit run --all-files # Run linting

# Git workflow
git checkout -b feature/my-feature     # Create feature branch
git add . && git commit -m "feat: ..."# Commit changes
git push origin feature/my-feature     # Push branch

# Docker
docker build -t ghq-iro:latest .      # Build image
docker run --env-file .env -p 8001:8001 ghq-iro:latest  # Run container
```

## Completion

**Date Completed**: **\*\***\_\_\_**\*\***

**Completed By**: **\*\***\_\_\_**\*\***

**Reviewed By**: **\*\***\_\_\_**\*\***

**Notes/Feedback**:

---

---

---

Welcome to the team! 🎉

---

**Last Updated**: January 15, 2025
**Version**: 1.0
**Contact**: <EMAIL>
