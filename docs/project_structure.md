# Project Folder Structure

This document provides a detailed overview of the project's folder structure and organization.

## Overview

The GHQ BPIT NCOP IRO project is organized into logical directories that separate concerns and provide clear organization for different types of files and functionality.

## Directory Structure

```
ghq-bpit-ncop-iro/
│
├── .github/                  # GitHub Actions workflows and templates
│   └── workflows/            # CI/CD pipeline definitions
│       └── build_dockerimage.yml
│
├── archive/                  # Archived code and old implementations
│   ├── main_v2.py
│   ├── main.py
│   ├── src/
│   └── src_v2/
│
├── cache/                    # Application cache files
│   └── merge_result_cache.pkl
│
├── config/                   # Configuration files
│   ├── config.toml           # Application configuration (not tracked)
│   ├── config.toml.deploy    # Production configuration template
│   └── config.toml.sample    # Configuration sample/template
│
├── data/                     # Data files and datasets
│
├── docs/                     # Documentation
│   ├── architecture_diagram.md
│   ├── cicd_guide.md
│   ├── docker_strategy.md
│   ├── project_structure.md
│   ├── deployment_guide.md
│   ├── sequence_diagram.md
│   └── setup_cleanup_policies_guide.md
│
├── gcp/                      # Google Cloud Platform configurations
│   ├── artifact_repo_cleanup_policies.json
│   ├── batch/
│   └── sheets/
│
├── logs/                     # Application logs (organized by module)
│   ├── app/
│   ├── src.api.dependencies/
│   ├── src.api.routers.*/
│   ├── src.data_loader.*/
│   ├── src.services.*/
│   ├── src.thirdparty.gcp.*/
│   └── src.utils.*/
│
├── notebooks/                # Jupyter notebooks for development and analysis
│   ├── dev.ipynb
│   ├── iro_breakdown_funcs.ipynb
│   ├── maps.ipynb
│   └── score_module.ipynb
│
├── runs/                     # Execution results and output data
│   └── matching_results_*/   # Timestamped matching results
│
├── sql/                      # SQL scripts and queries
│
├── src/                      # Source code for the application
│   ├── api/                  # API endpoints and routing
│   │   ├── dependencies/     # Dependency injection functions
│   │   └── routers/          # API route definitions
│   │       ├── matching_router/
│   │       ├── pubsub_router/
│   │       └── v1_router/
│   │
│   ├── data_loader/          # Data loading modules
│   │   ├── distance_loader/
│   │   ├── loc_loader/
│   │   ├── rl_loader/
│   │   ├── rl_mapping_loader/
│   │   └── rpt_loader/
│   │
│   ├── services/             # Business logic services
│   │   ├── artifact_service/
│   │   ├── data_service/
│   │   ├── distance_service/
│   │   ├── lp_service/
│   │   ├── matching_service/
│   │   └── update_ratelane_mapping/
│   │
│   ├── thirdparty/           # Third-party integrations
│   │   └── gcp/              # Google Cloud Platform services
│   │       ├── bq_manager/   # BigQuery management
│   │       ├── gcs_manager/  # Google Cloud Storage
│   │       ├── gdrive_manager/ # Google Drive integration
│   │       ├── gmaps_manager/ # Google Maps API
│   │       └── gsheet_manager/ # Google Sheets API
│   │
│   └── utils/                # Utility functions and helpers
│       ├── matching_utils/
│       ├── matching_utils_v2/
│       └── preprocess/
│
├── tests/                    # Unit and integration tests
│
├── tmp/                      # Temporary files
│
├── .env                      # Environment variables (not tracked by git)
├── .env.sample               # Environment variables template
├── .gitignore                # Files to ignore in git
├── app.py                    # Main application entry point
├── create_table.py           # Database table creation script
├── Dockerfile                # Docker configuration for containerization
├── ingest_report_data.py     # Data ingestion script
├── poetry.lock               # Poetry dependency lock file
├── pubsub_dummy.py           # Pub/Sub testing utilities
├── pyproject.toml            # Poetry project definition and dependencies
├── pytest.ini               # PyTest configuration
├── requirements.txt          # Python dependencies (legacy)
├── root.py                   # Root module/utilities
│
└── README.md                 # Project documentation
```

## Directory Descriptions

### Root Level Directories

#### `.github/`

Contains GitHub-specific configurations and workflows:

-   **`workflows/`**: GitHub Actions CI/CD pipeline definitions
-   **`build_dockerimage.yml`**: Main CI/CD workflow for building and deploying Docker images

#### `archive/`

Stores archived code and previous implementations:

-   **`main_v2.py`**, **`main.py`**: Previous versions of main application files
-   **`src/`**, **`src_v2/`**: Archived source code directories from previous iterations

#### `cache/`

Application-generated cache files:

-   **`merge_result_cache.pkl`**: Cached merge results to improve performance

#### `config/`

Configuration management:

-   **`config.toml`**: Active configuration file (not tracked in git)
-   **`config.toml.deploy`**: Production deployment configuration template
-   **`config.toml.sample`**: Sample configuration for new setups

#### `data/`

Data files and datasets used by the application

#### `docs/`

Project documentation:

-   **`architecture_diagram.md`**: System architecture overview
-   **`cicd_guide.md`**: CI/CD pipeline documentation
-   **`docker_strategy.md`**: Docker image management strategy
-   **`project_structure.md`**: This file - project organization guide
-   **`deployment_guide.md`**: Deployment instructions and procedures
-   **`sequence_diagram.md`**: Process flow diagrams
-   **`setup_cleanup_policies_guide.md`**: GCP cleanup policies

#### `gcp/`

Google Cloud Platform specific configurations:

-   **`artifact_repo_cleanup_policies.json`**: Artifact Registry cleanup rules
-   **`batch/`**: Batch processing configurations
-   **`sheets/`**: Google Sheets integration files

#### `logs/`

Application logs organized by module:

-   **`app/`**: Main application logs
-   **`src.api.*/`**: API-related logs
-   **`src.data_loader.*/`**: Data loading operation logs
-   **`src.services.*/`**: Business logic service logs
-   **`src.thirdparty.gcp.*/`**: GCP integration logs
-   **`src.utils.*/`**: Utility function logs

#### `notebooks/`

Jupyter notebooks for development and analysis:

-   **`dev.ipynb`**: Development and testing notebook
-   **`iro_breakdown_funcs.ipynb`**: IRO breakdown analysis
-   **`maps.ipynb`**: Geographic and mapping analysis
-   **`score_module.ipynb`**: Scoring algorithm development

#### `runs/`

Execution results and output data:

-   **`matching_results_*/`**: Timestamped directories containing matching algorithm results

#### `sql/`

SQL scripts and database queries

#### `tests/`

Unit and integration tests for the application

#### `tmp/`

Temporary files generated during application execution

### Source Code Structure (`src/`)

#### `api/`

API layer components:

-   **`dependencies/`**: Dependency injection functions for FastAPI
-   **`routers/`**: API endpoint definitions organized by functionality
    -   **`matching_router/`**: Matching algorithm endpoints
    -   **`pubsub_router/`**: Pub/Sub message handling endpoints
    -   **`v1_router/`**: Version 1 API endpoints

#### `data_loader/`

Data loading and ingestion modules:

-   **`distance_loader/`**: Distance calculation data loading
-   **`loc_loader/`**: Location data loading
-   **`rl_loader/`**: Rate lane data loading
-   **`rl_mapping_loader/`**: Rate lane mapping data loading
-   **`rpt_loader/`**: Report data loading

#### `services/`

Business logic services:

-   **`artifact_service/`**: Artifact management and processing
-   **`data_service/`**: General data processing services
-   **`distance_service/`**: Distance calculation services
-   **`lp_service/`**: Linear programming services
-   **`matching_service/`**: Core matching algorithm services
-   **`update_ratelane_mapping/`**: Rate lane mapping update services

#### `thirdparty/`

Third-party integrations:

-   **`gcp/`**: Google Cloud Platform services
    -   **`bq_manager/`**: BigQuery management and operations
    -   **`gcs_manager/`**: Google Cloud Storage operations
    -   **`gdrive_manager/`**: Google Drive integration
    -   **`gmaps_manager/`**: Google Maps API integration
    -   **`gsheet_manager/`**: Google Sheets API operations

#### `utils/`

Utility functions and helpers:

-   **`matching_utils/`**: Utilities for matching algorithms
-   **`matching_utils_v2/`**: Version 2 of matching utilities
-   **`preprocess/`**: Data preprocessing utilities

### Configuration Files

#### `.env.sample`

Template for environment variables containing:

-   Application environment settings
-   GCP authentication credentials
-   API keys and secrets
-   Server configuration

#### `pyproject.toml`

Poetry project definition containing:

-   Project metadata
-   Python dependencies
-   Development dependencies
-   Build system configuration

#### `Dockerfile`

Docker container configuration for:

-   Application containerization
-   Multi-stage build optimization
-   Production deployment

#### `pytest.ini`

PyTest configuration for:

-   Test discovery settings
-   Coverage reporting
-   Test execution parameters

## File Naming Conventions

### Python Files

-   **Snake case**: `module_name.py`
-   **Descriptive names**: Files should clearly indicate their purpose
-   **Consistent structure**: Similar modules follow similar naming patterns

### Configuration Files

-   **Environment specific**: `.env.sample`, `config.toml.deploy`
-   **Clear purpose**: File names indicate their specific use case
-   **Version control**: Sample files are tracked, actual config files are not

### Documentation Files

-   **Lowercase with underscores**: `project_structure.md`
-   **Descriptive names**: Clear indication of content
-   **Markdown format**: All documentation uses `.md` extension

## Organization Principles

### Separation of Concerns

-   **API layer**: Separated from business logic
-   **Data access**: Isolated in dedicated modules
-   **Third-party integrations**: Contained in separate namespace
-   **Configuration**: Centralized in dedicated directory

### Modularity

-   **Self-contained modules**: Each directory contains related functionality
-   **Clear interfaces**: Well-defined boundaries between components
-   **Reusable components**: Utility functions in dedicated modules

### Scalability

-   **Organized structure**: Easy to navigate and extend
-   **Logical grouping**: Related functionality grouped together
-   **Clear hierarchy**: Nested structure follows logical relationships

### Maintainability

-   **Consistent naming**: Predictable file and directory names
-   **Clear documentation**: Each major component documented
-   **Version control**: Appropriate files tracked/ignored

## Best Practices

### Adding New Components

1. **Choose appropriate directory**: Follow existing organization patterns
2. **Use consistent naming**: Follow established naming conventions
3. **Update documentation**: Document new components and their purpose
4. **Consider dependencies**: Place modules in appropriate hierarchy

### File Organization

1. **Group related files**: Keep related functionality together
2. **Use subdirectories**: Organize complex modules with subdirectories
3. **Maintain consistency**: Follow established patterns for similar components
4. **Document structure**: Update this guide when adding major components

### Configuration Management

1. **Use sample files**: Provide templates for configuration
2. **Environment separation**: Keep environment-specific configs separate
3. **Security**: Never commit sensitive configuration to version control
4. **Documentation**: Document all configuration options

---

**Last Updated**: July 9, 2025
**Version**: 1.0
**Maintained By**: Development Team

## Related Documentation

-   [README](../README.md) - Main project documentation
-   [Deployment Guide](./deployment_guide.md) - Application deployment procedures
-   [CI/CD Pipeline Guide](./cicd_guide.md) - Continuous integration and deployment
-   [Docker Strategy Guide](./docker_strategy.md) - Container management strategy
