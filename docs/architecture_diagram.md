# Unified API Architecture Diagram

```mermaid
flowchart TB
    Client([Client Applications])
    API[Unified API Service]
    DB[(Database)]

    subgraph "API Service"
        MainRoutes[Main Process Routes]
        RecalcRoutes[Recalculation Routes]
        HealthRoutes[Health Check Routes]
    end

    Client -->|API Requests| API
    API --> MainRoutes
    API --> RecalcRoutes
    API --> HealthRoutes

    MainRoutes -->|Data Operations| DB
    RecalcRoutes -->|Data Operations| DB

    style API fill:#f9f,stroke:#333,stroke-width:2px
```

## API Endpoints

### Base Endpoints
- `GET /` - Welcome message and API info
- `GET /health` - Overall service health check

### Main Process Endpoints
- `POST /main/start` - Start a new process
- `GET /main/status/{process_id}` - Get status of a running process
- `GET /main/health` - Health check for main process functionality

### Recalculation Endpoints
- `POST /recalculation/process` - Trigger a recalculation for a process
- `GET /recalculation/health` - Health check for recalculation functionality
