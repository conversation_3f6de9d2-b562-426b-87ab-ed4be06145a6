# Docker Strategy Guide

This document outlines the comprehensive Docker strategy for the Street Turn Matching Service, including image building, tagging, registry management, and deployment strategies.

## Overview

The Docker strategy is designed to support multiple environments with clear separation, efficient caching, and automated lifecycle management. All Docker images are built and stored in Google Cloud Platform (GCP) Artifact Registry.

## Docker Registry Configuration

### Primary Registry

```
Registry: asia-southeast1-docker.pkg.dev
Project: one-global-dilab-matchback-dev
Repository: iro-repo
Image Name: street-turn-matching-service
```

### Full Image Path Template

```
asia-southeast1-docker.pkg.dev/one-global-dilab-matchback-dev/iro-repo/street-turn-matching-service:{tag}
```

## Image Tagging Strategy

### Tag Naming Convention

Our tagging strategy follows a hierarchical approach that clearly identifies the environment, branch, and build metadata:

#### Production Tags (main branch)

| Tag Format        | Example  | Usage                                          |
| ----------------- | -------- | ---------------------------------------------- |
| `latest`          | `latest` | Always points to the latest production release |
| `prod`            | `prod`   | Stable production tag                          |
| `{version}`       | `v1.2.3` | Semantic versioning for releases               |
| `{major}.{minor}` | `v1.2`   | Major.minor version tracking                   |

#### Staging Tags (staging branch)

| Tag Format      | Example           | Usage                               |
| --------------- | ----------------- | ----------------------------------- |
| `staging`       | `staging`         | Latest staging build                |
| `staging-{sha}` | `staging-a1b2c3d` | Specific staging build with git SHA |

#### Development Tags (develop branch)

| Tag Format        | Example       | Usage                    |
| ----------------- | ------------- | ------------------------ |
| `develop`         | `develop`     | Latest development build |
| `pr-{number}`     | `pr-123`      | Pull request builds      |
| `sha-{short-sha}` | `sha-a1b2c3d` | Specific commit builds   |

#### Feature Branch Tags (feature/_, feat/_, test/\*)

| Tag Format              | Example                       | Usage                   |
| ----------------------- | ----------------------------- | ----------------------- |
| `{branch-name}-{sha}`   | `feature-auth-fix-a1b2c3d`    | Feature branch with SHA |
| `feature-{branch-name}` | `feature-user-authentication` | Feature branch latest   |
| `feat-{branch-name}`    | `feat-new-api`                | Feat branch latest      |
| `test-{branch-name}`    | `test-performance`            | Test branch latest      |

### Tag Generation Logic

The tagging strategy is implemented using Docker Metadata Action in the CI/CD pipeline:

```yaml
- name: Generate Docker Tags
  id: docker_meta
  uses: docker/metadata-action@v4
  with:
      images: ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPO_NAME }}/${{ env.IMAGE_NAME }}
      tags: |
          # Production tags (main branch)
          type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}
          type=raw,value=prod,enable=${{ github.ref == 'refs/heads/main' }}
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          # Staging tags
          type=raw,value=staging,enable=${{ github.ref == 'refs/heads/staging' }}
          type=raw,value=staging-{{sha}},enable=${{ github.ref == 'refs/heads/staging' }}
          # Development tags
          type=raw,value=develop,enable=${{ github.ref == 'refs/heads/develop' }}
          type=ref,event=pr,prefix=pr-
          type=sha,format=short,prefix=sha-
          # Feature branch tags
          type=ref,event=branch,suffix=-{{sha}}
          type=ref,event=branch,prefix=feature-,enable=${{ startsWith(github.ref, 'refs/heads/feature/') }}
          type=ref,event=branch,prefix=feat-,enable=${{ startsWith(github.ref, 'refs/heads/feat/') }}
          type=ref,event=branch,prefix=test-,enable=${{ startsWith(github.ref, 'refs/heads/test/') }}
```

## Dockerfile Strategy

### Multi-Stage Build Approach

The Dockerfile should follow multi-stage build patterns for optimal image size and security:

```dockerfile
# Example structure (refer to actual Dockerfile in project root)
FROM python:3.12-slim as base
# Base dependencies and system setup

FROM base as dependencies
# Install build dependencies and Python packages

FROM base as runtime
# Copy only runtime artifacts and dependencies
# Final lean production image
```

### Build Arguments

The following build arguments are injected during the CI/CD process:

| Argument      | Description        | Example Value                          |
| ------------- | ------------------ | -------------------------------------- |
| `ENVIRONMENT` | Target environment | `production`, `staging`, `development` |
| `BUILD_DATE`  | ISO 8601 timestamp | `2025-07-09T12:34:56Z`                 |
| `GIT_SHA`     | Git commit SHA     | `a1b2c3d4e5f6`                         |

### Image Labels

All images include standardized OCI labels for metadata:

```dockerfile
LABEL org.opencontainers.image.created="${BUILD_DATE}"
LABEL org.opencontainers.image.revision="${GIT_SHA}"
LABEL org.opencontainers.image.version="${VERSION}"
LABEL org.opencontainers.image.environment="${ENVIRONMENT}"
LABEL org.opencontainers.image.source="https://github.com/your-org/street-turn-matching-service"
LABEL org.opencontainers.image.vendor="Your Organization"
LABEL org.opencontainers.image.title="Street Turn Matching Service"
LABEL org.opencontainers.image.description="Service for matching street turn data"
```

## Build Optimization

### Docker Buildx Configuration

We use Docker Buildx for advanced build features:

-   **Multi-platform builds** (if needed)
-   **Build caching** with GitHub Actions cache
-   **Concurrent builds** for faster execution

```yaml
- name: Set up Docker Buildx
  uses: docker/setup-buildx-action@v2
```

### Caching Strategy

#### Layer Caching

```yaml
cache-from: type=gha
cache-to: type=gha,mode=max
```

**Benefits:**

-   Reduces build time by reusing unchanged layers
-   Minimizes bandwidth usage
-   Improves developer experience

#### Cache Optimization Tips

1. **Order Dockerfile instructions** from least to most frequently changing
2. **Copy dependency files first** (requirements.txt, poetry.lock)
3. **Install dependencies before copying source code**
4. **Use .dockerignore** to exclude unnecessary files

### Build Context Optimization

Create a comprehensive `.dockerignore` file:

```dockerignore
# Version control
.git
.gitignore
.github

# Documentation
*.md
docs/

# Development files
.env
.env.local
*.log

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
.pytest_cache/
.coverage
.venv/
venv/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
notebooks/
tests/
tmp/
cache/
logs/
runs/
```

## Security Strategy

### Base Image Selection

-   **Use official Python slim images** for smaller attack surface
-   **Pin specific versions** to ensure reproducible builds
-   **Regularly update base images** for security patches

```dockerfile
FROM python:3.12-slim@sha256:specific-digest
```

### Non-root User

Run containers as non-root user:

```dockerfile
RUN groupadd -r appuser && useradd -r -g appuser appuser
USER appuser
```

### Secret Management

-   **Never embed secrets** in Docker images
-   **Use environment variables** for runtime configuration
-   **Mount secrets** at runtime using Kubernetes secrets or similar

### Image Scanning

Implement automated security scanning:

```yaml
# Example Trivy scan (commented in current workflow)
- name: Run Trivy vulnerability scanner
  uses: aquasecurity/trivy-action@master
  with:
      image-ref: "."
      format: "table"
      exit-code: "1"
      ignore-unfixed: true
      severity: "CRITICAL,HIGH"
```

## Registry Management

### Artifact Registry Setup

#### Repository Configuration

```bash
# Create repository (one-time setup)
gcloud artifacts repositories create iro-repo \
    --repository-format=docker \
    --location=asia-southeast1 \
    --description="Street Turn Matching Service Docker Images"
```

#### IAM Permissions

Required IAM roles for CI/CD service account:

-   `roles/artifactregistry.writer`
-   `roles/artifactregistry.reader`
-   `roles/storage.objectViewer` (for build cache)

### Cleanup Policies

Implement automatic cleanup to manage storage costs:

```json
{
    "rules": [
        {
            "name": "delete-old-feature-branches",
            "action": {
                "type": "Delete"
            },
            "condition": {
                "tagState": "TAGGED",
                "tagPrefixes": ["feature-", "feat-", "test-"],
                "olderThan": "7d"
            }
        },
        {
            "name": "keep-recent-production",
            "action": {
                "type": "Keep"
            },
            "condition": {
                "tagState": "TAGGED",
                "tagPrefixes": ["latest", "prod", "v"],
                "packageNamePrefixes": ["street-turn-matching-service"]
            },
            "mostRecentVersions": {
                "keepCount": 10
            }
        }
    ]
}
```

## Deployment Strategy

### Environment-Specific Configuration

#### Production Deployment

-   **Image**: Use `latest` or specific version tags
-   **Resources**: Full resource allocation
-   **Replicas**: Multiple replicas for high availability
-   **Health checks**: Comprehensive readiness and liveness probes

#### Staging Deployment

-   **Image**: Use `staging` tag
-   **Resources**: Reduced resource allocation
-   **Replicas**: Single replica for cost optimization
-   **Health checks**: Basic health checks

#### Development Deployment

-   **Image**: Use `develop` tag
-   **Resources**: Minimal resource allocation
-   **Replicas**: Single replica
-   **Health checks**: Basic health checks

#### Feature Testing Deployment

-   **Image**: Use feature branch tags
-   **Resources**: Minimal resource allocation
-   **Replicas**: Single replica
-   **Namespace**: Isolated namespace per feature
-   **Lifecycle**: Temporary, cleaned up after testing

### Container Configuration

#### Resource Limits

```yaml
resources:
    requests:
        memory: "256Mi"
        cpu: "250m"
    limits:
        memory: "512Mi"
        cpu: "500m"
```

#### Health Checks

```yaml
livenessProbe:
    httpGet:
        path: /health
        port: 8000
    initialDelaySeconds: 30
    periodSeconds: 10

readinessProbe:
    httpGet:
        path: /ready
        port: 8000
    initialDelaySeconds: 5
    periodSeconds: 5
```

## Monitoring and Observability

### Image Metrics

Track the following metrics:

-   **Build time** per environment
-   **Image size** trends over time
-   **Pull counts** by environment
-   **Storage usage** in Artifact Registry

### Container Metrics

Monitor runtime metrics:

-   **Resource usage** (CPU, memory)
-   **Response times** and latency
-   **Error rates** and availability
-   **Log aggregation** and analysis

### Alerting

Set up alerts for:

-   **Failed builds** in CI/CD pipeline
-   **High resource usage** in containers
-   **Security vulnerabilities** in images
-   **Storage quota** approaching limits

## Best Practices

### Development Workflow

1. **Local Development**

    ```bash
    # Build locally for testing
    docker build -t street-turn-matching-service:local .

    # Run locally
    docker run -p 8000:8000 street-turn-matching-service:local
    ```

2. **Feature Branch Testing**

    - Use manual workflow dispatch for feature branch builds
    - Test in isolated feature-testing environment
    - Clean up feature images after testing

3. **Version Management**
    - Use semantic versioning for releases
    - Tag production releases with version numbers
    - Maintain changelog for image updates

### Performance Optimization

1. **Image Size Optimization**

    - Use multi-stage builds
    - Remove unnecessary packages and files
    - Optimize layer ordering

2. **Build Speed Optimization**

    - Leverage build cache effectively
    - Parallelize build steps where possible
    - Use efficient base images

3. **Runtime Optimization**
    - Configure appropriate resource limits
    - Use application-specific optimizations
    - Monitor and tune performance

### Security Best Practices

1. **Image Security**

    - Scan images for vulnerabilities
    - Use minimal base images
    - Keep base images updated

2. **Runtime Security**

    - Run as non-root user
    - Use read-only root filesystem
    - Implement proper network policies

3. **Secret Management**
    - Never embed secrets in images
    - Use external secret management
    - Rotate secrets regularly

## Troubleshooting

### Common Build Issues

1. **Build Context Too Large**

    ```bash
    # Solution: Optimize .dockerignore
    echo "node_modules/" >> .dockerignore
    echo "*.log" >> .dockerignore
    ```

2. **Cache Miss Issues**

    ```bash
    # Solution: Verify layer ordering in Dockerfile
    # Install dependencies before copying source code
    ```

3. **Authentication Failures**
    ```bash
    # Solution: Verify GCP credentials
    gcloud auth configure-docker asia-southeast1-docker.pkg.dev
    ```

### Registry Issues

1. **Push Permission Denied**

    - Verify service account has `artifactregistry.writer` role
    - Check if repository exists in correct region

2. **Pull Permission Denied**

    - Verify service account has `artifactregistry.reader` role
    - Ensure image tag exists

3. **Storage Quota Exceeded**
    - Review and apply cleanup policies
    - Remove unused images manually if needed

### Runtime Issues

1. **Container Fails to Start**

    - Check application logs
    - Verify health check endpoints
    - Review resource limits

2. **Performance Issues**
    - Monitor resource usage
    - Check for memory leaks
    - Optimize application code

## Cost Optimization

### Storage Management

-   Implement automated cleanup policies
-   Regular auditing of stored images
-   Remove unused feature branch images

### Build Optimization

-   Efficient use of build cache
-   Parallel builds where possible
-   Optimize CI/CD resource usage

### Transfer Costs

-   Use regional registries close to deployment
-   Minimize unnecessary image pulls
-   Implement efficient caching strategies

---

**Last Updated**: July 9, 2025
**Version**: 1.0
**Maintained By**: DevOps Team

## Related Documentation

-   [CI/CD Pipeline Guide](./cicd_guide.md)
-   [Architecture Diagram](./architecture_diagram.md)
-   [Cleanup Policies Guide](./setup_cleanup_policies_guide.md)
