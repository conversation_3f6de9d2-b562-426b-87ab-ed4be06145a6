from fastapi import APIRouter, FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.api.dependencies import lifespan
from src.api.routers import matching_router, pubsub_router, v1_router
from src.const import get_settings
from src.utils.exceptions import (
    APIError,
    ValidationError,
    api_error_handler,
    general_exception_handler,
    pydantic_error_handler,
)
from src.utils.logger import log_handler

logger = log_handler.get_logger(name="app")


def _print_routes(app: FastAPI):
    for route in app.routes:
        logger.info(f"Registered route: {route.methods} - {route.path}")


def create_app() -> FastAPI:
    """Create a FastAPI application."""
    settings = get_settings()

    app = FastAPI(
        title="Street-turn Matching Service",
        description="API for matching street-turn requests with available containers.",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        log_level=settings.ENV.LOG_LEVEL,
        lifespan=lifespan,
    )
    router = APIRouter(prefix="/api/v1")
    # Include API routes
    router.include_router(v1_router.router)
    router.include_router(matching_router.router, prefix="/matching", tags=["matching"])
    router.include_router(pubsub_router.router, prefix="/pubsub", tags=["pubsub"])
    app.include_router(router)

    # Exception handlers
    app.add_exception_handler(APIError, api_error_handler)
    app.add_exception_handler(ValidationError, pydantic_error_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    # Middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Adjust this in production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    _print_routes(app)

    return app


app = create_app()


def start():
    """Start the FastAPI application."""
    import uvicorn

    settings = get_settings()

    # Get host and port from environment variables with defaults
    host = settings.ENV.HOST
    port = settings.ENV.PORT
    num_workers = settings.ENV.FASTAPI_WORKER

    print(f"Starting server at {host}:{port}")
    uvicorn.run("app:app", host=host, port=port, reload=True, workers=num_workers)


# Entry point for the application
if __name__ == "__main__":
    start()
