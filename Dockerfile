FROM python:3.12 as base

ARG DEV=false
ENV VIRTUAL_ENV=/app/.venv \
PATH="/app/.venv/bin:$PATH"

ENV PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100



FROM base as builder

ENV POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_IN_PROJECT=1 \
    POETRY_VIRTUALENVS_CREATE=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

WORKDIR /app

RUN pip install poetry==1.6.1

COPY pyproject.toml ./
RUN if [ $DEV ]; then \
      poetry install --with dev --no-root --no-interaction --no-ansi && rm -rf $POETRY_CACHE_DIR; \
    else \
      poetry install --without dev --no-root --no-interaction --no-ansi && rm -rf $POETRY_CACHE_DIR; \
    fi


FROM base as runtime

COPY --from=builder ${VIRTUAL_ENV} ${VIRTUAL_ENV}

COPY . /app
WORKDIR /app
COPY config/config.toml.deploy config/config.toml

# Set environment variables for host and port
ENV HOST=0.0.0.0
ENV PORT=8000

# Expose the port the app will run on
EXPOSE ${PORT}

# Command to run the application
CMD ["sh", "-c", "uvicorn app:app --host ${HOST} --port ${PORT}"]
