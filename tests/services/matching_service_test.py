from unittest.mock import AsyncMock, Mock, patch

import pandas as pd
import pytest

from src.services.matching_service import StreetTurnMatchingService


@pytest.fixture
def mock_bq_manager():
    """Mock BigQuery manager for testing."""
    return Mock()


@pytest.fixture
def matching_service(mock_bq_manager):
    """Fixture to create an instance of StreetTurnMatchingService with a mocked BigQuery manager."""
    return StreetTurnMatchingService(mock_bq_manager)


@pytest.mark.asyncio
async def test_create_candidate_pairs():
    """Test the __create_candidate_pairs function with valid input data."""
    # Mock input data
    test_data = pd.DataFrame(
        {
            "imp_port_cd": ["PORT1", "PORT1", "PORT2"],
            "xpt_port_cd": ["PORT1", "PORT2", "PORT3"],
            "imp_port_city": ["City1", "City1", "City2"],
            "imp_loc_cd": ["LOC1", "LOC2", "LOC3"],
            "imp_loc_city": ["LocCity1", "LocCity2", "LocCity3"],
            "xpt_loc_cd": ["XLOC1", "XLOC2", "XLOC3"],
            "xpt_loc_city": ["XLocCity1", "XLocCity2", "XLocCity3"],
        }
    )

    # Mock the distance calculation result
    distance_result = pd.Series(
        {
            "imp_to_port_dist_km": 10.0,
            "xpt_to_port_dist_km": 10.0,
            "imp_to_xpt_dist_km": 15.0,
            "round_trip_route_dist_km": 40.0,
            "street_turn_route_dist_km": 35.0,
            "dist_sav_km": 5.0,
        }
    )

    # Mock services and distance calculation
    mock_distance_cache = {}
    mock_upload_cache = AsyncMock()

    class MockMatchingService(StreetTurnMatchingService):
        async def _streetturnmatchingservice__calculate_distances_async(self, row):
            return distance_result

    with (
        patch("src.api.dependencies.services.distance_cache", mock_distance_cache),
        patch("src.services.matching_service.upload_distance_cache", mock_upload_cache),
    ):
        # Initialize service
        service = MockMatchingService(Mock())

        # Execute the function
        result = await service._StreetTurnMatchingService__create_candidate_pairs(test_data)

        # Verify results
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1  # Only one pair has matching ports (PORT1)
        assert "port_cd" in result.columns  # Verify column renaming
        assert "xpt_port_cd" not in result.columns  # Verify column dropping

        # Verify distance calculations
        assert "imp_to_port_dist_km" in result.columns
        assert "xpt_to_port_dist_km" in result.columns
        assert "imp_to_xpt_dist_km" in result.columns
        assert "round_trip_route_dist_km" in result.columns
        assert "street_turn_route_dist_km" in result.columns
        assert "dist_sav_km" in result.columns

        # Verify distance cache was uploaded
        mock_upload_cache.assert_called_once()


@pytest.mark.asyncio
async def test_create_candidate_pairs_possible_candidate_length():
    """Test the __create_candidate_pairs function with valid input data."""
    # Mock input data
    test_data = pd.DataFrame(
        {
            "imp_port_cd": ["PORT1", "PORT1", "PORT2", "PORT3", "PORT4"],
            "xpt_port_cd": ["PORT1", "PORT2", "PORT3", "PORT3", "PORT4"],
            "imp_port_city": ["City1", "City1", "City2", "City2", "City3"],
            "imp_loc_cd": ["LOC1", "LOC2", "LOC3", "LOC4", "LOC5"],
            "imp_loc_city": ["LocCity1", "LocCity2", "LocCity3", "LocCity4", "LocCity5"],
            "xpt_loc_cd": ["XLOC1", "XLOC2", "XLOC3", "XLOC3", "XLOC4"],
            "xpt_loc_city": ["XLocCity1", "XLocCity2", "XLocCity3", "XLocCity3", "XLocCity4"],
        }
    )

    # Mock the distance calculation result
    distance_result = pd.Series(
        {
            "imp_to_port_dist_km": 10.0,
            "xpt_to_port_dist_km": 10.0,
            "imp_to_xpt_dist_km": 15.0,
            "round_trip_route_dist_km": 40.0,
            "street_turn_route_dist_km": 35.0,
            "dist_sav_km": 5.0,
        }
    )

    # Mock services and distance calculation
    mock_distance_cache = {}
    mock_upload_cache = AsyncMock()

    class MockMatchingService(StreetTurnMatchingService):
        async def _streetturnmatchingservice__calculate_distances_async(self, row):
            return distance_result

    with (
        patch("src.api.dependencies.services.distance_cache", mock_distance_cache),
        patch("src.services.matching_service.upload_distance_cache", mock_upload_cache),
    ):
        # Initialize service
        service = MockMatchingService(Mock())

        # Execute the function
        result = await service._StreetTurnMatchingService__create_candidate_pairs(test_data)

        # Verify results
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 3  # Only three pair has matching ports (PORT1)

        # Verify distance cache was uploaded
        mock_upload_cache.assert_called_once()


@pytest.mark.asyncio
async def test_create_candidate_pairs_empty_input():
    """Test the __create_candidate_pairs function with empty input data."""
    # Create empty input DataFrame
    test_data = pd.DataFrame(
        {
            "imp_port_cd": [],
            "xpt_port_cd": [],
            "imp_port_city": [],
            "imp_loc_cd": [],
            "imp_loc_city": [],
            "xpt_loc_cd": [],
            "xpt_loc_city": [],
        }
    )

    # Mock dependencies
    mock_distance_cache = {}
    mock_upload_cache = AsyncMock()

    # Initialize service and test
    with (
        patch("src.api.dependencies.services.distance_cache", mock_distance_cache),
        patch("src.services.matching_service.upload_distance_cache", mock_upload_cache),
    ):
        service = StreetTurnMatchingService(Mock())
        result = await service._StreetTurnMatchingService__create_candidate_pairs(test_data)

        # Verify results
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 0  # Should return empty DataFrame


@pytest.mark.asyncio
async def test_create_candidate_pairs_no_matches():
    """Test the __create_candidate_pairs function when no ports match."""
    # Create test data with no matching ports
    test_data = pd.DataFrame(
        {
            "imp_port_cd": ["PORT1", "PORT2"],
            "xpt_port_cd": ["PORT2", "PORT1"],
            "imp_port_city": ["City1", "City2"],
            "imp_loc_cd": ["LOC1", "LOC2"],
            "imp_loc_city": ["LocCity1", "LocCity2"],
            "xpt_loc_cd": ["XLOC1", "XLOC2"],
            "xpt_loc_city": ["XLocCity1", "XLocCity2"],
        }
    )

    # Mock dependencies
    mock_distance_cache = {}
    mock_upload_cache = AsyncMock()

    # Mock distance calculation but it shouldn't be called
    class MockMatchingService(StreetTurnMatchingService):
        async def _streetturnmatchingservice__calculate_distances_async(self, row):
            raise Exception("This should not be called")

    # Initialize service and test
    with (
        patch("src.api.dependencies.services.distance_cache", mock_distance_cache),
        patch("src.services.matching_service.upload_distance_cache", mock_upload_cache),
    ):
        service = MockMatchingService(Mock())
        result = await service._StreetTurnMatchingService__create_candidate_pairs(test_data)

        # Verify results
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 0  # Should return empty DataFrame as no ports match
