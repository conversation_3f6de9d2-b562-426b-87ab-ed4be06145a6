"""Unit tests for StreetTurnMatchingService.__merge_result."""

import pandas as pd
import pytest

from src.services.matching_service import StreetTurnMatchingService

# Mark all tests in this module as async
pytestmark = pytest.mark.asyncio


@pytest.fixture
def matching_service():
    """Create a StreetTurnMatchingService instance for testing."""
    return StreetTurnMatchingService(None)  # Pass None as we won't use bq_manager in these tests


@pytest.fixture
def sample_street_turn_table():
    """Create a sample street turn multi-index DataFrame."""
    data = [
        {
            "import_base_rate_return": 100,
            "export_base_rate_one_way": 150,
            "export_base_rate_return": 200,
            "vendor_cd": "vendor1",
            "loc_cd": "loc1",
        },
        {
            "import_base_rate_return": 120,
            "export_base_rate_one_way": 170,
            "export_base_rate_return": 220,
            "vendor_cd": "vendor1",
            "loc_cd": "loc2",
        },
        {
            "import_base_rate_return": 110,
            "export_base_rate_one_way": 160,
            "export_base_rate_return": 210,
            "vendor_cd": "vendor2",
            "loc_cd": "loc1",
        },
    ]
    df = pd.DataFrame(data)
    df.set_index(["vendor_cd", "loc_cd"], inplace=True)
    return df


@pytest.fixture
def sample_round_trip_table():
    """Create a sample round trip DataFrame with location index."""
    data = {
        "loc1": {"min_import_base_rate": 100, "min_export_base_rate": 200, "total_cost": 300},
        "loc2": {"min_import_base_rate": 120, "min_export_base_rate": 220, "total_cost": 340},
    }
    return pd.DataFrame.from_dict(data, orient="index")


@pytest.fixture
def sample_ratelane_mapping():
    """Create a sample ratelane mapping DataFrame."""
    data = {
        "VNDR_CD": ["vendor1", "vendor1", "vendor2"],
        "LOC_CD": ["loc1", "loc2", "loc1"],
        "PORT_CD": ["port1", "port1", "port1"],
        "VIA_PNT1": ["point1", "point2", "point1"],
        "IMP_BSE_RT_RTN": [100, 120, 110],
        "XPT_BSE_RT_RTN": [200, 220, 210],
        "IMP_BSE_RT_ONE_WY": [150, 170, 160],
        "XPT_BSE_RT_ONE_WY": [150, 170, 160],
        "HAZMAT": ["N", "N", "N"],
        "LANE_DESC": ["desc1", "desc2", "desc3"],
        "CRR_CD": ["carr1", "carr2", "carr3"],
        "IMP_PORT_CD": ["port1", "port1", "port1"],
        "IMP_HAZMAT": ["N", "N", "N"],
        "IMP_CRR_CD": ["carr1", "carr2", "carr3"],
        "XPT_CRR_CD": ["carr1", "carr2", "carr3"],
        "VIA_PNT1_IMP": ["point1", "point2", "point1"],
        "VIA_PNT1_XPT": ["point1", "point2", "point1"],
        "IMP_LANE_DESC": ["desc1", "desc2", "desc3"],
        "XPT_LANE_DESC": ["desc1", "desc2", "desc3"],
    }
    return pd.DataFrame(data)


async def test_merge_result_v2_successful_case(
    matching_service, sample_street_turn_table, sample_round_trip_table, sample_ratelane_mapping
):
    """Test successful case of merge_result_v2 with valid inputs."""
    # Arrange
    node_cd = "port1"
    imp_loc = "loc1"
    xpt_loc = "loc2"
    is_optimal = True

    # Act
    result = await matching_service._StreetTurnMatchingService__merge_result(
        node_cd,
        imp_loc,
        xpt_loc,
        sample_street_turn_table,
        sample_round_trip_table,
        sample_ratelane_mapping,
        is_optimal,
    )

    # Assert
    assert not result.empty
    assert "street_turn_total_cost" in result.columns
    assert "round_trip_total_cost" in result.columns
    assert "cost_save" in result.columns
    assert "optimal" in result.columns

    # Verify calculations
    expected_street_turn_cost = 100 + 170  # imp_base_rate_return + export_base_rate_one_way
    assert any(result["street_turn_total_cost"] == expected_street_turn_cost)


async def test_merge_result_v2_location_not_found(
    matching_service, sample_street_turn_table, sample_round_trip_table, sample_ratelane_mapping
):
    """Test case where location is not found in tables."""
    # Arrange
    node_cd = "port1"
    imp_loc = "nonexistent_loc"  # Location that doesn't exist
    xpt_loc = "loc2"
    is_optimal = True

    # Act
    result = await matching_service._StreetTurnMatchingService__merge_result(
        node_cd,
        imp_loc,
        xpt_loc,
        sample_street_turn_table,
        sample_round_trip_table,
        sample_ratelane_mapping,
        is_optimal,
    )

    # Assert
    assert result.empty


async def test_merge_result_v2_non_optimal_case(
    matching_service, sample_street_turn_table, sample_round_trip_table, sample_ratelane_mapping
):
    """Test case where is_optimal is False."""
    # Arrange
    node_cd = "port1"
    imp_loc = "loc1"
    xpt_loc = "loc2"
    is_optimal = False

    # Act
    result = await matching_service._StreetTurnMatchingService__merge_result(
        node_cd,
        imp_loc,
        xpt_loc,
        sample_street_turn_table,
        sample_round_trip_table,
        sample_ratelane_mapping,
        is_optimal,
    )

    # Assert
    assert not result.empty
    assert (~result["optimal"]).all()


async def test_merge_result_v2_with_via_priority(
    matching_service, sample_street_turn_table, sample_round_trip_table, sample_ratelane_mapping
):
    """Test case with via_priority set to True."""
    # Arrange
    node_cd = "port1"
    imp_loc = "loc1"
    xpt_loc = "loc2"
    is_optimal = True
    via_priority = True

    # Act
    result = await matching_service._StreetTurnMatchingService__merge_result(
        node_cd,
        imp_loc,
        xpt_loc,
        sample_street_turn_table,
        sample_round_trip_table,
        sample_ratelane_mapping,
        is_optimal,
        via_priority,
    )

    # Assert
    assert not result.empty
    assert "import_via_pnt1" in result.columns
    assert "export_via_pnt1" in result.columns
