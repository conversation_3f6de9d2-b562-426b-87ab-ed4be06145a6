# linear_programming__logic_test.py
# ruff: noqa: S101
"""Comprehensive test suite for LinearProgrammingOptimizer matching logic.

Assert statements are expected in pytest

ASSUMPTIONS:
- Container type compatibility filtering is already done upstream
- All import-export pairs in the input data have matching container types
- Service focuses on distance, time, and optimal/sub-optimal selection logic
"""

import time

import numpy as np
import pandas as pd
import pytest

from src.services.lp_service import LinearProgrammingOptimizer


class TestLinearProgrammingOptimizer:
    """Core functionality tests - basic matching scenarios."""

    @staticmethod
    def create_test_data_single_match():
        """Create test data for single match scenario."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"],
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"],
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"],
            "xpt_cop_no": ["MOCK_XPT_COP_00"],
            "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00"],
            "xpt_cntr_no": [""],
            "imp_cntr_tpsz_cd": ["D5"],
            "xpt_cntr_tpsz_cd": ["D5"],  # Always matches import type (pre-filtered)
            "time_gap_in_hour": [-90],
            "imp_to_xpt_dist_km": [30.306],
            "dist_sav_km": [120.0],
        }
        return pd.DataFrame(data)

    @staticmethod
    def create_test_data_multiple_matches():
        """Create test data for multiple matches scenario."""
        num_exports = 5
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * num_exports,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * num_exports,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"] * num_exports,
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:02d}" for i in range(num_exports)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:02d}" for i in range(num_exports)],
            "xpt_cntr_no": [""] * num_exports,
            "imp_cntr_tpsz_cd": ["D5"] * num_exports,
            "xpt_cntr_tpsz_cd": ["D5"] * num_exports,  # All pre-filtered to match
            "time_gap_in_hour": [-90] * num_exports,
            "imp_to_xpt_dist_km": [30.306] * num_exports,
            "dist_sav_km": [120.0, 100.0, 80.0, 60.0, 40.0],
        }
        return pd.DataFrame(data)

    @staticmethod
    def create_test_data_multiple_matches_fewer_exports():
        """Create test data for multiple matches with fewer exports scenario."""
        num_exports = 3
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * num_exports,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * num_exports,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"] * num_exports,
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:02d}" for i in range(num_exports)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:02d}" for i in range(num_exports)],
            "xpt_cntr_no": [""] * num_exports,
            "imp_cntr_tpsz_cd": ["D5"] * num_exports,
            "xpt_cntr_tpsz_cd": ["D5"] * num_exports,  # All pre-filtered to match
            "time_gap_in_hour": [-90] * num_exports,
            "imp_to_xpt_dist_km": [30.306] * num_exports,
            "dist_sav_km": [120.0, 100.0, 80.0],
        }
        return pd.DataFrame(data)

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup the matching service and parameters."""
        self.service = LinearProgrammingOptimizer()
        self.max_distance_thres_km = 643.74
        self.time_tolerance_thres_days = 0

    def test_find_matches_single_match(self):
        """Test finding a single match for an import."""
        test_df = self.create_test_data_single_match()
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)
        single_imp_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]

        assert len(single_imp_matches) == 1, "Expected exactly 1 match"
        assert single_imp_matches.iloc[0]["is_optimal"], "Single match should be marked as optimal"

    def test_find_matches_optimal_and_suboptimal(self):
        """Test finding optimal and sub-optimal matches for an import."""
        test_df = self.create_test_data_multiple_matches()
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)
        imp_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        optimal_matches = imp_matches[imp_matches["is_optimal"]]
        suboptimal_matches = imp_matches[~imp_matches["is_optimal"]]

        assert len(imp_matches) == 5, "Expected 5 total matches"
        assert len(optimal_matches) == 1, "Expected 1 optimal match"
        assert len(suboptimal_matches) == 4, "Expected 4 sub-optimal matches"
        if not optimal_matches.empty and not suboptimal_matches.empty:
            optimal_savings = optimal_matches.iloc[0]["dist_sav_km"]
            assert all(optimal_savings >= s for s in suboptimal_matches["dist_sav_km"]), (
                "Optimal match should have highest distance savings"
            )

    def test_find_matches_optimal_and_suboptimal_fewer_exports(self):
        """Test finding optimal and sub-optimal matches with fewer exports (3)."""
        test_df = self.create_test_data_multiple_matches_fewer_exports()
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)
        imp_matches_fewer = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        optimal_matches_fewer = imp_matches_fewer[imp_matches_fewer["is_optimal"]]
        suboptimal_matches_fewer = imp_matches_fewer[~imp_matches_fewer["is_optimal"]]

        assert len(imp_matches_fewer) == 3, "Expected 3 total matches"
        assert len(optimal_matches_fewer) == 1, "Expected 1 optimal match for fewer exports"
        assert len(suboptimal_matches_fewer) == 2, "Expected 2 sub-optimal matches for fewer exports"
        if not optimal_matches_fewer.empty and not suboptimal_matches_fewer.empty:
            optimal_savings = optimal_matches_fewer.iloc[0]["dist_sav_km"]
            assert all(optimal_savings >= s for s in suboptimal_matches_fewer["dist_sav_km"]), (
                "Optimal match should have highest distance savings in fewer exports scenario"
            )


class TestOptimalSuboptimalLogic:
    """Test cases for 1 optimal + max 4 sub-optimal business logic."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup the matching service and parameters."""
        self.service = LinearProgrammingOptimizer()
        self.max_distance_thres_km = 643.74
        self.time_tolerance_thres_days = 0

    def test_single_import_exactly_5_exports(self):
        """Test 1 import with exactly 5 exports (1 optimal + 4 sub-optimal)."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * 5,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * 5,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"] * 5,
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:02d}" for i in range(5)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:02d}" for i in range(5)],
            "xpt_cntr_no": [""] * 5,
            "imp_cntr_tpsz_cd": ["D5"] * 5,
            "xpt_cntr_tpsz_cd": ["D5"] * 5,
            "time_gap_in_hour": [-90] * 5,
            "imp_to_xpt_dist_km": [30.306] * 5,
            "dist_sav_km": [150.0, 120.0, 100.0, 80.0, 60.0],  # Descending order
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)

        imp_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        optimal_matches = imp_matches[imp_matches["is_optimal"]]
        suboptimal_matches = imp_matches[~imp_matches["is_optimal"]]

        assert len(imp_matches) == 5, "Should return all 5 matches"
        assert len(optimal_matches) == 1, "Should have exactly 1 optimal match"
        assert len(suboptimal_matches) == 4, "Should have exactly 4 sub-optimal matches"
        assert optimal_matches.iloc[0]["dist_sav_km"] == 150.0, "Highest savings should be optimal"

    def test_single_import_more_than_5_exports(self):
        """Test 1 import with 8+ exports (should limit to 1 optimal + 4 sub-optimal)."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * 8,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * 8,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"] * 8,
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:02d}" for i in range(8)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:02d}" for i in range(8)],
            "xpt_cntr_no": [""] * 8,
            "imp_cntr_tpsz_cd": ["D5"] * 8,
            "xpt_cntr_tpsz_cd": ["D5"] * 8,
            "time_gap_in_hour": [-90] * 8,
            "imp_to_xpt_dist_km": [30.306] * 8,
            "dist_sav_km": [180.0, 160.0, 140.0, 120.0, 100.0, 80.0, 60.0, 40.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)

        imp_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        optimal_matches = imp_matches[imp_matches["is_optimal"]]
        suboptimal_matches = imp_matches[~imp_matches["is_optimal"]]

        assert len(imp_matches) == 5, "Should limit to 5 matches total (1+4)"
        assert len(optimal_matches) == 1, "Should have exactly 1 optimal match"
        assert len(suboptimal_matches) == 4, "Should have exactly 4 sub-optimal matches"
        assert optimal_matches.iloc[0]["dist_sav_km"] == 180.0, "Highest savings should be optimal"

        # Verify the top 5 savings are selected
        returned_savings = sorted(imp_matches["dist_sav_km"].tolist(), reverse=True)
        expected_top5 = [180.0, 160.0, 140.0, 120.0, 100.0]
        assert returned_savings == expected_top5, "Should return top 5 by savings"

    def test_single_import_less_than_5_exports(self):
        """Test 1 import with only 3 exports (1 optimal + 2 sub-optimal)."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * 3,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * 3,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"] * 3,
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:02d}" for i in range(3)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:02d}" for i in range(3)],
            "xpt_cntr_no": [""] * 3,
            "imp_cntr_tpsz_cd": ["D5"] * 3,
            "xpt_cntr_tpsz_cd": ["D5"] * 3,
            "time_gap_in_hour": [-90] * 3,
            "imp_to_xpt_dist_km": [30.306] * 3,
            "dist_sav_km": [120.0, 100.0, 80.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)

        imp_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        optimal_matches = imp_matches[imp_matches["is_optimal"]]
        suboptimal_matches = imp_matches[~imp_matches["is_optimal"]]

        assert len(imp_matches) == 3, "Should return all 3 available matches"
        assert len(optimal_matches) == 1, "Should have exactly 1 optimal match"
        assert len(suboptimal_matches) == 2, "Should have 2 sub-optimal matches (less than max 4)"

    def test_two_imports_no_competition(self):
        """Test 2 imports with separate export pools (no competition)."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * 3 + ["MOCK_IMP_COP_NO_01"] * 3,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * 3 + ["MOCK_IMP_BKG_NO_01"] * 3,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO_00"] * 3 + ["MOCK_IMP_CNTR_NO_01"] * 3,
            "xpt_cop_no": [f"MOCK_XPT_COP_A_{i:02d}" for i in range(3)] + [f"MOCK_XPT_COP_B_{i:02d}" for i in range(3)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_A_{i:02d}" for i in range(3)]
            + [f"MOCK_EXP_BKG_NO_B_{i:02d}" for i in range(3)],
            "xpt_cntr_no": [""] * 6,
            "imp_cntr_tpsz_cd": ["D5"] * 6,
            "xpt_cntr_tpsz_cd": ["D5"] * 6,
            "time_gap_in_hour": [-90] * 6,
            "imp_to_xpt_dist_km": [30.306] * 6,
            "dist_sav_km": [150.0, 120.0, 100.0, 140.0, 110.0, 90.0],  # Imp1: 150,120,100 | Imp2: 140,110,90
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)

        # Check Import 1
        imp1_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        imp1_optimal = imp1_matches[imp1_matches["is_optimal"]]
        imp1_suboptimal = imp1_matches[~imp1_matches["is_optimal"]]

        assert len(imp1_matches) == 3, "Import 1 should have 3 matches"
        assert len(imp1_optimal) == 1, "Import 1 should have 1 optimal"
        assert len(imp1_suboptimal) == 2, "Import 1 should have 2 sub-optimal"
        assert imp1_optimal.iloc[0]["dist_sav_km"] == 150.0, "Import 1 optimal should be highest"

        # Check Import 2
        imp2_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_01"]
        imp2_optimal = imp2_matches[imp2_matches["is_optimal"]]
        imp2_suboptimal = imp2_matches[~imp2_matches["is_optimal"]]

        assert len(imp2_matches) == 3, "Import 2 should have 3 matches"
        assert len(imp2_optimal) == 1, "Import 2 should have 1 optimal"
        assert len(imp2_suboptimal) == 2, "Import 2 should have 2 sub-optimal"
        assert imp2_optimal.iloc[0]["dist_sav_km"] == 140.0, "Import 2 optimal should be highest"

    def test_two_imports_competing_for_same_exports(self):
        """Test 2 imports competing for the same export pool."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * 4 + ["MOCK_IMP_COP_NO_01"] * 4,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * 4 + ["MOCK_IMP_BKG_NO_01"] * 4,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO_00"] * 4 + ["MOCK_IMP_CNTR_NO_01"] * 4,
            # Same exports for both imports (competition)
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:02d}" for i in range(4)] * 2,
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:02d}" for i in range(4)] * 2,
            "xpt_cntr_no": [""] * 8,
            "imp_cntr_tpsz_cd": ["D5"] * 8,
            "xpt_cntr_tpsz_cd": ["D5"] * 8,
            "time_gap_in_hour": [-90] * 8,
            "imp_to_xpt_dist_km": [30.306] * 8,
            # Imp1 has better savings for first 3 exports, Imp2 better for last export
            "dist_sav_km": [150.0, 120.0, 100.0, 80.0, 140.0, 110.0, 90.0, 160.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)

        # Both imports should still get matches
        imp1_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        imp2_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_01"]

        assert len(imp1_matches) > 0, "Import 1 should get some matches"
        assert len(imp2_matches) > 0, "Import 2 should get some matches"

        # Check that each import has max 1 optimal
        imp1_optimal = imp1_matches[imp1_matches["is_optimal"]]
        imp2_optimal = imp2_matches[imp2_matches["is_optimal"]]

        assert len(imp1_optimal) <= 1, "Import 1 should have at most 1 optimal"
        assert len(imp2_optimal) <= 1, "Import 2 should have at most 1 optimal"

    def test_equal_savings_tie_breaking(self):
        """Test tie-breaking when multiple exports have same distance savings."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * 5,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * 5,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"] * 5,
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:02d}" for i in range(5)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:02d}" for i in range(5)],
            "xpt_cntr_no": [""] * 5,
            "imp_cntr_tpsz_cd": ["D5"] * 5,
            "xpt_cntr_tpsz_cd": ["D5"] * 5,
            "time_gap_in_hour": [-90] * 5,
            "imp_to_xpt_dist_km": [10.0, 20.0, 30.0, 40.0, 50.0],  # Different distances for tie-breaking
            "dist_sav_km": [120.0, 120.0, 120.0, 100.0, 80.0],  # First 3 tied
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)

        imp_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        optimal_matches = imp_matches[imp_matches["is_optimal"]]

        assert len(optimal_matches) == 1, "Should break ties and select exactly 1 optimal"
        assert optimal_matches.iloc[0]["dist_sav_km"] == 120.0, "Optimal should be from tied group"

    def test_negative_and_positive_savings_mixed(self):
        """Test mix of positive and negative distance savings."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * 6,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * 6,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"] * 6,
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:02d}" for i in range(6)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:02d}" for i in range(6)],
            "xpt_cntr_no": [""] * 6,
            "imp_cntr_tpsz_cd": ["D5"] * 6,
            "xpt_cntr_tpsz_cd": ["D5"] * 6,
            "time_gap_in_hour": [-90] * 6,
            "imp_to_xpt_dist_km": [30.306] * 6,
            "dist_sav_km": [150.0, 100.0, 50.0, -20.0, -50.0, -100.0],  # Mix of positive/negative
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)

        imp_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        optimal_matches = imp_matches[imp_matches["is_optimal"]]

        # Should still return top 5 (1 optimal + 4 sub-optimal) even if some are negative
        assert len(imp_matches) == 5, "Should return top 5 regardless of negative values"
        assert len(optimal_matches) == 1, "Should have 1 optimal match"
        assert optimal_matches.iloc[0]["dist_sav_km"] == 150.0, "Highest value should be optimal"

    def test_optimal_suboptimal_never_overlap(self):
        """Ensure optimal and sub-optimal matches never overlap."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"] * 7,
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"] * 7,
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"] * 7,
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:02d}" for i in range(7)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:02d}" for i in range(7)],
            "xpt_cntr_no": [""] * 7,
            "imp_cntr_tpsz_cd": ["D5"] * 7,
            "xpt_cntr_tpsz_cd": ["D5"] * 7,
            "time_gap_in_hour": [-90] * 7,
            "imp_to_xpt_dist_km": [30.306] * 7,
            "dist_sav_km": [200.0, 180.0, 160.0, 140.0, 120.0, 100.0, 80.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)

        imp_matches = matches_df[matches_df["imp_cop_no"] == "MOCK_IMP_COP_NO_00"]
        optimal_matches = imp_matches[imp_matches["is_optimal"]]
        suboptimal_matches = imp_matches[~imp_matches["is_optimal"]]

        # Verify no overlap in export IDs
        optimal_exports = set(optimal_matches["xpt_cop_no"].tolist())
        suboptimal_exports = set(suboptimal_matches["xpt_cop_no"].tolist())

        assert len(optimal_exports.intersection(suboptimal_exports)) == 0, (
            "No export should be both optimal and sub-optimal"
        )
        assert len(optimal_matches) == 1, "Should have exactly 1 optimal"
        assert len(suboptimal_matches) == 4, "Should have exactly 4 sub-optimal (limited from 6 available)"

    def test_multiple_imports_optimal_count_constraint(self):
        """Test that each import gets exactly 1 optimal across complex scenarios."""
        # 3 imports, 10 exports, overlapping potential matches
        num_imports = 3
        num_exports = 10

        imp_data = []
        exp_data = []
        savings_data = []

        for i in range(num_imports):
            for j in range(num_exports):
                imp_data.append(f"MOCK_IMP_COP_NO_{i:02d}")
                exp_data.append(f"MOCK_XPT_COP_{j:02d}")
                # Create varied savings with some overlaps
                savings_data.append(200 - (i * 10) - j)

        data = {
            "imp_cop_no": imp_data,
            "imp_bkg_no": [f"MOCK_IMP_BKG_NO_{i // num_exports:02d}" for i in range(len(imp_data))],
            "imp_cntr_no": [f"MOCK_IMP_CNTR_NO_{i // num_exports:02d}" for i in range(len(imp_data))],
            "xpt_cop_no": exp_data,
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i % num_exports:02d}" for i in range(len(exp_data))],
            "xpt_cntr_no": [""] * len(imp_data),
            "imp_cntr_tpsz_cd": ["D5"] * len(imp_data),
            "xpt_cntr_tpsz_cd": ["D5"] * len(imp_data),
            "time_gap_in_hour": [-90] * len(imp_data),
            "imp_to_xpt_dist_km": [30.306] * len(imp_data),
            "dist_sav_km": savings_data,
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        matches_df = pd.DataFrame(matches)

        # Each import should have exactly 1 optimal
        for i in range(num_imports):
            imp_matches = matches_df[matches_df["imp_cop_no"] == f"MOCK_IMP_COP_NO_{i:02d}"]
            optimal_matches = imp_matches[imp_matches["is_optimal"]]

            assert len(optimal_matches) == 1, f"Import {i} should have exactly 1 optimal match"
            assert len(imp_matches) <= 5, f"Import {i} should have at most 5 total matches"


class TestBoundaryConditions:
    """Test boundary conditions and edge cases."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup the matching service and parameters."""
        self.service = LinearProgrammingOptimizer()
        self.max_distance_thres_km = 643.74
        self.time_tolerance_thres_days = 0

    def test_distance_threshold_boundaries(self):
        """Test distance threshold boundary conditions comprehensively."""
        test_cases = [
            (643.73, True, "Just under threshold"),
            (643.74, True, "Exactly at threshold"),
            (643.75, False, "Just over threshold"),
            (1000.0, False, "Way over threshold"),
            (0.0, True, "Zero distance"),
        ]

        for distance_km, expected_match, description in test_cases:
            data = {
                "imp_cop_no": ["MOCK_IMP_COP_NO_00"],
                "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"],
                "imp_cntr_no": ["MOCK_IMP_CNTR_NO"],
                "xpt_cop_no": ["MOCK_XPT_COP_00"],
                "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00"],
                "xpt_cntr_no": [""],
                "imp_cntr_tpsz_cd": ["D5"],
                "xpt_cntr_tpsz_cd": ["D5"],
                "time_gap_in_hour": [-90],
                "imp_to_xpt_dist_km": [distance_km],
                "dist_sav_km": [120.0],
            }
            test_df = pd.DataFrame(data)
            matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)

            expected_count = 1 if expected_match else 0
            assert len(matches) == expected_count, (
                f"{description}: {distance_km}km should {'match' if expected_match else 'not match'}"
            )

    def test_empty_dataset(self):
        """Test handling of empty input dataset."""
        empty_df = pd.DataFrame(
            columns=[
                "imp_cop_no",
                "imp_bkg_no",
                "imp_cntr_no",
                "xpt_cop_no",
                "xpt_bkg_no",
                "xpt_cntr_no",
                "imp_cntr_tpsz_cd",
                "xpt_cntr_tpsz_cd",
                "time_gap_in_hour",
                "imp_to_xpt_dist_km",
                "dist_sav_km",
            ]
        )
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, empty_df)

        assert len(matches) == 0, "Empty dataset should return no matches"
        assert isinstance(matches, list), "Should return a list even when empty"

    def test_zero_distance_scenarios(self):
        """Test various zero distance scenarios."""
        # Test zero distance with normal threshold
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"],
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"],
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"],
            "xpt_cop_no": ["MOCK_XPT_COP_00"],
            "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00"],
            "xpt_cntr_no": [""],
            "imp_cntr_tpsz_cd": ["D5"],
            "xpt_cntr_tpsz_cd": ["D5"],
            "time_gap_in_hour": [-90],
            "imp_to_xpt_dist_km": [0.0],
            "dist_sav_km": [120.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)

        assert len(matches) == 1, "Should handle zero distance with normal threshold"

        # Test zero distance with zero threshold
        matches = self.service.find_matches(0.0, self.time_tolerance_thres_days, test_df)
        assert len(matches) == 1, "Should find matches at zero distance with zero threshold"


class TestTimeBasedScenarios:
    """Test time-based edge cases."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup the matching service and parameters."""
        self.service = LinearProgrammingOptimizer()
        self.max_distance_thres_km = 643.74
        self.time_tolerance_thres_days = 0

    def test_positive_time_gap(self):
        """Test handling of positive time gaps (export before import)."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"],
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"],
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"],
            "xpt_cop_no": ["MOCK_XPT_COP_00"],
            "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00"],
            "xpt_cntr_no": [""],
            "imp_cntr_tpsz_cd": ["D5"],
            "xpt_cntr_tpsz_cd": ["D5"],
            "time_gap_in_hour": [24],  # Positive gap
            "imp_to_xpt_dist_km": [30.306],
            "dist_sav_km": [120.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, 1, test_df)  # 1 day tolerance

        assert len(matches) == 1, "Should handle positive time gaps within tolerance"

    def test_zero_time_gap(self):
        """Test handling of zero time gap."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"],
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"],
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"],
            "xpt_cop_no": ["MOCK_XPT_COP_00"],
            "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00"],
            "xpt_cntr_no": [""],
            "imp_cntr_tpsz_cd": ["D5"],
            "xpt_cntr_tpsz_cd": ["D5"],
            "time_gap_in_hour": [0],  # Exact timing
            "imp_to_xpt_dist_km": [30.306],
            "dist_sav_km": [120.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)

        assert len(matches) == 1, "Should handle zero time gap"

    def test_time_tolerance_boundary(self):
        """Test time tolerance boundary conditions."""
        test_cases = [
            (23, 0, False, "Just under 1 day with 0 tolerance"),
            (24, 1, True, "Exactly 1 day with 1 day tolerance"),
            (25, 1, False, "Just over 1 day with 1 day tolerance"),
            (0, 0, True, "Zero gap with zero tolerance"),
            (-24, 1, True, "Negative 1 day with 1 day tolerance"),
        ]

        for time_gap_hours, tolerance_days, expected_match, description in test_cases:
            data = {
                "imp_cop_no": ["MOCK_IMP_COP_NO_00"],
                "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"],
                "imp_cntr_no": ["MOCK_IMP_CNTR_NO"],
                "xpt_cop_no": ["MOCK_XPT_COP_00"],
                "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00"],
                "xpt_cntr_no": [""],
                "imp_cntr_tpsz_cd": ["D5"],
                "xpt_cntr_tpsz_cd": ["D5"],
                "time_gap_in_hour": [time_gap_hours],
                "imp_to_xpt_dist_km": [30.306],
                "dist_sav_km": [120.0],
            }
            test_df = pd.DataFrame(data)
            matches = self.service.find_matches(self.max_distance_thres_km, tolerance_days, test_df)

            expected_count = 1 if expected_match else 0
            assert len(matches) == expected_count, f"{description}: should {'match' if expected_match else 'not match'}"


class TestDataQuality:
    """Test data quality and input validation."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup the matching service and parameters."""
        self.service = LinearProgrammingOptimizer()
        self.max_distance_thres_km = 643.74
        self.time_tolerance_thres_days = 0

    def test_null_values_handling(self):
        """Test handling of null/NaN values in data."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00", "MOCK_IMP_COP_NO_01"],
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00", None],  # Null value
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO", "MOCK_IMP_CNTR_NO_01"],
            "xpt_cop_no": ["MOCK_XPT_COP_00", "MOCK_XPT_COP_01"],
            "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00", "MOCK_EXP_BKG_NO_01"],
            "xpt_cntr_no": ["", ""],
            "imp_cntr_tpsz_cd": ["D5", "D5"],
            "xpt_cntr_tpsz_cd": ["D5", "D5"],
            "time_gap_in_hour": [-90, np.nan],  # NaN value
            "imp_to_xpt_dist_km": [30.306, 30.306],
            "dist_sav_km": [120.0, 100.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)

        # Should handle null values gracefully
        assert isinstance(matches, list), "Should return valid result even with null values"

    def test_duplicate_records(self):
        """Test handling of duplicate records."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00", "MOCK_IMP_COP_NO_00"],  # Duplicate
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00", "MOCK_IMP_BKG_NO_00"],
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO", "MOCK_IMP_CNTR_NO"],
            "xpt_cop_no": ["MOCK_XPT_COP_00", "MOCK_XPT_COP_00"],  # Duplicate
            "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00", "MOCK_EXP_BKG_NO_00"],
            "xpt_cntr_no": ["", ""],
            "imp_cntr_tpsz_cd": ["D5", "D5"],
            "xpt_cntr_tpsz_cd": ["D5", "D5"],
            "time_gap_in_hour": [-90, -90],
            "imp_to_xpt_dist_km": [30.306, 30.306],
            "dist_sav_km": [120.0, 120.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)

        # Should handle duplicates appropriately
        assert len(matches) <= 2, "Should handle duplicate records"


class TestPerformanceAndScale:
    """Test performance and scalability."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup the matching service and parameters."""
        self.service = LinearProgrammingOptimizer()
        self.max_distance_thres_km = 643.74
        self.time_tolerance_thres_days = 0

    def test_large_dataset_performance(self):
        """Test performance with larger dataset."""
        num_records = 1000
        data = {
            "imp_cop_no": [f"MOCK_IMP_COP_NO_{i // 10:03d}" for i in range(num_records)],
            "imp_bkg_no": [f"MOCK_IMP_BKG_NO_{i // 10:03d}" for i in range(num_records)],
            "imp_cntr_no": [f"MOCK_IMP_CNTR_NO_{i // 10:03d}" for i in range(num_records)],
            "xpt_cop_no": [f"MOCK_XPT_COP_{i:04d}" for i in range(num_records)],
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i:04d}" for i in range(num_records)],
            "xpt_cntr_no": [""] * num_records,
            "imp_cntr_tpsz_cd": ["D5"] * num_records,
            "xpt_cntr_tpsz_cd": ["D5"] * num_records,
            "time_gap_in_hour": [-90] * num_records,
            "imp_to_xpt_dist_km": [30.306] * num_records,
            "dist_sav_km": [120.0 - (i % 100) for i in range(num_records)],
        }
        test_df = pd.DataFrame(data)

        start_time = time.time()
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        end_time = time.time()

        assert len(matches) > 0, "Should find matches in large dataset"
        assert end_time - start_time < 10, "Should complete within reasonable time"

    def test_large_scale_optimal_suboptimal_limits(self):
        """Test performance and correctness with many imports and exports."""
        num_imports = 50
        num_exports_per_import = 20

        imp_data = []
        exp_data = []
        savings_data = []

        for i in range(num_imports):
            for j in range(num_exports_per_import):
                imp_data.append(f"MOCK_IMP_COP_NO_{i:03d}")
                exp_data.append(f"MOCK_XPT_COP_{i:03d}_{j:02d}")  # Unique exports per import
                savings_data.append(1000 - j)  # Decreasing savings

        data = {
            "imp_cop_no": imp_data,
            "imp_bkg_no": [f"MOCK_IMP_BKG_NO_{i // num_exports_per_import:03d}" for i in range(len(imp_data))],
            "imp_cntr_no": [f"MOCK_IMP_CNTR_NO_{i // num_exports_per_import:03d}" for i in range(len(imp_data))],
            "xpt_cop_no": exp_data,
            "xpt_bkg_no": [f"MOCK_EXP_BKG_NO_{i}" for i in range(len(exp_data))],
            "xpt_cntr_no": [""] * len(imp_data),
            "imp_cntr_tpsz_cd": ["D5"] * len(imp_data),
            "xpt_cntr_tpsz_cd": ["D5"] * len(imp_data),
            "time_gap_in_hour": [-90] * len(imp_data),
            "imp_to_xpt_dist_km": [30.306] * len(imp_data),
            "dist_sav_km": savings_data,
        }
        test_df = pd.DataFrame(data)

        start_time = time.time()
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)
        end_time = time.time()

        matches_df = pd.DataFrame(matches)

        # Verify constraints for each import
        for i in range(num_imports):
            imp_cop_no = f"MOCK_IMP_COP_NO_{i:03d}"
            imp_matches = matches_df[matches_df["imp_cop_no"] == imp_cop_no]
            optimal_matches = imp_matches[imp_matches["is_optimal"]]

            assert len(imp_matches) <= 5, f"Import {i} should have at most 5 matches"
            assert len(optimal_matches) == 1, f"Import {i} should have exactly 1 optimal"

        assert end_time - start_time < 30, "Should complete large dataset within reasonable time"


class TestConfigurationEdgeCases:
    """Test configuration and parameter edge cases."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup the matching service and parameters."""
        self.service = LinearProgrammingOptimizer()

    def test_negative_threshold_handling(self):
        """Test handling of negative thresholds."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"],
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"],
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"],
            "xpt_cop_no": ["MOCK_XPT_COP_00"],
            "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00"],
            "xpt_cntr_no": [""],
            "imp_cntr_tpsz_cd": ["D5"],
            "xpt_cntr_tpsz_cd": ["D5"],
            "time_gap_in_hour": [-90],
            "imp_to_xpt_dist_km": [30.306],
            "dist_sav_km": [120.0],
        }
        test_df = pd.DataFrame(data)

        # Should handle negative thresholds gracefully
        try:
            matches = self.service.find_matches(-100.0, -1, test_df)
            assert isinstance(matches, list), "Should handle negative thresholds"
        except ValueError:
            # If it errors, that's also acceptable behavior
            pass


# Parametrized Tests for Systematic Edge Case Validation
class TestParametrizedScenarios:
    """Parametrized tests with systematic validation."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup the matching service and parameters."""
        self.service = LinearProgrammingOptimizer()
        self.max_distance_thres_km = 643.74
        self.time_tolerance_thres_days = 0

    @pytest.mark.parametrize(
        "distance_km,expected_match",
        [
            (643.73, True),  # Just under threshold
            (643.74, True),  # Exactly at threshold
            (643.75, False),  # Just over threshold
            (1000.0, False),  # Way over threshold
            (0.0, True),  # Zero distance
        ],
    )
    def test_distance_threshold_boundaries_parametrized(self, distance_km, expected_match):
        """Parametrized test for distance threshold boundaries."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"],
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"],
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"],
            "xpt_cop_no": ["MOCK_XPT_COP_00"],
            "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00"],
            "xpt_cntr_no": [""],
            "imp_cntr_tpsz_cd": ["D5"],
            "xpt_cntr_tpsz_cd": ["D5"],
            "time_gap_in_hour": [-90],
            "imp_to_xpt_dist_km": [distance_km],
            "dist_sav_km": [120.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, self.time_tolerance_thres_days, test_df)

        expected_count = 1 if expected_match else 0
        assert len(matches) == expected_count, (
            f"Distance {distance_km} should {'match' if expected_match else 'not match'}"
        )

    @pytest.mark.parametrize(
        "time_gap_hours,tolerance_days,expected_match",
        [
            (0, 0, True),  # Zero gap, zero tolerance
            (-24, 1, True),  # 1 day before, 1 day tolerance
            (24, 1, True),  # 1 day after, 1 day tolerance
            (-25, 1, True),  # Just over 1 day before
            (25, 1, False),  # Just over 1 day after
            (-48, 2, True),  # 2 days before, 2 day tolerance
            (48, 2, True),  # 2 days after, 2 day tolerance
        ],
    )
    def test_time_tolerance_boundaries_parametrized(self, time_gap_hours, tolerance_days, expected_match):
        """Parametrized test for time tolerance boundaries."""
        data = {
            "imp_cop_no": ["MOCK_IMP_COP_NO_00"],
            "imp_bkg_no": ["MOCK_IMP_BKG_NO_00"],
            "imp_cntr_no": ["MOCK_IMP_CNTR_NO"],
            "xpt_cop_no": ["MOCK_XPT_COP_00"],
            "xpt_bkg_no": ["MOCK_EXP_BKG_NO_00"],
            "xpt_cntr_no": [""],
            "imp_cntr_tpsz_cd": ["D5"],
            "xpt_cntr_tpsz_cd": ["D5"],
            "time_gap_in_hour": [time_gap_hours],
            "imp_to_xpt_dist_km": [30.306],
            "dist_sav_km": [120.0],
        }
        test_df = pd.DataFrame(data)
        matches = self.service.find_matches(self.max_distance_thres_km, tolerance_days, test_df)

        expected_count = 1 if expected_match else 0
        assert len(matches) == expected_count, (
            f"Time gap {time_gap_hours}h with {tolerance_days}d tolerance should {'match' if expected_match else 'not match'}"
        )


# Helper Fixtures
@pytest.fixture
def sample_service():
    """Fixture providing a configured LinearProgrammingOptimizer instance."""
    return LinearProgrammingOptimizer()


@pytest.fixture
def standard_test_params():
    """Fixture providing standard test parameters."""
    return {"max_distance_thres_km": 643.74, "time_tolerance_thres_days": 0}


@pytest.fixture
def mock_import_export_data():
    """Fixture providing mock import/export data."""
    return {
        "imp_cop_no": "MOCK_IMP_COP_NO_00",
        "imp_bkg_no": "MOCK_IMP_BKG_NO_00",
        "imp_cntr_no": "MOCK_IMP_CNTR_NO",
        "xpt_cop_no": "MOCK_XPT_COP_00",
        "xpt_bkg_no": "MOCK_EXP_BKG_NO_00",
        "xpt_cntr_no": "",
        "imp_cntr_tpsz_cd": "D5",
        "xpt_cntr_tpsz_cd": "D5",  # Always matches (pre-filtered assumption)
        "time_gap_in_hour": -90,
        "imp_to_xpt_dist_km": 30.306,
        "dist_sav_km": 120.0,
    }


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])
