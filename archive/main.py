import datetime as dt
import json
import time
import uuid
from pathlib import Path

from archive.src.data_core import DataLoader
from archive.src.matching.street_turn import StreetTurnMatcher
from archive.src.thirdparty.gcp import BigQueryManager
from archive.src.utils.preprocess import log_run_info
from root import PROJECT_ID


def main(rpt_date: str, rl_date: str, save_run_dir: Path, criteria: str):
    """_summary_."""
    bq_manager = BigQueryManager()
    data_cfg = {
        "loc": {"data_source": f"{PROJECT_ID}.SOURCE_DATA"},
        "cnt_sz": {"data_source": f"{PROJECT_ID}.RATE_LANE"},
        "rl": {"data_source": f"{PROJECT_ID}.RATE_LANE", "filter_date": rl_date},
        "rpt": {
            "data_source": f"{PROJECT_ID}.REPORT",
            "run_dt": rpt_date,
            "door_cy_value": "Door",
            "hazmat_value": "N",
            "has_customer_nominated_trucker": False,
            "drop_and_pick_value": "N",
        },
        "dis": {"data_source": f"{PROJECT_ID}.REPORT"},
    }
    loader = DataLoader(data_cfg, bq_manager)
    loader.fetch_data()

    matcher = StreetTurnMatcher(loader)

    curr_dt = dt.datetime.now().strftime("%Y-%m-%d")
    _id = uuid.uuid4().hex[:10]
    matcher.run(
        Path(save_run_dir / f"{curr_dt}_{_id}" / rpt_date),
        specific_cy=["USATL63"],  # USCMH60
        criteria=criteria,
    )


if __name__ == "__main__":
    start_date = dt.datetime(2024, 10, 1)
    end_date = dt.datetime(2024, 10, 31)
    delta = dt.timedelta(days=1)
    criteria = "distance"  # optimization criteria must be in ["distance", "distance_cost", "cost"]

    run_info: dict = {}
    current_date = start_date
    save_dir = Path(f"runs/{criteria}_USATL63_october_full")
    while current_date <= end_date:
        t1: float = time.time()
        run_date_1: str = current_date.strftime("%Y-%m-%dT05:00:00")
        main(run_date_1, current_date.strftime("%Y-%m-%d"), save_dir, criteria)
        t2: float = time.time()
        log_run_info(run_info, run_date_1, t2 - t1)

        t1 = time.time()
        run_date_2: str = current_date.strftime("%Y-%m-%dT12:30:00")
        main(run_date_2, current_date.strftime("%Y-%m-%d"), save_dir, criteria)
        t2 = time.time()
        log_run_info(run_info, run_date_2, t2 - t1)

        current_date += delta

    # save run time result to json
    with open(save_dir / "run_info_result.json", "w") as f:
        json.dump(run_info, f)

    # insert cached distance from calling GGAPI to BQ table
    # insert_cached_pairs_to_bq()
