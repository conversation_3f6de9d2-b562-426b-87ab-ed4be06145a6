import argparse
import datetime as dt
import shutil
import uuid
from pathlib import Path

import pandas as pd

from archive.src_v2.const import ConstMap, settings
from archive.src_v2.core.data_loader import DataLoader
from archive.src_v2.core.data_model import DataCollection
from archive.src_v2.core.geo.geo import convert_distance
from archive.src_v2.core.matching.street_turn_matcher import StreetTurnMatcher
from archive.src_v2.thirdparty.gcp import upload_to_gcs
from archive.src_v2.utils.decorators import measure_time
from archive.src_v2.utils.file_utils import save_json
from archive.src_v2.utils.logger import log_handler
from root import RUN_DIR, TMP_DIR

logger = log_handler.get_logger(name=__name__)


@measure_time()
def process_data(node_cd: str, run_report_dt: str, run_ratelane_date: str | None, output_dir: Path):
    """Fetch data for a given configuration."""
    output_dir.mkdir(parents=True, exist_ok=True)

    data_loader = DataLoader(
        cfg={
            "rl": {
                "filter_date": run_ratelane_date,
                "node_cd": (node_cd[: ConstMap.LOCATION_CODE_PREFIX_LENGTH],),
            },
            "rpt": {
                "run_dt": run_report_dt,
                "door_cy_value": settings.SYSTEM.DELIVERY_TYPE,
                "hazmat": settings.SYSTEM.HAZMAT,
                "customer_nominated_trucker": settings.SYSTEM.CUSTOMER_NOMINATED_TRUCKER,
                "drop_and_pick": settings.SYSTEM.DROP_AND_PICK,
                "node_cd": (node_cd,),
            },
        },
        save_dir=output_dir / node_cd / "data",
    )
    data_loader.fetch_data()

    location_data = data_loader.get_data("loc")
    cnt_size_map_data = data_loader.get_data("cnt_sz")
    report_data: pd.DataFrame = data_loader.get_data("rpt")
    ratelane_data: pd.DataFrame = data_loader.get_data("rl")
    ratelane_mapping_data: pd.DataFrame = data_loader.get_data("rl_mapping")
    distance_data: pd.DataFrame = data_loader.get_data("dis")
    report_data = data_loader.process_report_data(report_data, location_data)

    if report_data.empty:
        logger.warning(f"No data found for {node_cd} on {run_report_dt}")
        return None

    return DataCollection(
        location_data=location_data,
        cnt_size_map_data=cnt_size_map_data,
        ratelane_data=ratelane_data,
        ratelane_mapping_data=ratelane_mapping_data,
        report_data=report_data,
        distance_data=distance_data,
    )


@measure_time()
def process_matching(node_cd: str, run_dt: str, candidate_data: DataCollection, output_dir: Path) -> dict:
    """Process data matching for a given configuration.

    tuple[int, int]: (no_cal_gmaps_api, cache_len)
    """
    output_dir.mkdir(parents=True, exist_ok=True)

    matcher = StreetTurnMatcher()
    run_info = matcher.run(
        node_cd=node_cd,
        run_dt=run_dt,
        data=candidate_data,
        criteria=settings.SYSTEM.CRITERIA,
        distance_method=settings.SYSTEM.DISTANCE_METHOD,
        save_dir=output_dir / node_cd / "results",
    )

    return run_info


@measure_time()
def main(run_datetime_str: str, output_dir: Path):
    """Process matching for a specific datetime.

    Args:
        run_datetime_str (str): Datetime string in format 'YYYY-MM-DDThh:mm:ss'
        output_dir (Path): Output directory
    """
    logger.info(f"Processing matching for {run_datetime_str}")

    run_info: list[dict] = []

    # Parse the datetime
    current_date = dt.datetime.strptime(run_datetime_str, "%Y-%m-%dT%H:%M:%S")
    current_date_str = current_date.strftime("%Y-%m-%d")

    # Create specific output directory for this run
    output_dir_for_run = output_dir / current_date.strftime("%Y%m%dT%H%M%S")

    for node_cd in settings.SYSTEM.SPECIFIC_NODES:
        _run_info = {
            "node_cd": node_cd,
            "run_datetime": run_datetime_str,
            "time_available_gap": settings.SYSTEM.TIME_AVAILABLE_GAP,
            "distance_thres": round(convert_distance(settings.SYSTEM.DISTANCE_THRES_METERS / 1000, "miles")),
        }

        # Process node location
        candidate_data = process_data(
            node_cd=node_cd,
            run_report_dt=run_datetime_str,
            run_ratelane_date=current_date_str,
            output_dir=output_dir_for_run,
        )
        matching_info = process_matching(
            node_cd=node_cd,
            run_dt=run_datetime_str,
            candidate_data=candidate_data,
            output_dir=output_dir_for_run,
        )
        _run_info.update(matching_info)
        run_info.append(_run_info)

    # Upload results to storage
    upload_to_gcs(output_dir_for_run)

    # Save run info to files and BigQuery
    save_json(run_info, output_dir / "run_info.json")
    pd.DataFrame(run_info).to_csv(output_dir / "run_info.csv", index=False)

    # Upload run info to BigQuery
    upload_to_gcs(output_dir=output_dir)

    logger.info("Matching processing complete.")
    cleanup_tmp_dir()
    return run_info


def setup():
    """Set up the run configuration."""
    current_datetime = dt.datetime.now().strftime("%Y%m%d")
    run_id = uuid.uuid4().hex[:10]
    output_dir = Path(RUN_DIR / f"{current_datetime}_{run_id}")
    output_dir.mkdir(parents=True, exist_ok=True)

    # save settings.SYSTEM and settings.DB_CONFIG to a file
    # TODO: save back to toml format
    save_json(settings.SYSTEM.model_dump(), output_dir / "system_config.json")
    save_json(settings.DB.model_dump(), output_dir / "db_config.json")
    save_json(settings.GCP.model_dump(), output_dir / "gcp_config.json")

    return output_dir


def cleanup_tmp_dir():
    """Remove all files inside the temporary directory while preserving the directory itself."""
    if settings.ENV.ENVIRONMENT != 0:  # not dev env
        for file_path in TMP_DIR.glob("*"):
            if file_path.is_file():
                file_path.unlink()
            elif file_path.is_dir():
                shutil.rmtree(file_path)
        logger.info("Temporary files removed.")


if __name__ == "__main__":
    output_dir = setup()

    # add ArgumentParser for start_date and end_date
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--date_time", type=str, help="Start date for the run, format: YYYYMMDD", default="2025-03-26T05:00:00"
    )

    args = parser.parse_args()

    run_info = main(args.date_time, output_dir)

    cleanup_tmp_dir()
