import logging
import sys
from logging.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON>, TimedRotatingFileHandler
from pathlib import Path

from archive.src.const.config import global_config as GlobalSettings  # noqa: N812


class Handlers:
    def __init__(self):
        """Initialize the log handlers."""
        self.formatter = logging.Formatter(GlobalSettings.LOG_FORMATTER, datefmt="%Y-%m-%d %H:%M:%S")
        self.rotation = GlobalSettings.LOG_ROTATION

    def get_console_handler(self):
        """Get a console handler."""
        console_handler = logging.StreamHandler(sys.stdout.flush())
        console_handler.setFormatter(self.formatter)
        return console_handler

    def get_file_handler(self, log_filename: Path):
        """Get a file handler."""
        file_handler = TimedRotatingFileHandler(
            filename=log_filename,
            when=self.rotation,
            backupCount=10,
            encoding="utf-8",
        )
        file_handler.setFormatter(self.formatter)
        return file_handler

    def get_socket_handler(self):
        """Get a socket handler."""
        socket_handler = SocketHandler("127.0.0.1", 19996)  # default listening address
        return socket_handler

    def get_handlers(self):
        """Get all available handlers."""
        return [
            self.get_console_handler(),
            self.get_socket_handler(),
        ]
