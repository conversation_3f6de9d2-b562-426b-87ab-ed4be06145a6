import datetime as dt
import math
from datetime import datetime

import googlemaps
import numpy as np
import pandas as pd
from dotenv import load_dotenv
from google.cloud import bigquery

from archive.src.const.config import global_config as GlobalSettings  # noqa: N812
from archive.src.utils.logger import log_handler

load_dotenv()
EARTH_RADIUS = 6371  # Radius of the Earth in km
gg_api_call_count = 0
bq_client = bigquery.Client()
gmaps = googlemaps.Client(key=GlobalSettings.GG_MAP_API_KEY)
cache_distance: dict[tuple[str, str], float] = {}
cache_distance_api: dict[tuple[str, str], tuple[str | None, float, str, dt.datetime, dt.datetime]] = {}
current_date_time: dt.datetime = dt.datetime.now(dt.timezone.utc)

logger = log_handler.get_logger(name="utils")


def get_distance_in_meter(origin: str, destination: str, label: str | None = None) -> float:
    """Fetch the driving distance in meters between two locations using Google Maps API.

    Args:
        origin (str): The origin location (e.g., "GAINESVILLE, GA").
        destination (str): The destination location (e.g., "POWDER SPRINGS, GA").
        label (str): Name of type calling API for calculating distance.

    Returns:
        float: The distance in meters as a float.
    """
    global gg_api_call_count

    if gmaps == "":
        raise ValueError("Google Map API not found!")

    try:
        gg_api_call_count += 1

        # Call the Distance Matrix API
        distance_matrix_result = gmaps.distance_matrix([origin], [destination], mode="driving")
        distance_text = distance_matrix_result["rows"][0]["elements"][0]["distance"]["text"]
        duration_text = distance_matrix_result["rows"][0]["elements"][0]["duration"]["text"]

        # Detect and convert distance to meters
        if "km" in distance_text:
            distance_meters = float(distance_text.replace(",", "").split()[0]) * 1000  # Convert km to meters
        elif "m" in distance_text:
            distance_meters = float(distance_text.replace(",", "").split()[0])  # Already in meters
        else:
            raise ValueError("Unknown distance unit in Google Maps API response")

        cache_distance_api[(origin, destination)] = (
            "all",
            distance_meters,
            duration_text,
            current_date_time,
            current_date_time,
        )

        return distance_meters

    except Exception:
        # logger.error(f"Error: {err}")
        return -1


def get_distance_between_locations(
    origin: str, destination: str, distance_data: pd.DataFrame, label: str | None = None
) -> float:
    """Fetches the distance between two locations from the distance data or calculates it if not found.

    Args:
        origin (str): The origin city.
        destination (str): The destination city.
        distance_data (pd.DataFrame): The DataFrame containing pre-calculated distances.
        label (str): Name of type calling api for calculate distance

    Returns:
        float: The distance between the two locations.
    """
    if (origin, destination) in cache_distance:
        return cache_distance[(origin, destination)]

    distance_row = distance_data[(distance_data["origin"] == origin) & (distance_data["destination"] == destination)]
    if distance_row.empty:
        distance_row = distance_data[
            (distance_data["origin"] == destination) & (distance_data["destination"] == origin)
        ]

    if distance_row.empty:
        distance = get_distance_in_meter(origin, destination, label)
    else:
        distance = int(distance_row.iloc[0]["distance"])
    # distance = get_distance_in_meter(origin, destination, label)

    cache_distance[(origin, destination)] = distance
    cache_distance[(destination, origin)] = distance

    return float(distance)


def find_key_by_location_name(location_name, locations_dict):
    """_summary_.

    Args:
        location_name (_type_): _description_
        locations_dict (_type_): _description_

    Returns:
        _type_: _description_
    """
    for key, value in locations_dict.items():
        if value["location_name"] == location_name:
            return key
    return None


def haversine_distance(lat1, lon1, lat2, lon2):
    """_summary_.

    Args:
        lat1 (_type_): _description_
        lon1 (_type_): _description_
        lat2 (_type_): _description_
        lon2 (_type_): _description_

    Returns:
        _type_: _description_
    """
    dlat = np.radians(lat2 - lat1)
    dlon = np.radians(lon2 - lon1)
    a = np.sin(dlat / 2) ** 2 + np.cos(np.radians(lat1)) * np.cos(np.radians(lat2)) * np.sin(dlon / 2) ** 2
    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
    return EARTH_RADIUS * c


def get_total_distance_v2(row, location_dict, distance_ap, distance_cp, im_address, ex_address, bound):
    """Calculate the total distance between locations using a distance dataset or Google Maps API as fallback.

    Args:
        row (pd.Series): Row from the DataFrame containing the lane description.
        location_dict (dict): Dictionary mapping location codes to names.
        distance_ap (pd.DataFrame): DataFrame containing pre-calculated distances.
        distance_cp (pd.DataFrame): DataFrame containing pre-calculated distances.
        im_address (str): __description__
        ex_address (str): __description__
        bound (str): __description__
    Returns:
        int or None: Total distance between the locations or None if the distance cannot be calculated.
    """
    if bound not in ["im", "ex"]:
        raise ValueError("bound must be in ['im', 'ex']")
    try:
        cy_to_door, door_to_cy = -1, -1
        loc1, loc2, loc3 = row["lane_description"].split("_")
        # Get value from via_point1 if available
        loc3 = row["via_point1"] if row["via_point1"] else loc1

        loc1_name = location_dict[loc1[:5]]["location_name"]
        loc2_name = location_dict[loc2[:5]]["location_name"]
        loc3_name = location_dict[loc3[:5]]["location_name"]

        if bound == "im":
            cy_to_door = get_distance_between_locations(loc1_name, im_address, distance_ap, label="all")
            door_to_cy = get_distance_between_locations(im_address, loc3_name, distance_ap, label="all")

        elif bound == "ex":
            cy_to_door = get_distance_between_locations(loc1_name, ex_address, distance_ap, label="all")
            door_to_cy = get_distance_between_locations(ex_address, loc3_name, distance_ap, label="all")

        if cy_to_door == -1:
            cy_to_door = get_distance_between_locations(loc1_name, loc2_name, distance_cp, label="all")

        if door_to_cy == -1:
            door_to_cy = get_distance_between_locations(loc2_name, loc3_name, distance_cp, label="all")

        return cy_to_door + door_to_cy
    except Exception as e:
        logger.info(f"Error: {e}")
        return None


def get_distance_saved_v2(
    importland,
    exportland,
    location_dict,
    distance_ap,
    distance_cp,
    distance_aa,
    im_address,
    ex_address,
):
    """_summary_.

    Args:
        importland (pd.Dataframe): _description_
        exportland (pd.Dataframe): _description_
        location_dict (dict): _description_
        distance_ap (pd.Dataframe): _description_
        distance_cp (pd.Dataframe): _description_
        distance_aa (pd.Dataframe): _description_
        im_address (str): _description_
        ex_address (str): _description_

    Returns:
        int: distance saved in km
    """
    iloc1, iloc2, _ = importland.split("_")
    eloc1, eloc2, _ = exportland.split("_")

    iloc1_name = location_dict[iloc1[:5]]["location_name"]
    iloc2_name = location_dict[iloc2[:5]]["location_name"]

    eloc1_name = location_dict[eloc1[:5]]["location_name"]
    eloc2_name = location_dict[eloc2[:5]]["location_name"]

    cy_to_imdoor = get_distance_between_locations(im_address, iloc1_name, distance_ap, label="all")
    if cy_to_imdoor == -1:
        cy_to_imdoor = get_distance_between_locations(iloc1_name, iloc2_name, distance_cp, label="all")

    imdoor_to_exdoor = get_distance_between_locations(im_address, ex_address, distance_aa, label="all")
    if imdoor_to_exdoor == -1:
        imdoor_to_exdoor = get_distance_between_locations(iloc2_name, eloc1_name, distance_cp, label="all")

    exdoor_to_cy = get_distance_between_locations(ex_address, eloc1_name, distance_ap, label="all")
    if exdoor_to_cy == -1:
        exdoor_to_cy = get_distance_between_locations(eloc1_name, eloc2_name, distance_cp, label="all")

    return cy_to_imdoor + imdoor_to_exdoor + exdoor_to_cy


def get_run_info() -> tuple[int, int]:
    """Get the total number of API calls made to the Google Maps API."""
    return gg_api_call_count, len(cache_distance)


def log_run_info(run_info: dict, current_date: str, run_time: float) -> dict[str, dict]:
    """Logs the runtime, API call count, and distance cache length for the current run.

    Args:
        run_info (dict): __description__
        current_date (datetime): __description__
        run_time (float): __description__
    """
    # Store run information for the current date
    run_info[current_date] = {
        "run_time": run_time,
        "gg_api_count": gg_api_call_count,
        "distance_cache_len": len(cache_distance),
    }

    return run_info


def _insert_cached_pairs_to_bq(client, table_ref, cache_distance_api):
    """Insert new (origin, destination) pairs from the cache into a BigQuery table.

    This function checks if a BigQuery table exists. If not, it creates the table and inserts
    the new cached pairs (origin, destination) along with additional information like label,
    distance, duration, creation date, and update date.

    Args:
        client (bigquery.Client): A BigQuery client instance to execute queries.
        table_ref (str): The BigQuery table reference where the data should be inserted.
        cache_distance_api (dict): A dictionary with key as (origin, destination) and value as
                                   (label, distance_km, duration, current_date_time, current_date_time).

    Returns:
        None
    """
    # Check if the table exists, if not, create one
    try:
        client.get_table(table_ref)  # Make an API request to get the table
        logger.info(f"Table {table_ref} exists.")
    except Exception:
        # Create the table if not found
        logger.info(f"Table {table_ref} not found. Creating a new table.")
        schema = [
            bigquery.SchemaField("origin", "STRING", mode="REQUIRED"),
            bigquery.SchemaField("destination", "STRING", mode="REQUIRED"),
            bigquery.SchemaField("category", "STRING", mode="NULLABLE"),
            bigquery.SchemaField("distance", "FLOAT", mode="NULLABLE"),
            bigquery.SchemaField("duration", "STRING", mode="NULLABLE"),
            bigquery.SchemaField("cre_at", "DATETIME", mode="NULLABLE"),
            bigquery.SchemaField("upd_at", "DATETIME", mode="NULLABLE"),
        ]
        table = bigquery.Table(table_ref, schema=schema)
        client.create_table(table)
        logger.info(f"Created table {table_ref}.")

    # Prepare data to be inserted
    rows_to_insert = []
    count = 0
    for (origin, destination), (
        label,
        distance_km,
        duration,
        created_at,
        updated_at,
    ) in cache_distance_api.items():
        # Ensure that created_at and updated_at are datetime objects, otherwise handle them
        if isinstance(created_at, datetime):
            created_at_str = created_at.strftime("%Y-%m-%dT%H:%M:%S")  # Remove timezone info
        else:
            created_at_str = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")  # Default to now if not a datetime

        if isinstance(updated_at, datetime):
            updated_at_str = updated_at.strftime("%Y-%m-%dT%H:%M:%S")  # Remove timezone info
        else:
            updated_at_str = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")  # Default to now if not a datetime

        # Prepare row for insertion
        row = {
            "origin": origin,
            "destination": destination,
            "category": label,
            "distance": distance_km,
            "duration": duration,
            "cre_at": created_at_str,  # Correct datetime format
            "upd_at": updated_at_str,  # Correct datetime format
        }
        rows_to_insert.append(row)
        count += 1

    # Insert data into BigQuery table
    try:
        client.insert_rows_json(table_ref, rows_to_insert)
        logger.info(f"Added {count} new pairs to the {table_ref}")
    except Exception as err:
        logger.info(f"Errors occurred while inserting rows: {err}")


def insert_cached_pairs_to_bq():
    """_summary_."""
    _insert_cached_pairs_to_bq(
        bq_client,
        table_ref="one-global-dilab-matchback-dev.REPORT.DISTANCES_TEST",
        cache_distance_api=cache_distance_api,
    )


def log_scale(value):
    """_summary_."""
    return math.log1p(value)
