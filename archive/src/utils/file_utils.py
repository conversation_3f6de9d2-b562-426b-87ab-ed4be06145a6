import datetime as dt

import yaml

from archive.src.utils.logger import log_handler

logger = log_handler.get_logger(name="utils")


def load_yaml_config(fname: str) -> dict:
    """Load a YAML configuration file.

    Args:
        fname (str): The path to the YAML configuration file.

    Returns:
            dict: The configuration settings.
    """
    try:
        with open(fname, encoding="utf-8") as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        logger.error("Configuration file %s not found.", fname)
        raise
    except yaml.YAMLError as e:
        logger.error("Error parsing configuration file: %s", e)
        raise


def extract_datetime_from_filename(filename: str) -> str:
    """Extract and format the datetime part from a filename.

    Args:
        filename (str): The filename containing the datetime part.

    Returns:
        str: The formatted datetime string in the format 'YYYY-MM-DD HH:MM:SS'.
    """
    # Extract the datetime part from the filename
    datetime_str = filename.split("_")[1].split(".")[0]
    # Convert to datetime object
    datetime_obj = dt.datetime.strptime(datetime_str, "%Y%m%d%H%M")
    # Format as YYYY-MM-DD HH:MM:SS
    return datetime_obj.strftime("%Y-%m-%d %H:%M:%S")
