from enum import IntEnum

from dotenv import load_dotenv
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

load_dotenv()


class EnvStage(IntEnum):
    DEV = 0
    STAGING = 1
    PROD = 2


class _BaseSettings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", extra="ignore", env_file_encoding="utf-8")


class DatabaseSettings(_BaseSettings):
    # Report Processor
    REPORT_DATA_FOLDER_ID: str = Field(default="")
    REPORT_DATASET_ID: str = Field(default="REPORT")
    REPORT_RAW_TABLE_ID: str = Field(default="REPORT_RAW_GD")
    REPORT_TABLE_ID: str = Field(default="REPORT_GD")


class GlobalSettings(_BaseSettings):
    """Global settings for the application."""

    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8")

    ENV: EnvStage = Field(default=EnvStage.DEV)
    VERBOSE: bool = Field(default=False)

    # Log settings
    LOG_FORMATTER: str = "Log entry for %(name)s: %(asctime)s %(levelname)s %(filename)s %(module)s - %(funcName)s(%(lineno)d): %(message)s"
    LOG_ROTATION: str = "midnight"
    LOG_LEVEL: str = Field(default="INFO")

    # GCP
    GDRIVE_CONFIG: str = Field(default="config/gdrive_config.yaml")
    SERVICE_ACCOUNT_ENCODE: str = Field(default="")
    GG_MAP_API_KEY: str = Field(default="")

    # @field_validator("SERVICE_ACCOUNT_ENCODE")
    # @classmethod
    # def validate_service_account(cls, v: str) -> str:
    #     """Validate the SERVICE_ACCOUNT_ENCODE variable."""
    #     if v == "":
    #         raise ValueError("SERVICE_ACCOUNT_ENCODE must be set")
    #     return v


global_config = GlobalSettings()
database_config = DatabaseSettings()

if __name__ == "__main__":
    print(global_config.model_dump())
