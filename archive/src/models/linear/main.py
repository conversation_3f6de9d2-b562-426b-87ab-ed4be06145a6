import time
from collections.abc import Generator
from multiprocessing import Pool
from pathlib import Path

import pulp

from archive.src.utils.logger import log_handler
from root import RUN_DIR

logger = log_handler.get_logger(name="linear_solver")


class LinearSolver:
    def __init__(self, run_dir: Path = RUN_DIR):
        """Initializes the LinearProgramming object.

        Args:
            run_dir (Path): __description__
        """
        self.run_dir = run_dir

    def solve(
        self,
        save_dir: Path,
        valid_pairs: dict[str, dict[str, int]],
        savings: dict[tuple[str, str], float],
        n_ex: int,
        n_im: int,
        loc: str,
        save_model: bool = False,
    ) -> dict[str, dict[str, pulp.LpVariable]]:
        """Solve the linear programming problem for maximizing savings with a dictionary of pairs.

        Args:
            save_dir (Path): saving path.
            valid_pairs (Dict[str, Dict[str, int]]): Dictionary of valid import-export pairs.
                                                    Format: {'im1': {'ex1': 1, 'ex2': 1}, ...}.
            savings (Dict[Tuple[str, str], float]): Savings between imports and exports.
            n_ex (int): Maximum number of exports that can be matched.
            n_im (int): Maximum number of imports that can be matched.
            loc (str): location name.
            save_model (bool): True to save the model to a file, False otherwise.

        Returns:
            Dict[str, Dict[str, pulp.LpVariable]]: The matching result as a dictionary of decision variables.
        """
        tik = time.time()

        # Create a linear programming problem
        lp = pulp.LpProblem("Maximize_Savings", pulp.LpMaximize)  # maximize
        # lp = pulp.LpProblem("Minimize_Distance", pulp.LpMinimize) #minimize

        # Extract all unique imports and exports from valid_pairs
        imports = list(valid_pairs.keys())
        exports = list({ex for export_dict in valid_pairs.values() for ex in export_dict.keys()})

        # Extract all unique imports and exports from valid_pairs
        imports = list(valid_pairs.keys())
        exports = list({ex for export_dict in valid_pairs.values() for ex in export_dict.keys()})

        # Decision variables
        x = pulp.LpVariable.dicts("match", (imports, exports), cat="Binary")

        # Objective function
        lp += (
            pulp.lpSum([savings[(i, j)] * x[i][j] for i in valid_pairs for j in valid_pairs[i] if (i, j) in savings]),
            "Total_Savings",
        )

        # Constraints
        for j in exports:
            lp += pulp.lpSum([x[i][j] for i in imports if j in valid_pairs[i]]) <= n_ex, f"Export_{j}_constraint"
        for i in imports:
            lp += pulp.lpSum([x[i][j] for j in valid_pairs[i]]) <= n_im, f"Import_{i}_constraint"

        # Write the problem to a file
        if save_model:
            save_path = save_dir / f"lp_solver/lp_mode_{loc}.lp"
            save_path.parent.mkdir(parents=True, exist_ok=True)
            lp.writeLP(save_path)

        # Solve the problem
        lp.solve(pulp.PULP_CBC_CMD(msg=False))
        # lp.solve(pulp.GLPK_CMD(msg=False))

        tok = time.time()
        logger.info(f"Linear solver runtime: {round(tok - tik, 2)}s")

        return x

    @staticmethod
    def _parallel_solve(
        imports: list[str], exports: list[str], savings: dict[tuple[str, str], float], n_ex: int, n_im: int
    ) -> dict[str, dict[str, pulp.LpVariable]]:
        """Solve the linear programming problem in parallel.

        Args:
            imports (List[str]): List of import locations.
            exports (List[str]): List of export locations.
            savings (Dict[Tuple[str, str], float]): Savings between imports and exports.
            n_ex (int): Number of export candidates.
            n_im (int): Number of import candidates.

        Returns:
            Dict[str, Dict[str, pulp.LpVariable]]: The matching result.
        """
        lp = pulp.LpProblem("Maximize_Savings", pulp.LpMaximize)

        # Decision variables
        x = pulp.LpVariable.dicts("match", (imports, exports), cat="Binary")

        # Objective function
        lp += pulp.lpSum([savings[i, j] * x[i][j] for i in imports for j in exports]), "Total_Savings"

        # Constraints
        for j in exports:
            lp += pulp.lpSum([x[i][j] for i in imports]) <= n_ex, f"Export_{j}_constraint"
        for i in imports:
            lp += pulp.lpSum([x[i][j] for j in exports]) <= n_im, f"Import_{i}_constraint"

        # Solve the problem
        lp.solve(pulp.PULP_CBC_CMD(msg=False))

        return x

    def parallel_solve(
        self,
        imports: list[str],
        exports: list[str],
        savings: dict[tuple[str, str], float],
        n_ex: int,
        n_im: int,
        num_chunks: int,
    ) -> dict[str, dict[str, pulp.LpVariable]]:
        """Run the linear solver in parallel by splitting data into chunks.

        Args:
            imports (List[str]): List of import locations.
            exports (List[str]): List of export locations.
            savings (Dict[Tuple[str, str], float]): Savings between imports and exports.
            n_ex (int): Number of export candidates.
            n_im (int): Number of import candidates.
            num_chunks (int): Number of chunks to split data into for parallel processing.

        Returns:
            Dict[str, Dict[str, pulp.LpVariable]]: The combined results from parallel processing.
        """
        tik = time.time()

        import_chunks = list(self.chunk_data(imports, len(imports) // num_chunks))
        export_chunks = list(self.chunk_data(exports, len(exports) // num_chunks))

        savings_chunks = [
            (i_chunk, e_chunk, {(i, j): savings.get((i, j), 0) for i in i_chunk for j in e_chunk}, n_ex, n_im)
            for i_chunk in import_chunks
            for e_chunk in export_chunks
        ]

        with Pool(processes=num_chunks) as pool:
            results = pool.starmap(self._parallel_solve, savings_chunks)

        x: dict[str, dict[str, pulp.LpVariable]] = {i: {} for i in imports}
        for result in results:
            for i in result:
                x[i].update(result[i])

        tok = time.time()
        logger.info(f"Linear solver time: {round(tok - tik, 2)}s")

        return x

    @staticmethod
    def chunk_data(data: list[str], chunk_size: int) -> Generator[list[str], None, None]:
        """Chunk data into smaller batches.

        Args:
            data (List[str]): The data to be chunked.
            chunk_size (int): The size of each chunk.

        Yields:
            Generator[List[str], None, None]: A chunk of the data.
        """
        for i in range(0, len(data), chunk_size):
            yield data[i : i + chunk_size]
