"""A wrapper for the Google Cloud BigQuery client."""

from __future__ import annotations

import base64
import json
import os
from pathlib import Path

import google.cloud.bigquery as bigquery
import pandas as pd
from google.oauth2 import service_account

from archive.src.data_model.schemas import REPORT_RAW as REPORT_RAW_SCHEMA
from archive.src.utils.exceptions import AuthenticationError, BigQueryError
from archive.src.utils.file_utils import extract_datetime_from_filename
from archive.src.utils.logger import log_handler
from root import PROJECT_ID

logger = log_handler.get_logger(name="gcp")


def sanitize_column_name(column: str) -> str:
    """Sanitize a column name by converting it to lowercase and replacing certain characters.

    Args:
        column (str): The original column name.

    Returns:
        str: The sanitized column name.
    """
    return (
        column.upper()
        .replace(" ", "_")
        .replace("/", "_")
        .replace("&", "and")
        .replace(".", "")
        .replace("?", "")
        .replace("(", "")
        .replace(")", "")
        .replace("-", "_")
        .replace("'", "")
        .replace(",", "")
        .replace("CONTAINER", "CNTR")
        .replace("NUMBER", "NO")
        .replace("BOOKING", "BKG")
        .upper()
    )


class BigQueryManager:
    """A wrapper for the Google Cloud BigQuery client."""

    def __init__(self):
        """Initialize the BigQueryInteraction class."""
        self.client = self.__authenticate_service_account()

    def __load_service_account(self) -> dict:
        """Load the service account info from the provided file."""
        msg = "SERVICE_ACCOUNT_ENCODE not found. Check eviroment variables."
        try:
            __base64_str = os.environ.get("SERVICE_ACCOUNT_ENCODE", "")
            if __base64_str == "":
                raise ValueError(msg)

            __decoded_bytes = base64.b64decode(__base64_str)
            __decoded_str = __decoded_bytes.decode("utf-8")
        except Exception as err:
            logger.error(msg)
            raise ValueError(msg) from err

        return json.loads(__decoded_str)

    def __authenticate_service_account(self):
        """Authenticate using the provided service account info and build the Drive service."""
        try:
            credentials = service_account.Credentials.from_service_account_info(self.__load_service_account())
            return bigquery.Client(credentials=credentials, project=PROJECT_ID)
        except Exception as err:
            msg = f"Failed to authenticate: {str(err)}"
            logger.error(msg)
            raise AuthenticationError(msg) from err

    def execute_query(self, query: str, job_config: bigquery.QueryJobConfig | None = None, **kwargs) -> pd.DataFrame:
        """Execute a BigQuery SQL query.

        Args:
            query (str): The SQL query to execute.
            job_config (bigquery.QueryJobConfig, optional): The configuration for the query job. Defaults to None.
            **kwargs: Additional keyword arguments to pass to the query method.

        Returns:
            pd.DataFrame: The results of the query.

        Raises:
            BigQueryError: If there's an error executing the query.
        """
        try:
            query_job = self.client.query(query, job_config=job_config, **kwargs)
            results = query_job.result()
            return results.to_dataframe()
        except Exception as e:
            msg = f"Error executing query: {str(e)}"
            logger.error(msg)
            raise BigQueryError(msg) from e

    def load_report_raw_to_bq(self, csv_paths: list[Path], dataset_id: str, table_id: str):
        """Load a CSV file to BigQuery."""
        if len(csv_paths) == 0:
            logger.info("No files found to load.")
            return

        table_ref = self.client.dataset(dataset_id).table(table_id)
        job_config = bigquery.LoadJobConfig(
            schema=REPORT_RAW_SCHEMA,
            source_format=bigquery.SourceFormat.CSV,
            write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
            time_partitioning=bigquery.TimePartitioning(
                type_=bigquery.TimePartitioningType.DAY, field="EDW_UPD_DT", require_partition_filter=True
            ),
        )

        for fname in csv_paths:
            df = pd.read_csv(fname, sep="\t")  # Use tab as delimiter

            column_mapping = {col: sanitize_column_name(col) for col in df.columns}
            df.rename(columns=column_mapping, inplace=True)
            df[df.columns[[17, 18, 19, 20]]] = df[df.columns[[17, 18, 19, 20]]].apply(pd.to_datetime)
            df["CRE_DT"] = pd.Timestamp.now()
            df["UPD_DT"] = pd.Timestamp.now()
            df["DEL_DT"] = None
            df["EDW_UPD_DT"] = extract_datetime_from_filename(os.path.basename(fname))
            # check if the columns exist in the dataframe

            drop_lst = ["REASON", "REMARK"]
            for col in drop_lst:
                if col in df.columns:
                    df.drop(col, axis=1, inplace=True)

            job = self.client.load_table_from_dataframe(df, table_ref, job_config=job_config)
            job.result()

            logger.info("Loaded %d rows into %s.%s", len(df), dataset_id, table_id)

    def process_report_raw(self, edw_upd_dt_str: str, dataset_id: str, table_id: str, table_raw_id: str):
        """Process the report raw data by joining it with the DWL_COP_HIS table."""
        query = """
        INSERT INTO `{dataset_id}.{table_id}`
        (
            WITH utc_report AS(
                SELECT
                    *,
                    -- CASE
                    --   WHEN CNTR_NO IS NULL THEN "COMU0000000"
                    --   ELSE CNTR_NO
                    -- END CNTR_NO_CONVERT,
                    TIMESTAMP_ADD(EDW_UPD_DT, INTERVAL 4 HOUR) AS EDW_UPD_DT_UTC
                FROM `{dataset_id}.{table_raw_id}`
                WHERE EDW_UPD_DT = DATETIME("{edw_upd_dt}")
            ),
            ranked_report AS (
                SELECT
                    a.*,
                    b.COP_STS_CD,
                    b.BKG_STS_CD,
                    b.BKG_EVNT_TP_CD,
                    b.CRE_DT AS DWL_COP_HIS_CRE_DT
                FROM utc_report a
                LEFT JOIN `SOURCE_DATA.DWL_COP_HIS` b
                ON
                    a.COP_NO = b.COP_NO
                    AND a.BKG_NO = b.BKG_NO
                    -- AND a.CNTR_NO_CONVERT = b.CNTR_NO
                    AND a.EDW_UPD_DT_UTC >= b.CRE_DT
                QUALIFY RANK () OVER (PARTITION BY a.COP_NO, a.BKG_NO, a.EDW_UPD_DT_UTC ORDER BY b.CRE_DT DESC) = 1
            )
            SELECT *
            FROM ranked_report
        )
        """
        query = query.format(
            dataset_id=dataset_id,
            table_id=table_id,
            table_raw_id=table_raw_id,
            edw_upd_dt=edw_upd_dt_str,
        )
        query_job = self.client.query(query)
        query_job.result()  # Wait for the job to complete

        logger.info("Processed report raw data for EDW_UPD_DT: %s", edw_upd_dt_str)


if __name__ == "__main__":
    bq = BigQueryManager()
    query = """
    SELECT CNTR_NO, BKG_NO
    FROM `one-global-dilab-matchback-dev.REPORT.REPORT_GD`
    WHERE EDW_UPD_DT BETWEEN DATETIME("2024-09-01")
    AND DATETIME_ADD("2024-09-01", INTERVAL 1 DAY)
    LIMIT 100
    """
    results = bq.client.query(query).result()
    print(results.to_dataframe())
