"""A wrapper for the Google Drive client."""

from __future__ import annotations

import base64
import io
import json
import os
from pathlib import Path

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload, MediaIoBaseDownload

from archive.src.const.config import global_config as GlobalSettings  # noqa: N812
from archive.src.utils.exceptions import AuthenticationError
from archive.src.utils.file_utils import load_yaml_config
from archive.src.utils.logger import log_handler

logger = log_handler.get_logger(name="gcp")


class GoogleDriveManager:
    """A wrapper for the Google Drive client."""

    def __init__(self, chunksize: int = 5):
        """Initialize the GoogleDriveManager with service account credentials."""
        self.chunksize = chunksize
        self.config = load_yaml_config(GlobalSettings.GDRIVE_CONFIG)
        self.service = self.__authenticate_service_account()
        logger.info("Google Drive service initialized.")

    def __load_service_account(self) -> dict:
        """Load the service account info from the provided file."""
        msg = "SERVICE_ACCOUNT_ENCODE not found. Check eviroment variables."
        try:
            __base64_str = os.environ.get("SERVICE_ACCOUNT_ENCODE", "")
            if __base64_str == "":
                raise ValueError(msg)

            __decoded_bytes = base64.b64decode(__base64_str)
            __decoded_str = __decoded_bytes.decode("utf-8")
        except Exception as err:
            logger.error(msg)
            raise ValueError(msg) from err

        return json.loads(__decoded_str)

    def __authenticate_service_account(self):
        """Authenticate using the provided service account info and build the Drive service."""
        try:
            credentials = service_account.Credentials.from_service_account_info(
                self.__load_service_account(), scopes=self.config["scopes"]
            )
            return build(self.config["api_service_name"], self.config["api_version"], credentials=credentials)
        except Exception as err:
            msg = f"Failed to authenticate: {str(err)}"
            logger.error(msg)
            raise AuthenticationError(msg) from err

    def __execute_list_request(self, query: str, page_token: str | None, page_size: int) -> dict:
        """Execute a file list request to the Google Drive API.

        Args:
            query (str): The search query.
            page_token (Optional[str]): The token for the next page of results.
            page_size (int): The number of files to return per page.

        Returns:
            Dict: The API response containing the list of files.
        """
        return (
            self.service.files()
            .list(
                q=query,
                spaces="drive",
                fields="nextPageToken, files(id, name, modifiedTime, createdTime)",
                includeItemsFromAllDrives=True,
                supportsAllDrives=True,
                pageToken=page_token,
                pageSize=page_size,
                orderBy="createdTime desc",
            )
            .execute()
        )

    @staticmethod
    def __log_results(files: list[dict], parent_id: str, verbose: bool):
        """Log the results of a file list operation.

        Args:
            files (List[Dict]): The list of files to log.
            parent_id (str): The ID of the parent folder.
            verbose (bool): Whether to log detailed information about each file.
        """
        if verbose:
            for file in files:
                logger.info(
                    "Found file: %s: %s, created: %s, modified: %s",
                    file.get("id"),
                    file.get("name"),
                    file.get("createdTime"),
                    file.get("modifiedTime"),
                )
        logger.info("Found %d files in folder ID %s.", len(files), parent_id)

    def list_files(
        self, parent_id: str, page_size: int = 20, return_all: bool = False, verbose: bool = False
    ) -> list[dict]:
        """Search for files in a Google Drive folder.

        Args:
            parent_id (str): The ID of the parent folder to search in.
            page_size (int): The number of files to return per page.
            return_all (bool): Whether to return all files or just the first page.
            verbose (bool): Whether to log the files found.

        Returns:
            list[dict]: The list of files if the search was successful. Otherwise, an empty list is
                returned.
        """
        query = f"'{parent_id}' in parents and trashed=false"
        files = []
        page_token = None

        try:
            while True:
                results = self.__execute_list_request(query, page_token, page_size)
                new_files = results.get("files", [])
                files.extend(new_files)

                if not return_all or not (page_token := results.get("nextPageToken")):
                    break
                self.__log_results(files, parent_id, verbose)

            return files

        except HttpError as err:
            msg = f"An error occurred while searching for files. Error: {err}"
            logger.error(msg)
            return files

    def download_file(self, file_id: str, dest_fpath: Path):
        """Download a file from Google Drive.

        Args:
            file_id (str): The ID of the file to download.
            dest_fpath (Path): The path to save the downloaded file.
        """
        save_dir = dest_fpath.parent
        save_dir.mkdir(parents=True, exist_ok=True)

        try:
            request = self.service.files().get_media(fileId=file_id, supportsAllDrives=True)
            fh = io.FileIO(dest_fpath, "wb")
            downloader = MediaIoBaseDownload(fh, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk(num_retries=3)
                logger.info("Download %d%%.", int(status.progress() * 100))

            fh.close()
            logger.info("File has been downloaded to %s.", dest_fpath)

        except HttpError as error:
            logger.error(f"An error occurred while downloading the file: {error}")
            raise

        except OSError as error:
            logger.error(f"An error occurred while writing the file: {error}")
            raise

    def upload_file(self, file_path: Path, parent_ids: list[str]) -> dict | None:
        """Upload a file to Google Drive.

        Args:
            file_path (Path): The path to the file to upload.
            parent_ids (list[str]): The parent folder IDs to upload the file to.

        Returns:
            dict | None: The response from the Google Drive API if the upload was successful,
                otherwise None.
        """
        try:
            file_metadata = {"name": file_path.name, "parents": parent_ids}
            media = MediaFileUpload(
                filename=str(file_path),
                # mimetype="application/octet-stream",
                resumable=True,
                chunksize=self.chunksize * 1024 * 1024,
            )
            file = self.service.files().create(
                body=file_metadata, media_body=media, fields="id", supportsAllDrives=True
            )
            result = None
            while result is None:
                status, result = file.next_chunk()
                if status:
                    logger.info("Uploaded %d%%.", int(status.progress() * 100))

            logger.info("File %s has been uploaded. ID: %s.", result.get["name"], result.get("id"))
            return result

        except FileNotFoundError:
            logger.error("File not found: %s", file_path)
            raise

        except HttpError as error:
            logger.error(f"An error occurred while uploading the file: {error}")
            raise


if __name__ == "__main__":
    drive_manager = GoogleDriveManager()

    # List files in a folder
    folder_id = ""
    files = drive_manager.list_files(parent_id=folder_id, verbose=True)
