import time
from pathlib import Path

import pandas as pd

from archive.src.data_core import DataLoader
from archive.src.matching.recommend_module import ImportExportRouteMatcher
from archive.src.utils.logger import log_handler

logger = log_handler.get_logger(name="street_turn")


class StreetTurnMatcher:
    def __init__(self, data_loader: DataLoader):
        """Initializes the StreetTurnMatcher object and loads the necessary data."""
        self.data_loader = data_loader
        (
            self.loc_dict,
            self.cnt_size_map_dict,
            self.rl_import_data,
            self.rl_export_data,
            self.rpt_data,
            self.distance_data,
        ) = self._load_data()

        # Initialize the combined matcher and recommender
        self.matcher = ImportExportRouteMatcher(
            loc_dict=self.loc_dict,
            cnt_size_map_dict=self.cnt_size_map_dict,
            rl_import_data=self.rl_import_data,
            rl_export_data=self.rl_export_data,
            rpt_data=self.rpt_data,
            distance_data=self.distance_data,
        )

    def _load_data(self) -> tuple:
        """Loads the necessary data for matching and recommendation from the DataLoader."""
        location_dict: dict = self.data_loader.get_data("loc")
        cnt_size_map_dict: dict = self.data_loader.get_data("cnt_sz")
        ratelane_import_data, ratelane_export_data = self.data_loader.get_data("rl")
        report_data: pd.DataFrame = self.data_loader.get_data("rpt")
        distance_data = self.data_loader.get_data("dis")

        return (
            location_dict,
            cnt_size_map_dict,
            ratelane_import_data,
            ratelane_export_data,
            report_data,
            distance_data,
        )

    def print_summary(self, start_time, matched_count, pairs):
        """Prints the summary of matches and the total runtime."""
        raw_pairs_count = sum(len(inner_dict) for inner_dict in pairs.values())
        logger.info(
            f"Full matching runtime is {round(time.time() - start_time, 2)}s - {matched_count}/{raw_pairs_count} IMPORTS matched with length of MB report {len(self.rpt_data)}"
        )

    def run(self, save_dir: Path, specific_cy: list[str], criteria: str | None = None):
        """Executes the matching and recommendation process, saving results to the specified directory."""
        save_dir.mkdir(parents=True, exist_ok=True)

        start_time = time.time()
        matches_count = 0
        unique_loc_city = self.rpt_data["interchange_location"].unique()

        for loc in unique_loc_city:
            if specific_cy is not None and loc not in specific_cy:
                continue

            # Find pairs import and export, filter with constraint
            im_ex_pairs = self.matcher.find_matching_import_export(loc)
            if im_ex_pairs is None:
                continue

            raw_pairs, valid_pairs, loc, valid_cntz, valid_date = im_ex_pairs
            self.matcher.save_pair_matches(save_dir / f"matching_result_for_{loc}_raw_pairs", raw_pairs)
            self.matcher.save_pair_matches(save_dir / f"matching_result_for_{loc}_cntz_pairs", valid_cntz)
            self.matcher.save_pair_matches(save_dir / f"matching_result_for_{loc}_datetime_pairs", valid_date)
            self.matcher.save_pair_matches(save_dir / f"matching_result_for_{loc}_val_pairs", valid_pairs)

            logger.info(f"Number of raw pairs: {sum(len(inner_dict) for inner_dict in raw_pairs.values())}")
            logger.info(
                f"Number of valid pairs after [date, container_size] constrains: {sum(len(inner_dict) for inner_dict in valid_pairs.values())}"
            )

            valid_pairs, val_distance_pairs_info, metadata_rl = self.matcher.match_with_rate_lane(
                loc, valid_pairs, thres=1
            )

            self.matcher.save_pair_matches(save_dir / f"matching_result_for_{loc}_distance_pairs", valid_pairs)
            logger.info(
                f"Number of valid pairs after distance constrains: {sum(len(inner_dict) for inner_dict in valid_pairs.values())}"
            )

            # save rl meta-data
            output_file_path = f"{save_dir}/{loc}_metadata_storage_single_file.pickle"
            self.matcher.save_metadata_storage(metadata_rl, output_file_path, file_format="pickle")

            # Run LP solver
            lp_results, details_distance_pairs, val_distance_pairs_info = self.matcher.lp_solver(
                save_dir, valid_pairs, val_distance_pairs_info, loc, n_ex=1, n_im=1, criteria=criteria, save_model=True
            )

            self.matcher.save_detailed_pair_matches(
                save_dir / f"matching_result_for_{loc}_distance_info", val_distance_pairs_info
            )
            if lp_results is None:
                continue

            self.matcher.save_matches(save_dir / f"lp_result_for_{loc}_after_constraints", matches=lp_results)
            self.matcher.save_pair_matches(
                save_dir / f"matching_result_for_{loc}_doors_distance",
                details_distance_pairs,
            )
            for im, export_dict in valid_pairs.items():
                im_index = int(im.split("_")[1])
                for ex, val in export_dict.items():  # noqa: B007
                    ex_index = int(ex.split("_")[1])
                    if lp_results[im][ex].varValue == 1:
                        self.matcher.add_recommended_route(im_index, ex_index)
                        matches_count += 1

            # Print final runtime and save results
            self.print_summary(start_time, matches_count, raw_pairs)

            # Save the final route recommendations
            self.matcher.save_results_to_csv(save_dir / "route_recommendation.csv")

        logger.info("Run successfully!")
