import json
import pickle
import time
from pathlib import Path

import pandas as pd
from rich.progress import Progress

from archive.src.models.linear.main import LinearSolver
from archive.src.utils.logger import log_handler
from archive.src.utils.preprocess import (
    find_key_by_location_name,
    get_distance_between_locations,
    get_distance_in_meter,
    get_distance_saved_v2,
    get_total_distance_v2,
    log_scale,
)

logger = log_handler.get_logger(name="route_recommender")

pd.options.mode.chained_assignment = None


class ImportExportRouteMatcher:
    def __init__(
        self,
        loc_dict: dict,
        cnt_size_map_dict: dict,
        rpt_data: pd.DataFrame,
        distance_data: pd.DataFrame,
        rl_import_data: pd.DataFrame = None,
        rl_export_data: pd.DataFrame = None,
    ):
        """Initializes the ImportExportRouteMatcher object.

        Args:
            loc_dict (dict): Location dictionary.
            cnt_size_map_dict (dict): Container size mapping dictionary.
            rpt_data (pd.DataFrame): Report data.
            distance_data (pd.DataFrame): Distance data.
            rl_import_data (pd.DataFrame): Rate lane import data (optional).
            rl_export_data (pd.DataFrame): Rate lane export data (optional).
        """
        self.loc_dict = loc_dict
        self.cnt_size_map_dict = cnt_size_map_dict
        self.rpt_data = rpt_data
        self.distance_data = distance_data
        self.rl_import_data = rl_import_data
        self.rl_export_data = rl_export_data
        self.rec_data: dict[str, str] = {}

        self.linear_solver = LinearSolver()

        self.distance_data_ap = self.distance_data[self.distance_data["category"] == "all"]
        self.distance_data_cp = self.distance_data[self.distance_data["category"] == "all"]
        self.distance_data_aa = self.distance_data[self.distance_data["category"] == "all"]
        self.distance_data_cc = self.distance_data[self.distance_data["category"] == "all"]

        self.df_recommended_routes = pd.DataFrame(
            columns=[
                "Import Unique ID",
                "Export Unique ID",
                "Shipline",
                "Import Shipper",
                "Trucker",
                "Container Number",
                "Size Type",
                "Import Door City",
                "Import Door State",
                "Import Port Ramp",
                "Export Door City",
                "Export Door State",
                "Export Port Ramp",
                "Export Shipper",
                "Import Available Date",
                "Import Expire Date",
                "Export Available Date",
                "Export Expire Date",
                "Overlaps",
                "Overlap Difference Days",
                "Distance Between Import and Export (km)",
                "Street Turn Distance (km)",
                "Round Trip Distance (km)",
                "Distance Saved (km)",
            ]
        )

    def find_matching_import_export(self, loc: str):
        """Find matching imports and exports based on location and city codes, with container size and date compatibility checks."""
        logger.info(f"Matching imports and exports for CY {loc}")
        same_cy = self.rpt_data.query(f"`interchange_location`.str.match('^{loc}.*')", engine="python")

        im_candi = same_cy[same_cy["bound"] == "IMPORT"]
        exp_candi = same_cy[same_cy["bound"] == "EXPORT"]

        if len(im_candi) == 0 or len(exp_candi) == 0:
            logger.info(f"CY {loc} can't match because import = {len(im_candi)} and export = {len(exp_candi)}")
            return

        # Generate location city codes for imports and exports
        im_candi["loc_city_cd"] = im_candi["location_city"].apply(lambda x: find_key_by_location_name(x, self.loc_dict))
        exp_candi["loc_city_cd"] = exp_candi["location_city"].apply(
            lambda x: find_key_by_location_name(x, self.loc_dict)
        )

        # Initialize dictionaries
        raw_pairs: dict[str, dict[str, int]] = {}
        valid_cntz_sz_pairs: dict[str, dict[str, int]] = {}
        valid_datetime_pairs: dict[str, dict[str, int]] = {}
        valid_pairs: dict[str, dict[str, int]] = {}

        # Populate raw_pairs, valid_cntz_sz_pairs, and valid_datetime_pairs
        for im in im_candi.itertuples():
            im_loc = f"{im.loc_city_cd}_{im.Index}_{im.cop_no}_{im.booking_no}"
            raw_pairs[im_loc] = {}
            valid_cntz_sz_pairs[im_loc] = {}
            valid_datetime_pairs[im_loc] = {}
            im_index = im.Index

            for ex in exp_candi.itertuples():
                ex_loc = f"{ex.loc_city_cd}_{ex.Index}_{ex.cop_no}_{ex.booking_no}"
                ex_index = ex.Index

                raw_pairs[im_loc][ex_loc] = 0  # Default to 0

                # Check container size compatibility
                if self.is_container_size_compatible(im_index, ex_index):
                    valid_cntz_sz_pairs[im_loc][ex_loc] = 1
                else:
                    valid_cntz_sz_pairs[im_loc][ex_loc] = 0

                # Check date compatibility
                if self.is_date_compatible(im_index, ex_index):
                    valid_datetime_pairs[im_loc][ex_loc] = 1
                else:
                    valid_datetime_pairs[im_loc][ex_loc] = 0

        # Combine valid_cntz_sz_pairs and valid_datetime_pairs into valid_pairs
        for key in valid_cntz_sz_pairs.keys():
            valid_pairs[key] = {
                sub_key: 1
                for sub_key in valid_cntz_sz_pairs[key]
                if valid_cntz_sz_pairs[key][sub_key] == 1 and valid_datetime_pairs[key][sub_key] == 1
            }

        logger.info(f"Number of imports: {len(im_candi)} - exports: {len(exp_candi)}")
        return raw_pairs, valid_pairs, loc, valid_cntz_sz_pairs, valid_datetime_pairs

    def lp_solver(self, save_dir, valid_pairs, val_distance_pairs_info, loc, n_ex, n_im, criteria, save_model=False):
        """Call the linear solver to solve the LP problem."""
        # Calculate s()avings only for compatible imports and exports
        tik = time.time()
        savings, distance_pairs, distance_pairs_info = self.calculate_savings_matrix(
            valid_pairs, val_distance_pairs_info, criteria
        )
        logger.info(f"Calculated saving matrix with {round(time.time() - tik, 2)}s")

        # Solve linear programming problem if there are any valid savings
        if savings:
            x = self.linear_solver.solve(
                save_dir,
                valid_pairs,
                savings=savings,
                n_ex=n_ex,
                n_im=n_im,
                loc=loc,
                save_model=save_model,
            )
            matches = x
            return matches, distance_pairs, distance_pairs_info
        else:
            logger.info(f"No compatible matches found for location {loc}.")
            return None, distance_pairs, distance_pairs_info

    def calculate_savings_matrix(self, valid_paris, val_distance_pairs_info, criteria):
        """Calculate the savings based on preloaded distance data from BigQuery.

        Args:
            valid_paris (dict): Dictionary with imports as keys and dict of exports as values.
                                Format: {'im1': {'ex1': 1, 'ex2': 1, ...}, 'im2': {'ex1': 1, ...}, ...}
            val_distance_pairs_info (dict): __description__
            criteria (str): Criteria for calculating savings.

        Returns:
            dict: Dictionary with key as (import, export) pair and value as the calculated savings.
        """
        if self.distance_data is None:
            raise ValueError("Distance data not loaded. Ensure fetch_data() and process_data() are called.")

        savings = {}
        distance_pairs = {}

        for im_loc, export_dict in valid_paris.items():
            im_index = int(im_loc.split("_")[1])
            distance_pairs[im_loc] = {}
            for ex_loc, is_valid in export_dict.items():
                if not is_valid:  # Skip invalid pairs
                    continue

                ex_index = int(ex_loc.split("_")[1])

                im_name_street = self.rpt_data.iloc[im_index]["street_address"]
                im_name_city = self.loc_dict[im_loc.split("_")[0]]["location_name"]
                ex_name_street = self.rpt_data.iloc[ex_index]["street_address"]
                ex_name_city = self.loc_dict[ex_loc.split("_")[0]]["location_name"]

                distance = self._get_distance(im_name_street, ex_name_street, im_name_city, ex_name_city)
                self.rec_data[f"{im_index}-{ex_index}"].append(float(distance) / 1000)
                if distance == -1:
                    logger.info(
                        f"No distance for pair: {im_name_street}--{ex_name_street} and {im_name_city}--{ex_name_city}"
                    )
                    continue
                if int(distance) > 643737.6:  # 643737.6
                    logger.info(
                        f"Distance between IMPORT and EXPORT = {int(distance)} > 643.7376 km(400 miles): {im_name_street}--{ex_name_street} and {im_name_city}--{ex_name_city}"
                    )
                    val_distance_pairs_info[im_loc][ex_loc]["is_pass"] = False
                    val_distance_pairs_info[im_loc][ex_loc]["door_distance"] = float(distance)
                    continue

                distance_pairs[im_loc][ex_loc] = float(distance)
                val_distance_pairs_info[im_loc][ex_loc]["door_distance"] = float(distance)

                if criteria == "distance":
                    # savings[(im_loc, ex_loc)] = distance
                    savings[(im_loc, ex_loc)] = self.rec_data[f"{im_index}-{ex_index}"][0]
                elif criteria == "distance_cost":
                    savings[(im_loc, ex_loc)] = log_scale(distance) + log_scale(
                        self.rec_data[f"{im_index}-{ex_index}"][2]
                    )
                elif criteria == "cost":
                    savings[(im_loc, ex_loc)] = self.rec_data[f"{im_index}-{ex_index}"][2]

        return savings, distance_pairs, val_distance_pairs_info

    def _get_distance(self, im_name_street, ex_name_street, im_name_city, ex_name_city):
        """Helper function to calculate distance based on location details.

        Args:
            im_name_street (str): Street address of the import location.
            ex_name_street (str): Street address of the export location.
            im_name_city (str): City name of the import location.
            ex_name_city (str): City name of the export location.

        Returns:
            float: Calculated distance or -1 if not found.
        """
        distance = get_distance_between_locations(im_name_street, ex_name_street, self.distance_data_aa, label="all")
        if distance == -1:
            distance = get_distance_between_locations(im_name_street, ex_name_city, self.distance_data_ap, label="all")
        if distance == -1:
            distance = get_distance_between_locations(ex_name_street, im_name_city, self.distance_data_ap, label="all")
        if distance == -1:
            distance = get_distance_between_locations(im_name_city, ex_name_city, self.distance_data_cc, label="all")
        if distance == -1:
            # Fallback to external API
            full_im_name = f"{im_name_street or ''}, {im_name_city}"
            full_ex_name = f"{ex_name_street or ''}, {ex_name_city}"
            distance = get_distance_in_meter(full_im_name, full_ex_name, label="all")

        return distance

    def match_with_rate_lane(self, loc: str, val_pairs: dict[str, dict[str, int]], thres: int = 10):
        """Match imports and exports with rate lane and apply thresholds.

        Modify the input dictionary `val_pairs` to retain only valid import-export pairs,
        along with additional details for each pair.

        Args:
            loc (str): Location identifier.
            val_pairs (dict[str, dict[str, int]]): Dictionary of import-export pairs.
            thres (int): Maximum number of matches allowed per import.

        Returns:
            tuple:
                - dict[str, dict[str, dict[str, Any]]]: Modified dictionary with only valid pairs and additional details.
                - dict[str, dict[str, pd.DataFrame]]: Dictionary storing metadata (im_rl and ex_rl) for each import-export pair.
        """
        total_tasks = sum(len(exports) for exports in val_pairs.values())
        detailed_pairs: dict[str, dict[str, dict[str, float | bool]]] = {}
        metadata_rl: dict[str, dict[str, dict[str, pd.DataFrame]]] = {}  # Metadata dictionary

        with Progress() as progress:
            task = progress.add_task(f"[green]Processing matches for {loc} ...", total=total_tasks)

            for im in list(val_pairs.keys()):
                im_cop_bk = f"{im.split('_')[-1]} - {im.split('_')[-2]}"
                detailed_pairs[im] = {}
                metadata_rl[im_cop_bk] = {}  # Initialize the metadata dictionary for this import

                for ex in list(val_pairs[im].keys()):
                    ex_cop_bk = f"{ex.split('_')[-1]} - {ex.split('_')[-2]}"
                    progress.advance(task)

                    # Call the matching function
                    (
                        is_pass,
                        round_trip_distance,
                        street_turn_distance,
                        distance_saved,
                        im_rl,
                        ex_rl,
                        im_rl_picked,
                        ex_rl_picked,
                    ) = self.process_matching_pair(im, ex)

                    # Store details for each import-export pair
                    detailed_pairs[im][ex] = {
                        "is_pass": is_pass,
                        "round_trip_distance": round_trip_distance,
                        "street_turn_distance": street_turn_distance,
                        "distance_saved": distance_saved,
                    }

                    # Store the metadata for this import-export pair
                    metadata_rl[im_cop_bk][ex_cop_bk] = {
                        "im_rl": im_rl,
                        "ex_rl": ex_rl,
                        "im_rl_picked": im_rl_picked,
                        "ex_rl_picked": ex_rl_picked,
                    }

                    # Remove invalid pairs from the original dictionary
                    if not is_pass:
                        val_pairs[im].pop(ex)

                # Remove the import if it has no valid exports
                if not val_pairs[im]:
                    val_pairs.pop(im)

            matched_count = sum(len(exports) for exports in val_pairs.values())
            logger.info(f"Total matched pairs: {matched_count}")

            # Return the modified detailed dictionary and metadata storage
            return val_pairs, detailed_pairs, metadata_rl

    def process_matching_pair(self, im, ex):
        """Processes a matching pair, ensuring constraints are met before adding to recommendations."""
        im_loc, im_index = im.split("_")[0], int(im.split("_")[1])
        ex_loc, ex_index = ex.split("_")[0], int(ex.split("_")[1])

        # Match with rate lane data
        im_rate_lane, ex_rate_lane = self.find_rate_lane_candidates(im_index, ex_index, im_loc, ex_loc)
        if len(im_rate_lane) == 0 or len(ex_rate_lane) == 0:
            return False, 0, 0, 0, None, None, None, None

        im_address = (
            self.loc_dict[im_loc]["location_name"]
            if pd.isna(self.rpt_data.iloc[im_index]["street_address"])
            else self.rpt_data.iloc[im_index]["street_address"]
        )
        ex_address = (
            self.loc_dict[ex_loc]["location_name"]
            if pd.isna(self.rpt_data.iloc[ex_index]["street_address"])
            else self.rpt_data.iloc[ex_index]["street_address"]
        )

        ex_rate_lane["total_distance"] = ex_rate_lane.apply(
            lambda x: get_total_distance_v2(
                x,
                self.loc_dict,
                self.distance_data_ap,
                self.distance_data_cp,
                im_address,
                ex_address,
                bound="ex",
            ),
            axis=1,
        )
        im_rate_lane["total_distance"] = im_rate_lane.apply(
            lambda x: get_total_distance_v2(
                x,
                self.loc_dict,
                self.distance_data_ap,
                self.distance_data_cp,
                im_address,
                ex_address,
                bound="im",
            ),
            axis=1,
        )
        ex_rate_lane["total_distance"] = pd.to_numeric(ex_rate_lane["total_distance"], errors="coerce")
        im_rate_lane["total_distance"] = pd.to_numeric(im_rate_lane["total_distance"], errors="coerce")

        # Calculate and compare distances for street-turns
        (
            is_pass,
            round_trip_distance,
            street_turn_distance,
            self.distance_saved,
            im_rl,
            ex_rl,
            im_rl_picked,
            ex_rl_picked,
        ) = self.is_street_turn_beneficial(im_rate_lane, ex_rate_lane, im_address=im_address, ex_address=ex_address)
        # if not is_pass:
        #     #     return False, round_trip_distance, street_turn_distance, self.distance_saved
        #     self.ignore_same_pair.append(f"{im_address}-{ex_address}")

        # save calculated data for distance saved, ranking of import and export
        self.rec_data[f"{im_index}-{ex_index}"] = [
            float(self.distance_saved) / 1000,  # to km
            int(self.rank_1st_im["base_rate"].values[0]),
            int(self.rank_1st_ex["base_rate"].values[0]),
            float(round_trip_distance) / 1000,
            float(street_turn_distance) / 1000,
        ]
        return (
            is_pass,
            round_trip_distance,
            street_turn_distance,
            self.distance_saved,
            im_rl,
            ex_rl,
            im_rl_picked,
            ex_rl_picked,
        )

    def add_recommended_route(self, im_index, ex_index):
        """Adds the matching import-export pair to the recommended routes dataframe."""
        new_row = {
            "Import Unique ID": f"{self.rpt_data.iloc[im_index]['booking_no']} - {self.rpt_data.iloc[im_index]['cop_no']}",
            "Export Unique ID": f"{self.rpt_data.iloc[ex_index]['booking_no']} - {self.rpt_data.iloc[ex_index]['cop_no']}",
            "Shipline": "ONE",
            "Import Shipper": self.rpt_data.iloc[im_index]["shipper__consignee_name"],
            "Trucker": self.rpt_data.iloc[ex_index]["truck_company"],
            "Container Number": self.rpt_data.iloc[im_index]["container_no"],
            "Size Type": self.cnt_size_map_dict[self.rpt_data.iloc[im_index]["container_size_type"]],
            "Import Door City": self.rpt_data.iloc[im_index]["location_city"],
            "Import Door State": self.rpt_data.iloc[im_index]["location_state"],
            "Import Port Ramp": self.rpt_data.iloc[im_index]["interchange_location"],
            "Export Door City": self.rpt_data.iloc[ex_index]["location_city"],
            "Export Door State": self.rpt_data.iloc[ex_index]["location_state"],
            "Export Port Ramp": self.rpt_data.iloc[ex_index]["interchange_location"],
            "Export Shipper": self.rpt_data.iloc[ex_index]["shipper__consignee_name"],
            "Import Available Date": self.rpt_data.iloc[im_index]["import_availability_at_final_cy"],
            "Import Expire Date": self.rpt_data.iloc[im_index]["estimated_import_delivery_date"],
            "Export Available Date": self.rpt_data.iloc[ex_index]["export_first_receiving_date"],
            "Export Expire Date": self.rpt_data.iloc[ex_index]["export_cut_off_date"],
            "Overlaps": "True",
            "Overlap Difference Days": (
                self.rpt_data.iloc[ex_index]["export_first_receiving_date"]
                - self.rpt_data.iloc[im_index]["estimated_import_delivery_date"]
            ).days,
            "Distance Between Import and Export (km)": self.rec_data[f"{im_index}-{ex_index}"][5],
            "Street Turn Distance (km)": self.rec_data[f"{im_index}-{ex_index}"][4],
            "Round Trip Distance (km)": self.rec_data[f"{im_index}-{ex_index}"][3],
            "Distance Saved (km)": self.rec_data[f"{im_index}-{ex_index}"][0],
        }

        self.df_recommended_routes = pd.concat([self.df_recommended_routes, pd.DataFrame([new_row])], ignore_index=True)

    def is_container_size_compatible(self, im_index, ex_index):
        """Checks if container sizes between import and export are compatible."""
        import_size = self.rpt_data.loc[im_index, "container_size_type"]
        export_size = self.rpt_data.loc[ex_index, "container_size_type"]

        return import_size == export_size or (import_size in {"D4", "D5"} and export_size == "X45")

    def is_date_compatible(self, im_index, ex_index):
        """Checks if the dates between import and export are compatible for matching."""
        export_cutoff = self.rpt_data.loc[ex_index, "export_cut_off_date"]
        estimated_import_delivery = self.rpt_data.loc[im_index, "estimated_import_delivery_date"]
        export_first_receiving = self.rpt_data.loc[ex_index, "export_first_receiving_date"]
        if pd.notna(export_cutoff) and pd.notna(estimated_import_delivery) and pd.notna(export_first_receiving):
            return export_cutoff > estimated_import_delivery >= export_first_receiving
        return False

    def find_rate_lane_candidates(self, im_index, ex_index, im_loc, ex_loc):
        """Finds rate lane candidates for both import and export."""
        im_inter = self.rpt_data.loc[im_index, "interchange_location"]
        ex_inter = self.rpt_data.loc[ex_index, "interchange_location"]

        ex_rate_lane = self.rl_export_data[
            (self.rl_export_data["origin_location_code"] == ex_loc)
            & (
                (self.rl_export_data["destination_location_code"] == ex_inter)
                | (self.rl_export_data["destination_location_code"] == ex_inter[:5])
            )
            & (
                (
                    self.rl_export_data["equipment_size_type"]
                    == self.cnt_size_map_dict[self.rpt_data.loc[ex_index, "container_size_type"]]
                )
                | (self.rl_export_data["equipment_size_type"].isnull())
            )
        ]

        im_rate_lane = self.rl_import_data[
            (self.rl_import_data["destination_location_code"] == im_loc)
            & (
                (self.rl_import_data["origin_location_code"] == im_inter)
                | (self.rl_import_data["origin_location_code"] == im_inter[:5])
            )
            & (
                (
                    self.rl_import_data["equipment_size_type"]
                    == self.cnt_size_map_dict[self.rpt_data.loc[im_index, "container_size_type"]]
                )
                | (self.rl_import_data["equipment_size_type"].isnull())
            )
        ]

        return im_rate_lane, ex_rate_lane

    def is_street_turn_beneficial(self, im_rate_lane, ex_rate_lane, im_address, ex_address):
        """Checks if the street-turn is more beneficial than the normal flow."""
        # Extracting the third position from LaneDescription for filtering
        im_rate_lane["third_position"] = im_rate_lane["lane_description"].str.split("_").str[2]
        ex_rate_lane["third_position"] = ex_rate_lane["lane_description"].str.split("_").str[2]

        # Find the common third position between im_rate_lane and ex_rate_lane
        common_positions = set(im_rate_lane["third_position"]).intersection(ex_rate_lane["third_position"])

        if not common_positions:
            raise ValueError("No common third position found between im_rate_lane and ex_rate_lane.")

        # Filter both dataframes to include only rows with the common third position
        im_filtered = im_rate_lane[im_rate_lane["third_position"].isin(common_positions)]
        ex_filtered = ex_rate_lane[ex_rate_lane["third_position"].isin(common_positions)]

        # Choose rows with the smallest base_rate
        ###TODO:
        # can choose many rate lanes

        self.rank_1st_im = im_filtered.nsmallest(1, ["base_rate"])
        self.rank_1st_ex = ex_filtered.nsmallest(1, ["base_rate"])

        # Calculate street-turn distance
        street_turn_distance = get_distance_saved_v2(
            importland=self.rank_1st_im["lane_description"].values[0],
            exportland=self.rank_1st_ex["lane_description"].values[0],
            location_dict=self.loc_dict,
            distance_ap=self.distance_data_ap,
            distance_cp=self.distance_data_cp,
            distance_aa=self.distance_data_aa,
            im_address=im_address,
            ex_address=ex_address,
        )

        # Calculate round-trip distance
        round_trip_distance = (
            self.rank_1st_im["total_distance"].values[0] + self.rank_1st_ex["total_distance"].values[0]
        )

        # Calculate distance saved
        self.distance_saved = round_trip_distance - street_turn_distance

        # Determine if the street-turn is beneficial
        # is_pass = street_turn_distance < round_trip_distance and street_turn_distance < 400
        # is_pass = street_turn_distance < 400
        # is_pass = street_turn_distance + 10000 < round_trip_distance
        is_pass = True
        return (
            is_pass,
            round_trip_distance,
            street_turn_distance,
            self.distance_saved,
            im_filtered,
            ex_filtered,
            self.rank_1st_im,
            self.rank_1st_ex,
        )

    def save_pair_matches(self, output_file_prefix: Path, pairs: dict[str, dict[str, int]]):
        """_summary_."""
        # save pairs, matches to a file
        with open(f"{output_file_prefix}.txt", "w") as f:
            for key, value in pairs.items():
                f.write(f"'{key}': {value}\n")

    def save_detailed_pair_matches(
        self,
        output_file_prefix: Path,
        detailed_pairs: dict[str, dict[str, dict[str, float | bool]]],
    ):
        """Save detailed pairs with additional information to a log file.

        Args:
            output_file_prefix (Path): Path prefix for the output file.
            detailed_pairs (dict): Dictionary containing detailed import-export pairs and their metadata.
        """
        with open(f"{output_file_prefix}.txt", "w") as f:
            for im, exports in detailed_pairs.items():
                # Use 'exports' directly since it is already a dictionary
                f.write(f"'{im}': {exports}\n")

    def save_matches(self, output_file_prefix: Path, matches: dict[str, dict]):
        """_summary_."""
        tmp = {}
        for k, v in matches.items():
            tmp[k] = {kk: vv.to_dict() for kk, vv in v.items()}
        with open(f"{output_file_prefix}_matches.json", "w") as f:
            json.dump(tmp, f)

    def save_results_to_csv(self, file_path: Path):
        """Saves the recommended routes to a CSV file."""
        self.df_recommended_routes.to_csv(file_path, index=False)

    def save_metadata_storage(self, metadata_storage, output_file_path, file_format="pickle"):
        """Save the entire metadata_storage to a single file.

        Args:
            metadata_storage (dict): The metadata storage dictionary.
            output_file_path (str): Path to the output file.
            file_format (str): Format to save the file ('pickle' or 'parquet').
        """
        if file_format == "pickle":
            with open(output_file_path, "wb") as file:
                pickle.dump(metadata_storage, file)
            print(f"Metadata storage saved as Pickle file at: {output_file_path}")

        elif file_format == "parquet":
            # Convert the metadata into a single Pandas DataFrame
            data_list = []
            for key, value in metadata_storage.items():
                for export_key, metadata in value.items():
                    row = {
                        "import_key": key,
                        "export_key": export_key,
                        "im_rl": metadata["im_rl"].to_dict()
                        if isinstance(metadata["im_rl"], pd.DataFrame)
                        else metadata["im_rl"],
                        "ex_rl": metadata["ex_rl"].to_dict()
                        if isinstance(metadata["ex_rl"], pd.DataFrame)
                        else metadata["ex_rl"],
                        "im_rl_picked": metadata["im_rl_picked"].to_dict()
                        if isinstance(metadata["im_rl_picked"], pd.DataFrame)
                        else metadata["im_rl_picked"],
                        "ex_rl_picked": metadata["ex_rl_picked"].to_dict()
                        if isinstance(metadata["ex_rl_picked"], pd.DataFrame)
                        else metadata["ex_rl_picked"],
                        "other_metadata": {k: v for k, v in metadata.items() if k not in ["im_rl", "ex_rl"]},
                    }
                    data_list.append(row)

            df = pd.DataFrame(data_list)
            df.to_parquet(output_file_path, index=False)
            print(f"Metadata storage saved as Parquet file at: {output_file_path}")

        else:
            raise ValueError("Unsupported file format. Use 'pickle' or 'parquet'.")
