LOC_MAP = {
    "location_code": "LOC_CD",
    "location_name": "LOC_NM",
    "region_code": "RGN_CD",
    "yard_code": "MTY_PKUP_YD_CD",
    "latitude": "NEW_LOC_LAT",
    "longitude": "NEW_LOC_LON",
}

YARD_MAP = {
    "location_code": "LOC_CD",
    "yard_code": "YD_CD",
    "yard_name": "YD_NM",
    "yard_address": "YD_ADDR",
    "zip_code": "ZIP_CD",
    "yard_latitude": "YD_LAT",
    "yard_longitude": "YD_LON",
}

CONTAINER_SIZE_MAP = {"sizetype": "sizetype", "mapsize": "mapsize"}

RATELANE_MAP = {
    "lane_description": "LaneDescription",
    "origin_location_code": "OriginLocationCode",
    "origin_location_name": "OriginLocationName",
    "destination_location_code": "DestinationLocationCode",
    "destination_location_name": "DestinationLocationName",
    "via_point1": "ViaPoint1",
    "base_rate": "BaseRate",
    "effective_date": "EffectiveDate",
    "expiry_date": "ExpiryDate",
    "equipment_size_type": "EquipmentSizeType",
    "trip_type": "TripType",
    "vendor_code": "VendorCode",
    "carrier_code": "CarrierCode",
    "comments": "Comments",
}

REPORT_MAP = {
    "bound": "BOUND",
    "reuse": "REUSE",
    "shipping_line": "SHIPPING_LINE",
    "truck_company_sequence": "TRUCK_COMPANY_SEQUENCE",
    "truck_company": "TRUCK_COMPANY",
    "cop_no": "COP_NO",
    "booking_no": "BKG_NO",
    "container_no": "CNTR_NO",
    "shipper__consignee_name": "SHIPPER__CONSIGNEE_NAME",
    "container_qty": "CNTR_QTY",
    "container_size_type": "CNTR_SIZE_TYPE",
    "location_city": "LOCATION_CITY",
    "location_state": "LOCATION_STATE",
    "location_zip": "LOCATION_ZIP",
    "location_country": "LOCATION_COUNTRY",
    "interchange_location": "INTERCHANGE_LOCATION",
    "interchange_location_city": "INTERCHANGE_LOCATION_CITY",
    "transport_mode": "TRANSPORT_MODE",
    "import_availability_at_final_cy": "IMPORT_AVAILABILITY_AT_FINAL_CY",
    "estimated_import_delivery_date": "ESTIMATED_IMPORT_DELIVERY_DATE",
    "export_first_receiving_date": "EXPORT_FIRST_RECEIVING_DATE",
    "export_cut_off_date": "EXPORT_CUT_OFF_DATE",
    "hazmat": "HAZMAT",
    "container_tare_weight_lbs": "CNTR_TARE_WEIGHT_LBS",
    "estimate_cargo_weight_lbs": "ESTIMATE_CARGO_WEIGHT_LBS",
    "flex_height": "FLEX_HEIGHT",
    "control_office": "CONTROL_OFFICE",
    "so_creator": "SO_CREATOR",
    "so_create_date": "SO_CREATE_DATE",
    "service_order_no": "SERVICE_ORDER_NO",
    "work_order_no": "WORK_ORDER_NO",
    "first_port_of_load_location": "FIRST_PORT_OF_LOAD_LOCATION",
    "first_port_of_load_cutoff_date": "FIRST_PORT_OF_LOAD_CUTOFF_DATE",
    "last_port_of_discharge_location": "LAST_PORT_OF_DISCHARGE_LOCATION",
    "drop_and_pick": "DROP_AND_PICK",
    "foc_cleared_status": "FOC_CLEARED_STATUS",
    "customer_nominated_trucker": "CUSTOMER_NOMINATED_TRUCKER",
    "commodity": "COMMODITY",
    "trucking_company_scac": "TRUCKING_COMPANY_SCAC",
    "pol_pod": "POL_POD",
    "inland_transport_mode": "INLAND_TRANSPORT_MODE",
    "rail_scac": "RAIL_SCAC",
    "door_cy": "DOOR_CY",
    "stcc_code": "STCC_CODE",
    "street_address": "STREET_ADDRESS",
    "cre_dt": "CRE_DT",
    "upd_dt": "UPD_DT",
    "del_dt": "DEL_DT",
    "edw_upd_dt": "EDW_UPD_DT",
    "edw_upd_dt_utc": "EDW_UPD_DT_UTC",
}
