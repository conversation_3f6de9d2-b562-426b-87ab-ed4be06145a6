from archive.src.data_core.base_loader import DataLoaderBase
from archive.src.data_core.col_map import CONTAINER_SIZE_MAP
from archive.src.thirdparty.gcp import BigQueryManager
from archive.src.utils.logger import log_handler
from root import PROJECT_ID

logger = log_handler.get_logger(name="data_loader")


class ContainerSizeDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the ContainerSizeDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self._cnt_data = None
        self.cnt_size_map_dict = None
        self.column_mappings = {"size_mapping": CONTAINER_SIZE_MAP}

    def fetch_data(self, data_source: str) -> None:
        """Fetch location and yard data from BigQuery.

        Args:
            data_source (str): The source data location.
        """
        query = """
        SELECT *
        FROM `{data_source}.view_RATE_LANE_MAP`
        """
        query = query.format(data_source=data_source)

        self._cnt_data = self.bq_manager.execute_query(query)

    def process_data(self) -> None:
        """Process the fetched data and create the location dictionary."""
        if self._cnt_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self.cnt_size_map_dict = self._cnt_data.set_index("sizetype")["mapsize"].to_dict()

        additional_mappings = {"X45": "40HC", "B4": "B4", "O5": "50OT"}
        self.cnt_size_map_dict.update(additional_mappings)

        logger.info("Container size mapping loaded successfully.")

    def get_data(self) -> dict:
        """Return the container size mapping dictionary.

        Returns:
            dict: A dictionary mapping size types to map sizes
        """
        if self.cnt_size_map_dict is None:
            raise ValueError("Size mapping not loaded. Call load_size_mapping() first.")
        return self.cnt_size_map_dict


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = ContainerSizeDataLoader(bq_manager)
    loader.fetch_data(f"{PROJECT_ID}.RATE_LANE")
    loader.process_data()
    cnt_size_map_dict = loader.get_data()
