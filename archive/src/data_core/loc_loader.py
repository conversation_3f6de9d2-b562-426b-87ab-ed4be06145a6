from archive.src.thirdparty.gcp import BigQueryManager
from archive.src.utils.logger import log_handler
from root import PROJECT_ID

from .base_loader import DataLoaderBase
from .col_map import LOC_MAP, YARD_MAP

logger = log_handler.get_logger(name="data_loader")


class LocationDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the LocationDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self._location_data = None
        self._yard_data = None
        self.location_dict = None
        self.column_mappings = {"location": LOC_MAP, "yard": YARD_MAP}

    def fetch_data(self, data_source: str) -> None:
        """Fetch location and yard data from BigQuery.

        Args:
            data_source (str): The source data location.
        """
        location_query = """
        SELECT DISTINCT {col}
        FROM `{data_source}.DWC_LOCATION`
        """
        location_query = location_query.format(col=self._generate_select_clause("location"), data_source=data_source)

        yard_query = """
        SELECT {col}
        FROM `{data_source}.DWC_YARD`
        """
        yard_query = yard_query.format(col=self._generate_select_clause("yard"), data_source=data_source)

        self._location_data = self.bq_manager.execute_query(location_query)
        self._yard_data = self.bq_manager.execute_query(yard_query)
        self._convert_to_float(self._location_data, ["latitude", "longitude"])
        self._convert_to_float(self._yard_data, ["yard_latitude", "yard_longitude"])

    def process_data(self) -> None:
        """Process the fetched data and create the location dictionary."""
        if self._location_data is None or self._yard_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self.location_dict = self._location_data.set_index("location_code").to_dict(orient="index")

        yard_groups = self._yard_data.groupby("location_code")
        for loc_code, yards in yard_groups:
            yard_list = yards[["yard_code", "yard_latitude", "yard_longitude"]].to_dict(orient="records")
            if loc_code in self.location_dict:
                self.location_dict[loc_code]["yards"] = yard_list
            else:
                self.location_dict[loc_code] = {
                    "location_name": None,
                    "region_code": None,
                    "latitude": None,
                    "longitude": None,
                    "yards": yard_list,
                }
        # Ensure that all locations have a "yards" key. TODO: may not be necessary
        for _, v in self.location_dict.items():  # noqa: B007
            if "yards" not in v:
                v["yards"] = []

        logger.info("Location data loaded successfully.")

    def get_data(self) -> dict:
        """Return the processed location data.

        Returns:
            dict: A dictionary containing location and yard information
        """
        if self.location_dict is None:
            raise ValueError("Data not processed. Call process_data() first.")
        return self.location_dict


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = LocationDataLoader(bq_manager)
    # loader.update_column_mapping('location', {'new_column': 'NEW_COL_NAME'})

    loader.fetch_data(f"{PROJECT_ID}.SOURCE_DATA")
    loader.process_data()
    location_dict = loader.get_data()
    print(location_dict)
