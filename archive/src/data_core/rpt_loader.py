import datetime as dt

import pandas as pd

from archive.src.thirdparty.gcp import BigQueryManager
from archive.src.utils.logger import log_handler
from root import PROJECT_ID

from .base_loader import DataLoaderBase
from .col_map import REPORT_MAP

THRES_SPLIT_DT = dt.time(16, 30)  # 4:30 PM in UTC / 12:30 PM in UTC-4

logger = log_handler.get_logger(name="data_loader")


class ReportDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the ReportDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self.report_data = None
        self.column_mappings = {"report": REPORT_MAP}

    @staticmethod
    def _validate_run_dt(run_dt: str | None = None):
        if run_dt is None:
            curr_dt = dt.datetime.now(dt.timezone.utc)
            curr_time = curr_dt.time()
            run_dt = (
                curr_dt.strftime("%Y-%m-%dT05:00:00")
                if curr_time < THRES_SPLIT_DT
                else curr_dt.strftime("%Y-%m-%dT12:30:00")
            )
        else:
            # Validate date format, the date format should be "YYYY-MM-DD" and the time format should be "HH:MM:SS"
            try:
                parsed_dt = dt.datetime.strptime(run_dt, "%Y-%m-%dT%H:%M:%S")
            except ValueError as err:
                raise ValueError("Invalid date format. Use 'YYYY-MM-DDTHH:MM:SS'.") from err
            if parsed_dt.time() not in [dt.time(5, 0), dt.time(12, 30)]:
                raise ValueError("Invalid time value. Time must be either '05:00:00' or '12:30:00'.")

        return run_dt

    def fetch_data(
        self,
        data_source: str,
        run_dt: str | None = None,
        door_cy_value: str = "Door",
        hazmat_value: str = "N",
        has_customer_nominated_trucker: bool = False,
        drop_and_pick_value: str | None = "N",
    ) -> None:
        """Fetch report data from BigQuery with dynamic parameters for filtering.

        Args:
            data_source (str): The source data location.
            run_dt (str): The date and time for filtering the edw_upd_dt column (format: "YYYY-MM-DDTHH:MM").
            door_cy_value (str): The value to filter for the door_cy column.
            hazmat_value (str): The value to filter for the hazmat column.
            has_customer_nominated_trucker (bool): The flag to filter for NULL customer_nominated_trucker column.
            drop_and_pick_value (str, optional): The value to filter for drop_and_pick column ("N" or None for NULL).
        """
        run_dt = self._validate_run_dt(run_dt)
        query = """
                SELECT {col}
                FROM
                    `{data_source}.REPORT_GD` AS report
                WHERE
                    edw_upd_dt = DATETIME('{date_time}')
                    AND COP_STS_CD IN ("T", "C")
                """

        if door_cy_value:
            query += f"AND DOOR_CY = '{door_cy_value}'\n"
        if hazmat_value:
            query += f"AND HAZMAT = '{hazmat_value}'\n"
        if not has_customer_nominated_trucker:
            query += "AND CUSTOMER_NOMINATED_TRUCKER IS NULL\n"
        if drop_and_pick_value == "N":
            query += f"AND (DROP_AND_PICK IS NULL OR DROP_AND_PICK = '{drop_and_pick_value}')\n"
        else:
            query += f"AND DROP_AND_PICK = '{drop_and_pick_value}'\n"

        query = query.format(col=self._generate_select_clause("report"), data_source=data_source, date_time=run_dt)
        self.report_data = self.bq_manager.execute_query(query)
        self._convert_to_datetime(
            self.report_data,
            [
                "import_availability_at_final_cy",
                "estimated_import_delivery_date",
                "export_first_receiving_date",
                "export_cut_off_date",
                "so_create_date",
                "first_port_of_load_cutoff_date",
                "edw_upd_dt",
            ],
        )
        self._convert_to_float(
            self.report_data, ["container_qty", "container_tare_weight_lbs", "container_tare_weight_lbs"]
        )

    def process_data(self) -> None:
        """Process the fetched report data."""
        if self.report_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        logger.info("Report data loaded successfully.")

    def get_data(self) -> pd.DataFrame:
        """Return the processed report data.

        Returns:
            pd.DataFrame: The processed report DataFrame
        """
        if self.report_data is None:
            raise ValueError("Data not processed. Call process_data() first.")
        return self.report_data


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = ReportDataLoader(bq_manager)

    # loader._validate_run_dt("2024-10-08T12:30")

    # Fetch data
    loader.fetch_data(
        data_source=f"{PROJECT_ID}.REPORT",
        door_cy_value="Door",
        hazmat_value="N",
        has_customer_nominated_trucker=False,
        drop_and_pick_value="N",
    )
    loader.process_data()
    df_report = loader.get_data()
