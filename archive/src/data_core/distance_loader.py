import pandas as pd

from archive.src.thirdparty.gcp import BigQueryManager
from archive.src.utils.logger import log_handler
from root import PROJECT_ID

from .base_loader import DataLoaderBase

logger = log_handler.get_logger(name="data_loader")


class DistanceDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the DistanceDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self.distance_data = None

    def fetch_data(self, data_source: str) -> None:
        """Fetch distance and yard data from BigQuery.

        Args:
            data_source (str): The source data distance.
        """
        query = """
        SELECT DISTINCT *
        FROM `{data_source}.DISTANCES_TEST`
        WHERE distance is NOT NULL
        """
        query = query.format(data_source=data_source)
        self.distance_data = self.bq_manager.execute_query(query)

    @staticmethod
    def _clean_distance_column(df: pd.DataFrame, column_name: str = "distance") -> pd.DataFrame:
        """Cleans and converts the distance column from a string to a float, removing commas.

        Args:
            df (pd.DataFrame): The DataFrame containing the distance column.
            column_name (str): The name of the column to clean (default is 'Distance').

        Returns:
            pd.DataFrame: The updated DataFrame with the cleaned distance column.
        """
        df[column_name] = df[column_name].astype(str).str.replace(",", "").str.extract(r"(\d+\.?\d*)").astype(float)
        return df

    def process_data(self) -> None:
        """Process the fetched distance data, converting distances to numeric values."""
        if self.distance_data is None:
            raise ValueError("Distance data not fetched. Call fetch_data() first.")

        self.distance_data = self._clean_distance_column(self.distance_data, "distance")
        logger.info("Distance data loaded successfully.")

    def get_data(self) -> tuple[pd.DataFrame, pd.DataFrame]:
        """Return the processed distance data as a DataFrame.

        Returns:
            pd.DataFrame: The processed distance data.
        """
        if self.distance_data is None:
            raise ValueError("Distance data not available. Call fetch_data() first.")
        return self.distance_data


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = DistanceDataLoader(bq_manager)
    loader.fetch_data(f"{PROJECT_ID}.REPORT")
    loader.process_data()
    distance_data = loader.get_data()
