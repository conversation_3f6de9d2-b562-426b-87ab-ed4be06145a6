import datetime as dt

import pandas as pd

from archive.src.thirdparty.gcp import BigQueryManager
from archive.src.utils.logger import log_handler
from root import PROJECT_ID

from .base_loader import DataLoaderBase
from .col_map import RATELANE_MAP

logger = log_handler.get_logger(name="data_loader")


class RateLaneLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the RateLaneDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self._ratelane_data = None
        self.ratelane_import_data = None  # Bound: I
        self.ratelane_export_data = None  # Bound: O
        self.column_mappings = {"rate_lane": RATELANE_MAP}

    def fetch_data(self, data_source: str, filter_date: str | None = None) -> None:
        """Fetch rate lane data from BigQuery.

        Args:
            data_source (str): The source data location.
            filter_date (str):The date to filter ExpiryDate. If None, uses today's date in UTC.
        """
        if filter_date is None:
            tmp = dt.datetime.now(dt.timezone.utc).date()
            filter_date = tmp.strftime("%Y-%m-%d")
        else:
            # Validate date format
            try:
                dt.datetime.strptime(filter_date, "%Y-%m-%d")
            except ValueError as err:
                raise ValueError("Invalid date format. Use 'YYYY-MM-DD'.") from err

        query = """
        SELECT {col}
        FROM `{data_source}.RATE_LANE_FULL`
        WHERE PARSE_DATE('%m/%d/%Y', ExpiryDate) >= DATETIME('{date}', '+00:00')
        """

        query = query.format(col=self._generate_select_clause("rate_lane"), data_source=data_source, date=filter_date)
        self._ratelane_data = self.bq_manager.execute_query(query)

        self._convert_to_float(self._ratelane_data, ["base_rate"])
        self._convert_to_datetime(self._ratelane_data, ["expiry_date", "effective_date"])

    def process_data(self) -> None:
        """Process the fetched rate lane data."""
        if self._ratelane_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        # Determine bound
        self._ratelane_data["bound"] = self._ratelane_data.apply(self.determine_export_import, axis=1)

        # Split into import and export dataframes
        self.ratelane_import_data = self._ratelane_data[self._ratelane_data["bound"] == "IMPORT"].copy()
        self.ratelane_export_data = self._ratelane_data[self._ratelane_data["bound"] == "EXPORT"].copy()

        logger.info("Rate lane data loaded successfully.")

    def get_data(self) -> tuple[pd.DataFrame, pd.DataFrame]:
        """Return the processed rate lane data.

        Returns:
            tuple[pd.DataFrame, pd.DataFrame]: A tuple containing import and export rate lane dataframes
        """
        if self.ratelane_import_data is None or self.ratelane_export_data is None:
            raise ValueError("Data not processed. Call process_data() first.")
        return self.ratelane_import_data, self.ratelane_export_data

    @staticmethod
    def determine_export_import(row: pd.Series) -> str:
        """Determine if a rate lane is for export or import."""
        col1 = row["lane_description"]
        col2 = row["origin_location_code"]
        col3 = row["destination_location_code"]

        parts = col1.split("_")
        if len(parts) == 3:
            middle_value = parts[1][:5]  # Get the first 5 characters of the middle part
        else:
            return "N/A"

        if middle_value == col2:
            return "EXPORT"  # or O
        elif middle_value == col3:
            return "IMPORT"  # or I
        else:
            return "N/A"


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = RateLaneLoader(bq_manager)
    loader.fetch_data(f"{PROJECT_ID}.RATE_LANE")
    loader.process_data()
    import_data, export_data = loader.get_data()
