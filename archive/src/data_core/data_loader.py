from archive.src.thirdparty.gcp import BigQueryManager
from archive.src.utils.logger import log_handler
from root import PROJECT_ID

from .cnt_sz_loader import ContainerSizeDataLoader
from .distance_loader import DistanceDataLoader
from .loc_loader import LocationDataLoader
from .rl_loader import RateLaneLoader
from .rpt_loader import ReportDataLoader

logger = log_handler.get_logger(name="data_loader")


class DataLoader:
    def __init__(self, cfg: dict, bq_manager: BigQueryManager):
        """Initialize the DataLoader with a BigQuery manager and configuration.

        Args:
            cfg (dict): The configuration for the data loaders.
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        self.cfg = cfg
        self.loaders = {
            "loc": LocationDataLoader(bq_manager),
            "cnt_sz": ContainerSizeDataLoader(bq_manager),
            "rl": <PERSON>Lane<PERSON>oader(bq_manager),
            "rpt": <PERSON>Data<PERSON>oader(bq_manager),
            "dis": <PERSON>Data<PERSON>oader(bq_manager),
        }

    def fetch_data(self) -> None:
        """Fetch data from BigQuery using the loaders."""
        for key, loader in self.loaders.items():
            print(f"Fetching data for {key}")
            loader.fetch_data(**self.cfg[key])
            loader.process_data()
            print(f"Finished fetching data for {key}")

    def get_data(self, data_type: str):
        """Return the data for a specific data type.

        Args:
            data_type (str): The type of data to return. Must be one of the keys in the loaders dict: `loc`, `cnt_sz`, `rl`, `rpt`, `dis`

        Returns:
            pd.DataFrame: The data for the specified type.
        """
        if data_type not in self.loaders:
            raise ValueError(f"Invalid data type: {data_type}, must be one of {list(self.loaders.keys())}")

        return self.loaders[data_type].get_data()


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    data_cfg = {
        "loc": {"data_source": f"{PROJECT_ID}.SOURCE_DATA"},
        "cnt_sz": {"data_source": f"{PROJECT_ID}.RATE_LANE"},
        "rl": {"data_source": f"{PROJECT_ID}.RATE_LANE", "filter_date": None},
        "rpt": {
            "data_source": f"{PROJECT_ID}.REPORT",
            "run_dt": None,
            "door_cy_value": "Door",
            "hazmat_value": "N",
            "has_customer_nominated_trucker": False,
            "drop_and_pick_value": "N",
        },
        "dis": {"data_source": f"{PROJECT_ID}.REPORT"},
    }
    loader = DataLoader(data_cfg, bq_manager)
    loader.fetch_data()
