import datetime as dt
import re


class FileProcessor:
    @staticmethod
    def filter_files_by_date_range(
        files: list[dict], start_date: str, end_date: str, pattern: str = r"Matchback Report_\d{8}(0500|1230)\.csv"
    ) -> list[dict]:
        """Filter files within a specified date range that match the given pattern.

        Args:
            files (list[dict]): List of file dictionaries containing file information with 'name' key
            start_date (str): Start date in 'YYYY-MM-DD' format
            end_date (str): End date in 'YYYY-MM-DD' format
            pattern (str, optional): Regex pattern to match file names.
                                   Defaults to "Matchback Report_YYYYMMDD(0500|1230).csv"

        Returns:
            list[dict]: Filtered list of file dictionaries that match the pattern and fall within
                       the specified date range (inclusive)
        """
        file_pattern = re.compile(pattern)
        start_dt = dt.datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = dt.datetime.strptime(end_date, "%Y-%m-%d")

        return [
            file
            for file in files
            if file_pattern.match(file["name"])
            and start_dt <= dt.datetime.strptime(file["name"].split("_")[1][:8], "%Y%m%d") <= end_dt
        ]
