import re


class ReportQueries:
    @staticmethod
    def _validate_bigquery_identifier(identifier: str, identifier_type: str) -> bool:
        """Validate BigQuery identifier according to naming conventions."""
        pattern = r"^[a-zA-Z0-9_-]+$"
        if not re.match(pattern, identifier):
            raise ValueError(f"Invalid {identifier_type} format: {identifier}")
        return True

    @staticmethod
    def get_existing_dates_query(
        project_id: str,
        dataset_id: str,
        table_id: str,
        start_date: str | None = None,
        interval_days: int | None = None,
    ) -> str:
        """Get query for existing dates with input validation."""
        # Validate BigQuery identifiers
        for id_pair in [
            (project_id, "project_id"),
            (dataset_id, "dataset_id"),
            (table_id, "table_id"),
        ]:
            ReportQueries._validate_bigquery_identifier(id_pair[0], id_pair[1])

        # Validate start_date format if provided
        if start_date:
            date_pattern = r"^\d{4}-\d{2}-\d{2}$"
            if not re.match(date_pattern, start_date):
                raise ValueError("start_date must be in YYYY-MM-DD format")

        # Validate interval_days if provided
        if interval_days is not None and not isinstance(interval_days, int):
            raise ValueError("interval_days must be an integer")

        if start_date and interval_days:
            return f"""
            SELECT DISTINCT EDW_UPD_DT
            FROM `{project_id}.{dataset_id}.{table_id}`
            WHERE EDW_UPD_DT BETWEEN DATETIME("{start_date}")
            AND DATETIME_ADD(DATETIME("{start_date}"), INTERVAL {interval_days} DAY)
            ORDER BY EDW_UPD_DT
            """  # noqa: S608
        return f"""
            SELECT DISTINCT EDW_UPD_DT
            FROM `{project_id}.{dataset_id}.{table_id}`
            WHERE EDW_UPD_DT IS NOT NULL
            ORDER BY EDW_UPD_DT
            """  # noqa: S608
