import google.cloud.bigquery as bigquery

REPORT_RAW = [
    bigquery.<PERSON>hema<PERSON><PERSON>("BOUND", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("REUSE", "STRING"),
    bigquery.Schema<PERSON>ield("SHIPPING_LINE", "STRING"),
    bigquery.SchemaField("TRUCK_COMPANY_SEQUENCE", "STRING"),
    bigquery.SchemaField("TRUCK_COMPANY", "STRING"),
    bigquery.Schema<PERSON>ield("COP_NO", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("BKG_NO", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("CNTR_NO", "STRING"),
    bigquery.SchemaField("SHIPPER__CONSIGNEE_NAME", "STRING"),
    bigquery.Schema<PERSON>ield("CNTR_QTY", "FLOAT"),
    bigquery.SchemaField("CNTR_SIZE_TYPE", "STRING"),
    bigquery.<PERSON>hem<PERSON><PERSON><PERSON>("LOCATION_CITY", "STRING"),
    bigquery.SchemaField("LOCATION_STATE", "STRING"),
    bigquery.SchemaField("LOCATION_ZIP", "STRING"),
    bigquery.SchemaField("LOCATION_COUNTRY", "STRING"),
    bigquery.SchemaField("INTERCHANGE_LOCATION", "STRING"),
    bigquery.SchemaField("INTERCHANGE_LOCATION_CITY", "STRING"),
    bigquery.SchemaField("TRANSPORT_MODE", "STRING"),
    bigquery.SchemaField("IMPORT_AVAILABILITY_AT_FINAL_CY", "DATETIME"),
    bigquery.SchemaField("ESTIMATED_IMPORT_DELIVERY_DATE", "DATETIME"),
    bigquery.SchemaField("EXPORT_FIRST_RECEIVING_DATE", "DATETIME"),
    bigquery.SchemaField("EXPORT_CUT_OFF_DATE", "DATETIME"),
    bigquery.SchemaField("HAZMAT", "STRING"),
    bigquery.SchemaField("CNTR_TARE_WEIGHT_LBS", "FLOAT"),
    bigquery.SchemaField("ESTIMATE_CARGO_WEIGHT_LBS", "FLOAT"),
    bigquery.SchemaField("FLEX_HEIGHT", "STRING"),
    bigquery.SchemaField("CONTROL_OFFICE", "STRING"),
    bigquery.SchemaField("SO_CREATOR", "STRING"),
    bigquery.SchemaField("SO_CREATE_DATE", "DATETIME"),
    bigquery.SchemaField("SERVICE_ORDER_NO", "STRING"),
    bigquery.SchemaField("WORK_ORDER_NO", "STRING"),
    bigquery.SchemaField("FIRST_PORT_OF_LOAD_LOCATION", "STRING"),
    bigquery.SchemaField("FIRST_PORT_OF_LOAD_CUTOFF_DATE", "DATETIME"),
    bigquery.SchemaField("LAST_PORT_OF_DISCHARGE_LOCATION", "STRING"),
    bigquery.SchemaField("DROP_AND_PICK", "STRING"),
    bigquery.SchemaField("FOC_CLEARED_STATUS", "STRING"),
    bigquery.SchemaField("CUSTOMER_NOMINATED_TRUCKER", "STRING"),
    bigquery.SchemaField("COMMODITY", "STRING"),
    bigquery.SchemaField("TRUCKING_COMPANY_SCAC", "STRING"),
    bigquery.SchemaField("POL_POD", "STRING"),
    bigquery.SchemaField("INLAND_TRANSPORT_MODE", "STRING"),
    bigquery.SchemaField("RAIL_SCAC", "STRING"),
    bigquery.SchemaField("DOOR_CY", "STRING"),
    bigquery.SchemaField("STCC_CODE", "STRING"),
    bigquery.SchemaField("STREET_ADDRESS", "STRING"),
    bigquery.SchemaField("CRE_DT", "DATETIME", default_value_expression="CURRENT_DATETIME()"),
    bigquery.SchemaField("UPD_DT", "DATETIME", default_value_expression="CURRENT_DATETIME()"),
    bigquery.SchemaField("DEL_DT", "DATETIME"),
    bigquery.SchemaField("EDW_UPD_DT", "DATETIME"),
]

REPORT = REPORT_RAW + [
    bigquery.SchemaField("EDW_UPD_DT_UTC", "DATETIME"),
    bigquery.SchemaField("COP_STS_CD", "STRING"),
    bigquery.SchemaField("BKG_STS_CD", "STRING"),
    bigquery.SchemaField("BKG_EVNT_TP_CD", "STRING"),
    bigquery.SchemaField("DWL_COP_HIS_CRE_DT", "DATETIME"),
]
