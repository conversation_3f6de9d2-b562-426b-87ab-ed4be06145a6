from dataclasses import dataclass

import pandas as pd


@dataclass
class DataCollection:
    """Container related data for candidate matching."""

    location_data: pd.DataFrame | dict
    cnt_size_map_data: pd.DataFrame | dict
    ratelane_data: pd.DataFrame
    report_data: pd.DataFrame
    ratelane_mapping_data: pd.DataFrame
    distance_data: pd.DataFrame

    # loc_dict: dict[str, dict[str, str]],
    # cnt_size_map_dict: dict[str, str],
    # rpt_data: pd.DataFrame,
    # rl_data: pd.DataFrame,
    # distance_data: DistanceData,

    def __post_init__(self):
        """Initialize the candidate data."""
        self.__check_empty(self.location_data, "location_data")
        self.__check_empty(self.cnt_size_map_data, "cnt_size_map_data")
        self.__check_empty(self.ratelane_data, "ratelane_data")
        self.__check_empty(self.report_data, "report_data")
        self.__check_empty(self.distance_data, "distance_data")
        self.__check_empty(self.ratelane_mapping_data, "ratelane_mapping_data")

    def __check_empty(self, data: pd.DataFrame | dict, name: str):
        """Check if the data is empty."""
        if isinstance(data, dict) and not data:
            raise ValueError(f"Empty {name} dictionary provided")
        elif isinstance(data, pd.DataFrame) and data.empty:
            raise ValueError(f"Empty {name} DataFrame provided")


@dataclass
class RouteMatch:
    import_details: dict
    export_details: dict
    matched_details: dict
    shipline: str = "ONE"


@dataclass
class MatchingResult:
    """Container for triangulation matching results."""

    location: str
    ratelane_pairs: dict | None
    lr_pairs: dict | None
    optim_data: dict | None


@dataclass
class TripData:
    lane: str
    distance: float
    cost: float
    carrier_cd: str
    details: dict


@dataclass
class RoundTripResult:
    ib_lane: str
    ob_lane: str
    distance: float
    cost: float
    inbound_data: dict
    outbound_data: dict


@dataclass
class StreetTurnResult:
    ib_lane: str
    ob_lane: str
    distance: float
    cost: float
    inbound_data: dict
    outbound_data: dict


@dataclass
class LanePair:
    """Data class to store lane pair information and calculations."""

    lane_description: str
    origin_location_code: str
    origin_location_name: str
    origin_location_type: str
    origin_geo_hierarchy: str
    origin_address_1: str | None
    origin_city: str
    origin_state: str
    origin_country: str
    destination_location_code: str
    destination_location_name: str
    destination_location_type: str
    destination_geo_hierarchy: str
    destination_address_1: str | None
    destination_city: str
    destination_state: str
    destination_country: str
    via_point1: str | None
    base_rate: float
    currency_cd: str
    trip_type: str
    commodity_class: str | None
    is_hazmat: str | None
    equipment_class: str | None
    equipment_size_type: str | None
    carrier_cd: str
    distance: float

    reverse_lane_description: str
    reverse_origin_location_code: str
    reverse_origin_location_name: str
    reverse_origin_location_type: str
    reverse_origin_geo_hierarchy: str
    reverse_origin_address_1: str | None
    reverse_origin_city: str
    reverse_origin_state: str
    reverse_origin_country: str
    reverse_destination_location_code: str
    reverse_destination_location_name: str
    reverse_destination_location_type: str
    reverse_destination_geo_hierarchy: str
    reverse_destination_address_1: str | None
    reverse_destination_city: str
    reverse_destination_state: str
    reverse_destination_country: str
    reverse_via_point1: str | None
    reverse_base_rate: float
    reverse_currency_cd: str
    reverse_trip_type: str
    reverse_commodity_class: str | None
    reverse_is_hazmat: str | None
    reverse_equipment_class: str | None
    reverse_equipment_size_type: str | None
    reverse_carrier_cd: str
    reverse_distance: float

    pair_type: str | None = None
    total_distance: float | None = None
    total_cost: float | None = None
