from pathlib import Path
from typing import Any

import pandas as pd

from archive.src_v2.const import settings
from archive.src_v2.core.data_model import DataCollection
from archive.src_v2.core.geo.geo import convert_distance
from archive.src_v2.thirdparty.gcp import (
    upload_raw_pairs_to_bq,
    upload_run_info_to_bq,
    upload_savings_to_bq,
    upload_street_turn_to_bq,
    upload_valid_pairs_to_bq,
)
from archive.src_v2.utils.decorators import measure_time
from archive.src_v2.utils.file_utils import save_json
from archive.src_v2.utils.logger import log_handler
from archive.src_v2.utils.preprocess import find_key_by_location_name, get_run_info

from .route_matcher import RM_COULMN_MAP, RouteMatcher

logger = log_handler.get_logger(name=__name__)


class StreetTurnMatcher:
    """Matches import and export containers for street turn opportunities.

    This class handles the core matching logic for finding compatible import/export
    pairs that can be street turned to optimize container reuse and reduce empty miles.

    Key features:
    - Validates container size and availability date compatibility
    - Calculates distances and potential savings from street turns
    - Uses linear programming to optimize overall matches
    - Saves match results and metadata
    """

    def __init__(self) -> None:
        """Initializes the StreetTurnMatcher object and loads the necessary data."""
        self.matcher = RouteMatcher()

    def __count_pairs(self, pairs_dict: dict) -> int:
        """Count the total number of pairs in a nested dictionary.

        Args:
            pairs_dict: Dictionary of dictionaries containing pairs

        Returns:
            int: Total number of pairs
        """
        return sum(len(inner_dict) for inner_dict in pairs_dict.values())

    def __initialize_run_metadata(self, node_cd: str, run_dt: str) -> dict[str, Any]:
        """Initialize the metadata of pair counts dictionary with initial filtering results.

        Returns:
            dict[str, int|float]: Dictionary containing pair counts at each filtering stage
        """
        run_metadata = {
            "node_cd": node_cd,
            "run_dt": run_dt,
            "time_available_gap": settings.SYSTEM.TIME_AVAILABLE_GAP,
            # convert to miles
            "distance_thres": round(convert_distance(settings.SYSTEM.DISTANCE_THRES_METERS / 1000, "miles")),
            "raw_pairs_count": 0,
            "cntr_sz_valid_pairs_count": 0,
            "time_valid_pairs_count": 0,
            "cntr_sz_time_valid_pairs_count": 0,
            "matches_count": 0,
            "dist_valid_pairs_count": 0.0,
            "total_cost_save": 0.0,
            "total_distance_save": 0.0,
            "container_reuse": 0,
        }

        return run_metadata

    def __update_initial_pair_counts(
        self, raw_pairs: dict, valid_cntz: dict, valid_date: dict, valid_pairs: dict
    ) -> dict[str, int]:
        """Update and log the initial pair counts for different validation stages.

        This method counts the number of pairs at each validation stage and creates
        a dictionary with these counts. It also logs the counts for monitoring purposes.

        Args:
            raw_pairs (dict): Dictionary containing all possible import-export pairs
                before any validation
            valid_cntz (dict): Dictionary containing pairs that passed container
                size validation
            valid_date (dict): Dictionary containing pairs that passed date
                validation
            valid_pairs (dict): Dictionary containing pairs that passed both
                container size and date validation

        Returns:
            dict[str, int]: Dictionary containing counts for each validation stage:
                - raw_pairs_count: Number of total possible pairs
                - cntr_sz_valid_pairs_count: Number of container size valid pairs
                - time_valid_pairs_count: Number of time valid pairs
                - cntr_sz_time_valid_pairs_count: Number of pairs valid for both
                  container size and time
        """
        _tmp = {
            "raw_pairs_count": self.__count_pairs(raw_pairs),
            "cntr_sz_valid_pairs_count": self.__count_pairs(valid_cntz),
            "time_valid_pairs_count": self.__count_pairs(valid_date),
            "cntr_sz_time_valid_pairs_count": self.__count_pairs(valid_pairs),
        }

        # Log initial counts
        logger.info(
            "Initial pair counts:\n"
            f"  raw_pairs_count: {_tmp['raw_pairs_count']}\n"
            f"  cntr_sz_valid_pairs_count: {_tmp['cntr_sz_valid_pairs_count']}\n"
            f"  time_valid_pairs_count: {_tmp['time_valid_pairs_count']}\n"
            f"  cntr_sz_time_valid_pairs_count: {_tmp['cntr_sz_time_valid_pairs_count']}"
        )

        return _tmp

    @measure_time(logger=logger)
    def run(
        self,
        node_cd: str,
        run_dt: str,
        data: DataCollection,
        criteria: str,
        distance_method: str,
        save_dir: Path = Path("."),
    ):
        """Execute the full matching process for a given node.

        Performs container matching in stages:
        1. Find valid import/export pairs based on constraints
        2. Calculate distances and potential savings
        3. Run LP solver to optimize matches
        4. Save results and metadata

        Args:
            node_cd: Container yard/terminal code to process
            run_dt: Date of the run
            data: Collection of required data (locations, rates, etc)
            criteria: Optimization criteria - "distance", "cost" or "distance_cost"
            distance_method: Method to calculate distances between locations
            save_dir: Directory to save output files

        Returns:
            dict: A dictionary containing the following keys:
                - 'raw_pairs_count': Total number of raw pairs
                - 'matches_count': Total number of matches found
                - 'valid_pairs_count': Total number of valid pairs after all filtering
        """
        save_dir.mkdir(parents=True, exist_ok=True)
        run_metadata: dict[str, Any] = self.__initialize_run_metadata(node_cd, run_dt)

        # ---------------------------- Find pairs import and export, filter with constraints ----------------------------#
        im_ex_pairs = self.matcher.run(node_cd=node_cd, data_coll=data, date_time=run_dt)
        if im_ex_pairs is None:
            return run_metadata

        raw_pairs, valid_pairs, valid_cntz, valid_date, raw_pairs_info, valid_distance = im_ex_pairs
        pair_counts: dict[str, Any] = self.__update_initial_pair_counts(raw_pairs, valid_cntz, valid_date, valid_pairs)
        run_metadata.update(pair_counts)

        # Save initial matching results
        save_json(raw_pairs, save_dir / f"{node_cd}_01_all_possible_pairs.json")
        save_json(valid_cntz, save_dir / f"{node_cd}_02_container_size_filtered.json")
        save_json(valid_date, save_dir / f"{node_cd}_03_date_filtered.json")
        save_json(valid_pairs, save_dir / f"{node_cd}_05_valid_pairs_constrains_filter.json")
        save_json(valid_distance, save_dir / f"{node_cd}_04_distance_filtered.json")

        # ---------------------------- Put to the Linear Programming  ---------------------------------------------------#
        # Run LP solver
        lp_results, savings = self.matcher.lp_solver(
            data_coll=data,
            valid_pairs=valid_pairs,
            raw_pairs_info=raw_pairs_info,
            node_cd=node_cd,
            n_ex=1,
            n_im=1,
            criteria=criteria,
            save_dir=save_dir,
        )

        # Save LP solver results
        if lp_results is None:
            return run_metadata
        self.matcher.save_matches(lp_results, save_dir / f"{node_cd}_07_lp_solver_results.json")

        for im, export_dict in valid_pairs.items():
            im_index = int(im.split("_")[1])
            for ex, val in export_dict.items():  # noqa: B007
                ex_index = int(ex.split("_")[1])
                if lp_results[im][ex].varValue == 1:
                    self.matcher.add_recommended_route(im_index, ex_index, data.report_data, data.cnt_size_map_data)
                    run_metadata["matches_count"] += 1

        no_cal_gmaps_api, cache_len = get_run_info()
        run_metadata.update({"no_cal_gmaps_api": no_cal_gmaps_api, "cache_len": cache_len})
        self.__print_summary(run_metadata)

        # Save the final route recommendations
        self.matcher.save_results_to_csv(save_dir / f"{node_cd}_09_street_turn_matches.csv")

        # ---------------------------- Searching with ratelane ------------------------------------------------------------#
        street_turn_matches_fpath = save_dir / f"{node_cd}_09_street_turn_matches.csv"
        new_rec, result_summary = self.__choose_ratelane(node_cd, street_turn_matches_fpath, data, save_dir)
        run_metadata.update(result_summary)

        if new_rec.empty:
            return run_metadata
        # ---------------------------- Save to BigQuery --------------------------------------------------------------------#
        upload_raw_pairs_to_bq(
            node_cd,
            data,
            raw_pairs_info,
            settings.ENV.PROJECT_ID,
            f"{settings.DB.DATAPIPELINE_TABLE_ID}.{settings.DB.MATCHING_CANDIDATES_ID}",
            run_dt,
        )
        upload_street_turn_to_bq(
            new_rec,
            run_metadata,
            settings.ENV.PROJECT_ID,
            f"{settings.DB.DATAPIPELINE_TABLE_ID}.{settings.DB.STREET_TURN_RUN_MATCHES_ID}",
        )
        upload_savings_to_bq(
            savings,
            settings.ENV.PROJECT_ID,
            f"{settings.DB.DATAPIPELINE_TABLE_ID}.{settings.DB.SAVINGS_MATRIX_ID}",
            run_dt,
        )
        upload_valid_pairs_to_bq(
            valid_pairs,
            settings.ENV.PROJECT_ID,
            f"{settings.DB.DATAPIPELINE_TABLE_ID}.{settings.DB.VALID_PAIRS_ID}",
            run_dt,
        )
        upload_run_info_to_bq(
            [run_metadata],
            settings.ENV.PROJECT_ID,
            f"{settings.DB.DATAPIPELINE_TABLE_ID}.{settings.DB.STREET_TURN_RUN_META_ID}",
        )

        return run_metadata

    def __print_summary(self, run_metadata: dict[str, int]):
        """Prints a summary of the matching process."""
        logger.info(
            f"\nTotal number of raw pairs: {run_metadata['raw_pairs_count']}\n"
            f"Total number of pairs after container size filtering: {run_metadata['cntr_sz_valid_pairs_count']}\n"
            f"Total number of pairs after date filtering: {run_metadata['time_valid_pairs_count']}\n"
            f"Total number of pairs after <container size, time> filtering: {run_metadata['cntr_sz_time_valid_pairs_count']}\n"
            f"Total number of pairs after <distance> filtering: {run_metadata['dist_valid_pairs_count']}"
            f"Total number of matches found: {run_metadata['matches_count']}\n"
        )

    def __choose_ratelane(
        self, node_cd: str, street_turn_matches_fpath: Path, data: DataCollection, output_dir: Path
    ) -> tuple[pd.DataFrame, dict[str, Any]]:
        """_summary_.

        Args:
            node_cd (str): _description_
            street_turn_matches_fpath (Path): _description_
            data (DataCollection): _description_
            output_dir (Path): _description_
        """
        output_dir.mkdir(parents=True, exist_ok=True)

        df_rec = pd.read_csv(street_turn_matches_fpath)
        new_rec = pd.DataFrame()

        for _, row in df_rec.iterrows():
            im_id = f"{row['Import BKG NO']} - {row['Import COP NO']}"
            ex_id = f"{row['Export BKG NO']} - {row['Export COP NO']}"
            im_city, ex_city = row["Import Door City"], row["Export Door City"]

            import_loc_cd = find_key_by_location_name(im_city, data.location_data)
            export_loc_cd = find_key_by_location_name(ex_city, data.location_data)

            df_final, df_cands = self.__merge_result(node_cd, import_loc_cd, export_loc_cd, data.ratelane_mapping_data)
            if df_final.empty:
                logger.warning(f"No street turn and round trip matches found for {im_id} and {ex_id}")
                continue

            df_final = df_final.drop(["optimal_x", "optimal_y"], axis=1, errors="ignore")
            new_row = row[list(RM_COULMN_MAP.values())]

            tmp_df = pd.DataFrame(new_row).T.merge(df_final, how="cross")

            tmp_df.drop(["street_turn_optimal", "round_trip_optimal"], axis=1, inplace=True)
            new_rec = pd.concat([new_rec, tmp_df])

        if new_rec.empty:
            logger.warning("No matches found for any street turn and round trip combinations")
            return pd.DataFrame(), {
                "total_cost_save": 0.0,
                "total_distance_save": 0.0,
                "container_reuse": 0,
            }

        output_fpath = output_dir / f"{node_cd}_10_final_street_turn_matches.xlsx"
        with pd.ExcelWriter(output_fpath, engine="xlsxwriter") as writer:
            new_rec.to_excel(writer, sheet_name="Street Turn Matches", index=False)

            report_summary = {
                "total_cost_save": float(new_rec.loc[new_rec["optimal"], "cost_save"].sum()),
                "total_distance_save": new_rec.loc[new_rec["optimal"], "Distance Saved (km)"].sum(),
                "container_reuse": new_rec["Export CNTR NO"].nunique(),
            }

            summary_df = pd.DataFrame([report_summary])
            summary_df.rename(
                columns={
                    "total_cost_save": "Total Cost Saved ($)",
                    "total_distance_save": "Total Distance Saved (km)",
                    "container_reuse": "Container Reuse",
                },
                inplace=True,
            )
            summary_df.to_excel(writer, sheet_name="Summary", index=False)

        return new_rec, report_summary

    def __merge_result(
        self, node_cd, import_loc_cd, export_loc_cd, data_rl: pd.DataFrame
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """_summary_."""
        # Initialize a cache dictionary to store results for each combination of port_cd, import_loc_cd, and export_loc_cd
        if not hasattr(self, "_cache"):
            self._cache: dict = {}

        # Create a unique key for the current combination
        cache_key = (node_cd[:5], import_loc_cd, export_loc_cd)

        # Check if the result is already in the cache
        if cache_key in self._cache:
            tmp_df = self._cache[cache_key]
        else:
            # Perform the search and store the result in the cache
            tmp_df = data_rl[
                (data_rl["import_loc_cd"] == import_loc_cd)
                & (data_rl["export_loc_cd"] == export_loc_cd)
                & (data_rl["port_cd"].str.startswith(node_cd[:5]))
            ]
            tmp_df.drop(
                [
                    "port_cd",
                    "import_loc_cd",
                    "export_loc_cd",
                ],
                axis=1,
                inplace=True,
                errors="ignore",
            )

            self._cache[cache_key] = tmp_df

        tmp_df["total_cost"] = tmp_df["import_base_rate"] + tmp_df["export_base_rate"]
        tmp_df["optimal"] = False

        # Filter street turns based on trip types
        street_turn = tmp_df[(tmp_df["import_trip_type"] == "RETURN") & (tmp_df["export_trip_type"] == "ONE WAY")]
        street_turn.drop_duplicates(inplace=True)

        # Get all possible street turns
        street_turn_result = street_turn[street_turn["import_vendor"] == street_turn["export_vendor"]].copy()

        # Identify the optimal street turn
        optimal_street_turn = street_turn_result[
            street_turn_result["total_cost"] == street_turn_result["total_cost"].min()
        ].head(1)
        # Add prefix to street turn result
        street_turn_result = street_turn_result.add_prefix("street_turn_")
        # Add a primary choice column: True for optimal, False for others
        street_turn_result.loc[:, "street_turn_optimal"] = False
        street_turn_result.loc[optimal_street_turn.index, "street_turn_optimal"] = True
        if street_turn_result.empty:
            return pd.DataFrame(), tmp_df

        # Filter round trips based on trip types
        df_merge = pd.DataFrame()
        for _, candidate in street_turn_result.iterrows():
            round_trip = tmp_df[(tmp_df["import_trip_type"] == "RETURN") & (tmp_df["export_trip_type"] == "RETURN")]
            round_trip.drop_duplicates(inplace=True)

            # Filter round trips that match the current export_vendor_code in the candidate
            round_trip_result = round_trip[round_trip["import_vendor"] == candidate["street_turn_export_vendor"]]

            # Get the round trip with minimum import and export base rates
            round_trip_result = round_trip_result[
                (round_trip_result["import_base_rate"] == round_trip_result["import_base_rate"].min())
                & (round_trip_result["export_base_rate"] == round_trip_result["export_base_rate"].min())
            ]

            # Add prefix to round trip result and select one as the primary choice
            round_trip_result = round_trip_result.add_prefix("round_trip_")
            round_trip_result.loc[round_trip_result.sample().index, "round_trip_optimal"] = True

            # Merge only the current candidate with the round trip result
            df_final = pd.DataFrame([candidate]).merge(round_trip_result, how="cross")

            df_final["cost_save"] = df_final["round_trip_total_cost"] - df_final["street_turn_total_cost"]

            # Concatenate the result into df_merge
            df_merge = pd.concat([df_merge, df_final], ignore_index=True)
        # Identify the row with the maximum cost_save
        max_cost_save = df_merge["cost_save"].max()

        # Ensure we only set optimal=True if max_cost_save is positive
        if max_cost_save > 0:
            candidates = df_merge[df_merge["cost_save"] == max_cost_save]
            min_street_turn_cost = candidates["street_turn_total_cost"].min()
            df_merge["optimal"] = (df_merge["cost_save"] == max_cost_save) & (
                df_merge["street_turn_total_cost"] == min_street_turn_cost
            )
        elif max_cost_save == 0:
            min_street_turn_cost = df_merge["street_turn_total_cost"].min()
            df_merge["optimal"] = df_merge["street_turn_total_cost"] == min_street_turn_cost
        else:
            df_merge["optimal"] = False

        # Ensure only the optimal row retains cost_save, others set to 0
        df_merge.loc[~df_merge["optimal"], "cost_save"] = 0

        return df_merge, tmp_df
