import pandas as pd
from rich.progress import BarColumn, Progress, TaskProgressColumn, TextColumn

from archive.src_v2.const import ConstMap
from archive.src_v2.core.data_model import LanePair
from archive.src_v2.utils.logger import log_handler
from archive.src_v2.utils.preprocess import get_distance_between_locations

logger = log_handler.get_logger(__name__)


class LaneProcessor:
    """Class to analyze lane pairs and perform calculations."""

    def __init__(self):
        """Initialize LaneProcessor with location dictionary and distance data."""
        self._preprocess_distance()

    def _preprocess_distance(self, ratelane_data, distance_data, distance_method: str):
        """Preprocess distances for each row with progress tracking.

        Uses rich progress bar to show processing status.
        Calculates distance terms for each row and adds them to DataFrame.
        """
        total_rows = len(ratelane_data)

        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
        ) as progress:
            task = progress.add_task("Calculating distances...", total=total_rows)

            try:
                distances: list[float] = []
                for row in ratelane_data.itertuples():
                    res = self.calculate_node_distance_terms(
                        row=row, distance_data=distance_data, distance_method=distance_method
                    )
                    distances.append(res)
                    progress.advance(task)

                ratelane_data["distance"] = distances

            except Exception as e:
                progress.console.print(f"[red]Error during distance calculation: {str(e)}")
                raise

    def calculate_node_distance_terms(
        self, row, distance_data, distance_method: str, door_cd: str | None = None, door_address: str | None = None
    ) -> float:
        """Calculate the distance between locations using distance dataset or Google Maps API.

        Args:
            row: DataFrame row containing location information
            distance_data: DataFrame containing distance data
            distance_method: Method to use for distance calculation
            door_cd: Optional door location code
            door_address: Optional door address

        Returns:
            float: Calculated distance between locations
        """
        try:
            orig_cd = row.origin_location_code
            orig_address = (
                row.origin_address_1
                if row.origin_address_1
                else ", ".join(
                    [_ for _ in [row.origin_city, row.origin_state, row.origin_country] if _ and not pd.isna(_)]
                )
            )

            dest_cd = row.destination_location_code
            dest_address = (
                row.destination_address_1
                if row.destination_address_1
                else ", ".join(
                    [
                        _
                        for _ in [row.destination_city, row.destination_state, row.destination_country]
                        if _ and not pd.isna(_)
                    ]
                )
            )

            d: float = ConstMap.DEFAULT_DISTANCE
            if door_cd is None or door_address is None:
                d = get_distance_between_locations(orig_address, dest_address, distance_data, distance_method)
                return d

            if door_cd[: ConstMap.LOCATION_CODE_PREFIX_LENGTH] == orig_cd[: ConstMap.LOCATION_CODE_PREFIX_LENGTH]:
                d = get_distance_between_locations(door_address, dest_address, distance_data, distance_method)
            elif door_cd[: ConstMap.LOCATION_CODE_PREFIX_LENGTH] == dest_cd[: ConstMap.LOCATION_CODE_PREFIX_LENGTH]:
                d = get_distance_between_locations(orig_address, door_address, distance_data, distance_method)
            if d == ConstMap.DEFAULT_DISTANCE:
                d = get_distance_between_locations(orig_address, dest_address, distance_data, distance_method)

            return d

        except Exception as e:
            logger.error(
                f"Error calculating distance for row {row} and door_cd {door_cd}, door_address {door_address}.\nError: {e}",
                exc_info=True,
            )
            return ConstMap.DEFAULT_DISTANCE

    def calculate_street_turn_distance_terms(
        self,
        inbound_route: str,
        outbound_route: str,
        ib_address: str,
        ob_address: str,
        loc_dict,
        distance_data,
        distance_method: str,
    ) -> float:
        """_summary_.

        Args:
            inbound_route (pd.Dataframe): _description_
            outbound_route (pd.Dataframe): _description_
            ib_address (str): _description_
            ob_address (str): _description_
            loc_dict (dict): _description_
            distance_data (pd.Dataframe): _description_
            distance_method (str): _description_

        Returns:
            tuple[float, float, float]: _description_
        """
        _, iloc2, _ = inbound_route.split("_")
        _, eloc2, _ = outbound_route.split("_")

        iloc2_name: str = loc_dict[iloc2[: ConstMap.LOCATION_CODE_PREFIX_LENGTH]]["location_name"]
        eloc2_name: str = loc_dict[eloc2[: ConstMap.LOCATION_CODE_PREFIX_LENGTH]]["location_name"]

        imdoor_to_exdoor: float = get_distance_between_locations(ib_address, ob_address, distance_data, distance_method)
        if imdoor_to_exdoor == ConstMap.DEFAULT_DISTANCE:
            imdoor_to_exdoor = get_distance_between_locations(iloc2_name, eloc2_name, distance_data, distance_method)
        return imdoor_to_exdoor

    @staticmethod
    def _reverse_lane(lane: str) -> str:
        """Reverse the components of a lane description."""
        return "_".join(lane.split("_")[::-1])

    @staticmethod
    def _should_skip_pair(original_type: str, reverse_type: str) -> bool:
        """Determine if a pair should be skipped based on trip types."""
        return (original_type == "ONE WAY" and reverse_type == "ONE WAY") or (
            original_type == "ONE WAY" and reverse_type == "RETURN"
        )

    # def _calculate_pair_metrics(self, pair: LanePair) -> None:
    #     """Calculate total distance and cost based on trip types."""
    #     if pair.trip_type == "RETURN" and pair.reverse_trip_type == "RETURN":
    #         pair.pair_type = "ROUND_TRIP"
    #         pair.total_distance = pair.distance + pair.reverse_distance
    #         pair.total_cost = pair.base_rate + pair.reverse_base_rate
    #     elif pair.trip_type == "RETURN" and pair.reverse_trip_type == "ONE WAY":
    #         pair.pair_type = "STREET_TURN"
    #         pair.total_distance = pair.distance + pair.reverse_distance
    #         pair.total_cost = pair.base_rate + pair.reverse_base_rate

    def _create_pair(self, orig: pd.Series, reverse: pd.Series) -> LanePair:
        """Create a LanePair object from two DataFrame rows."""
        _orig = orig.to_dict()
        _reverse = {f"reverse_{k}": v for k, v in reverse.to_dict().items()}

        return LanePair(**{**_orig, **_reverse})

    def find_reverse_lane_pairs(self, df: pd.DataFrame) -> list[LanePair]:
        """Find and analyze reverse lane pairs."""
        results = []

        for idx, row in df.iterrows():
            reversed_lane = self._reverse_lane(row["lane_description"])

            # Find matching records
            matches = df[
                (df["lane_description"] == reversed_lane) & (df["carrier_cd"] == row["carrier_cd"]) & (df.index > idx)
            ]

            for _, match in matches.iterrows():
                if self._should_skip_pair(row["trip_type"], match["trip_type"]):
                    continue

                pair = self._create_pair(row, match)
                # self._calculate_pair_metrics(pair)
                results.append(pair)

        return results
