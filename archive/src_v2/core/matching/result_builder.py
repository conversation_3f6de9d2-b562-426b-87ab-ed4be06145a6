from pathlib import Path

import pandas as pd

from archive.src_v2.core.data_model import RouteMatch
from archive.src_v2.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


class ResultsBuilder:
    def __init__(self):
        """Initialize the ResultsBuilder object."""
        self.columns = {
            "import_bkg_no": "Import Booking Number",
            "export_bkg_no": "Export Booking Number",
            "import_cop_no": "Import COP Number",
            "export_cop_no": "Export COP Number",
            "size_type": "Size Type",
            "import_cntr_no": "Import Container Number",
            "export_cntr_no": "Export Container Number",
            "shipline": "Shipline",
            "import_door_city": "Import Door City",
            "import_door_state": "Import Door State",
            "import_port_ramp": "Import Port Ramp",
            "import_address": "Import Address",
            "import_shipper": "Import Shipper",
            "import_trucker": "Import Trucker",
            "export_door_city": "Export Door City",
            "export_door_state": "Export Door State",
            "export_port_ramp": "Export Port Ramp",
            "export_address": "Export Address",
            "export_shipper": "Export Shipper",
            "export_trucker": "Export Trucker",
            "import_available_date": "Import Available Date",
            "import_expire_date": "Import Expire Date",
            "export_available_date": "Export Available Date",
            "export_expire_date": "Export Expire Date",
            "overlaps": "Overlaps",
            "overlap_difference_days": "Overlap Difference Days",
            "distance_between_import_export": "Distance Between Import and Export (km)",
            "round_trip_distance (km)": "Round Trip Distance",
            "street_turn_distance (km)": "Street Turn Distance",
            "distance_saved (km)": "Distance Saved",
            "import_cost": "Import Cost",
            "export_cost_round_trip": "Export Cost Round Trip",
            "export_cost_street_turn": "Export Cost Street Turn",
            "round_trip_cost": "Round Trip Cost",
            "street_turn_cost": "Street Turn Cost",
            "cost_saved": "Cost Saved",
            "optimal_route": "Optimal Route",
        }
        self.results_df = pd.DataFrame(columns=list(self.columns.keys()))
        self.matches: list[RouteMatch] = []

    def add_match(self, route_match: RouteMatch) -> None:
        """Add a new route match to the results."""
        new_row = {
            "import_bkg_no": route_match.import_details["booking_no"],
            "export_bkg_no": route_match.export_details["booking_no"],
            "import_cop_no": route_match.import_details["cop_no"],
            "export_cop_no": route_match.export_details["cop_no"],
            "shipline": route_match.shipline,
            "trucker": route_match.export_details["truck_company"],
            "import_cntr_no": route_match.import_details["container_no"],
            "export_cntr_no": route_match.export_details["container_no"],
            "size_type": route_match.matched_details["size_type"],
            "import_door_city": route_match.import_details["location_city"],
            "import_door_state": route_match.import_details["location_state"],
            "import_port_ramp": route_match.import_details["interchange_location"],
            "import_address": route_match.matched_details["ib_address"],
            "import_shipper": route_match.import_details["shipper__consignee_name"],
            "export_door_city": route_match.export_details["location_city"],
            "export_door_state": route_match.export_details["location_state"],
            "export_port_ramp": route_match.export_details["interchange_location"],
            "export_address": route_match.matched_details["ob_address"],
            "export_shipper": route_match.export_details["shipper__consignee_name"],
            "import_available_date": route_match.import_details["import_availability_at_final_cy"],
            "import_expire_date": route_match.import_details["estimated_import_delivery_date"],
            "export_available_date": route_match.export_details["export_first_receiving_date"],
            "export_expire_date": route_match.export_details["export_cut_off_date"],
            "overlaps": route_match.matched_details["overlaps"],
            "overlap_difference_days": route_match.matched_details["overlap_difference_days"],
            "distance_between_import_export": route_match.matched_details["ib_to_ob"],
            "round_trip_distance": route_match.matched_details["round_trip_distance"],
            "street_turn_distance": route_match.matched_details["street_turn_distance"],
            "distance_saved": route_match.matched_details["distance_saved"],
            "import_cost": route_match.matched_details["ib_round_trip_cost"],
            "export_cost_round_trip": route_match.matched_details["ob_round_trip_cost"],
            "export_cost_street_turn": route_match.matched_details["ob_street_turn_cost"],
            "round_trip_cost": route_match.matched_details["round_trip_cost"],
            "street_turn_cost": route_match.matched_details["street_turn_cost"],
            "cost_saved": route_match.matched_details["cost_saved"],
            "optimal_route": route_match.matched_details["optimal_route"],
        }
        self.results_df = pd.concat([self.results_df, pd.DataFrame([new_row])], ignore_index=True)

    def save_results(self, save_path: Path) -> None:
        """Save results to CSV and generate summary."""
        output_file = save_path / "route_recommendation.csv"
        self.results_df.rename(columns=self.columns, inplace=True)
        self.results_df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(self.results_df)} matches to {output_file}")
