from archive.src_v2.const.config import settings
from archive.src_v2.thirdparty.gcp import GCSManager
from archive.src_v2.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


class ArtifactsHandler:
    def __init__(self):
        """Initialize ArtifactsHandler with GCSManager."""
        self.gcs_manager = GCSManager(bucket_name=settings.GCP.STORAGE.BUCKET_NAME)

    def save_artifact(self, artifact: str, artifact_name: str):
        """Save artifact to GCS.

        Args:
            artifact (str): _description_
            artifact_name (str): _description_
        """
        self.gcs_manager.upload_to_fname(source_fname=artifact, destination_blob_name=artifact_name)
        logger.info("Artifact saved to GCS: %s", artifact_name)

    def upload_artifact(self, artifact: str, artifact_name: str):
        """Upload artifact to GCS."""
        pass
