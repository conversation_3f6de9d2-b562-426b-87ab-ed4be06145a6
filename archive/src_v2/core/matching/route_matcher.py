import multiprocessing
import pickle
import random
import typing
from concurrent.futures import <PERSON><PERSON>ool<PERSON>xecutor
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

import numpy as np
import pandas as pd
from tqdm.auto import tqdm

from archive.src_v2.const import ConstMap, settings
from archive.src_v2.core.data_model import DataCollection
from archive.src_v2.core.lp import LinearSolver
from archive.src_v2.utils.decorators import measure_time
from archive.src_v2.utils.exceptions import GMapsAPIError
from archive.src_v2.utils.file_utils import save_json
from archive.src_v2.utils.logger import log_handler
from archive.src_v2.utils.preprocess import (
    find_key_by_location_name,
    get_distance_between_locations,
    get_distance_in_meter,
)

logger = log_handler.get_logger(name=__name__)

pd.options.mode.chained_assignment = None


RM_COULMN_MAP = {
    "import_bkg_no": "Import BKG NO",
    "import_cop_no": "Import COP NO",
    "import_cntr_no": "Import CNTR NO",
    "export_bkg_no": "Export BKG NO",
    "export_cop_no": "Export COP NO",
    "export_cntr_no": "Export CNTR NO",
    "size_type": "Size Type",
    "shipline": "Shipline",
    "import_door_city": "Import Door City",
    "import_door_state": "Import Door State",
    "import_port_ramp": "Import Port Ramp",
    "import_address": "Import Address",
    "import_shipper": "Import Shipper",
    "import_trucker": "Import Trucker",
    "export_door_city": "Export Door City",
    "export_door_state": "Export Door State",
    "export_port_ramp": "Export Port Ramp",
    "export_address": "Export Address",
    "export_shipper": "Export Shipper",
    "export_trucker": "Export Trucker",
    "import_available_date": "Import Available Date",
    "import_expire_date": "Import Expire Date",
    "export_available_date": "Export Available Date",
    "export_expire_date": "Export Expire Date",
    "overlaps": "Overlaps",
    "overlap_difference_days": "Overlap Difference Days",
    "distance_between_import_export": "Distance Between Import and Export (km)",
    "street_turn_distance": "Street Turn Distance (km)",
    "round_trip_distance": "Round Trip Distance (km)",
    "distance_saved": "Distance Saved (km)",
}


@dataclass
class PairKey:
    """Represents a key for import/export pairs."""

    loc_code: str
    index: int
    cop_no: str
    booking_no: str

    @classmethod
    def from_string(cls, key: str) -> "PairKey":
        """Create a PairKey object from a string key."""
        loc_code, index, cop_no, booking_no = key.split("_")
        return cls(loc_code, int(index), cop_no, booking_no)

    def to_string(self) -> str:
        """Convert the PairKey object to a string key."""
        return f"{self.loc_code}_{self.index}_{self.cop_no}_{self.booking_no}"


class RouteMatcher:
    def __init__(self):
        """Initializes the RouteMatcher object."""
        self.linear_solver = LinearSolver()
        self.df_recommended_routes = pd.DataFrame(columns=RM_COULMN_MAP.keys())
        self._distance_cache = {}  # Cache for storing distance pairs: (node_cd, import_loc_cd, export_loc_cd) -> (distance_door, distance_saved)

    @measure_time(logger=logger)
    def run(self, node_cd: str, data_coll: DataCollection, date_time: str):
        """Orchestrates the full process of matching imports and exports for a given CY node."""
        logger.info(f"Matching imports and exports for CY {node_cd}")

        same_cy = self.get_imports_exports(node_cd, data_coll)
        if same_cy.empty:
            logger.info(f"No data available for CY {node_cd}")
            return

        ib_candi, ob_candi = self.pre_filtering(same_cy, date_time)
        if ib_candi.empty or ob_candi.empty:
            logger.info(f"CY {node_cd} can't match because import = {len(ib_candi)} and export = {len(ob_candi)}")
            return

        ib_candi, ob_candi = self.add_location_city_codes(ib_candi, ob_candi, data_coll)

        raw_pairs, valid_cntz_sz_pairs, valid_datetime_pairs, raw_pairs_info, valid_distance_pairs = (
            self.create_candidate_pairs(ib_candi, ob_candi, data_coll, node_cd)
        )

        valid_pairs = self.filter_with_constraints(valid_cntz_sz_pairs, valid_datetime_pairs, valid_distance_pairs)

        logger.info(f"Number of non-overlapping imports: {len(ib_candi)} - exports: {len(ob_candi)}")
        return raw_pairs, valid_pairs, valid_cntz_sz_pairs, valid_datetime_pairs, raw_pairs_info, valid_distance_pairs

    def get_imports_exports(self, node_cd: str, data_coll: DataCollection) -> pd.DataFrame:
        """Extracts imports and exports for the given CY node."""
        return data_coll.report_data.query(f"`interchange_location`.str.match('^{node_cd}.*')", engine="python")

    def pre_filtering(self, same_cy: pd.DataFrame, date_time: str):
        """Filters out non-overlapping imports and exports based on time constraints."""
        non_overlapping_imports, non_overlapping_exports = self.filter_non_overlapping_imports_exports(
            same_cy, date_time
        )

        ib_candi = same_cy[(same_cy["bound"] == "IMPORT") & (~same_cy["cop_no"].isin(non_overlapping_imports))]
        ob_candi = same_cy[(same_cy["bound"] == "EXPORT") & (~same_cy["cop_no"].isin(non_overlapping_exports))]

        return ib_candi, ob_candi

    def add_location_city_codes(self, ib_candi: pd.DataFrame, ob_candi: pd.DataFrame, data_coll: DataCollection):
        """Adds city location codes to imports and exports."""
        ib_candi["loc_city_cd"] = ib_candi["location_city"].apply(
            lambda x: find_key_by_location_name(x, data_coll.location_data)
        )
        ob_candi["loc_city_cd"] = ob_candi["location_city"].apply(
            lambda x: find_key_by_location_name(x, data_coll.location_data)
        )
        return ib_candi, ob_candi

    def _process_chunk(self, chunk_data):
        """Process a chunk of import containers against all exports."""
        im_chunk, ob_candi, data_coll, node_cd = chunk_data
        chunk_results = []

        # Pre-calculate container sizes and dates for exports
        ex_sizes = {ex.Index: ex.container_size_type for ex in ob_candi.itertuples()}
        ex_dates = {ex.Index: (ex.export_first_receiving_date, ex.export_cut_off_date) for ex in ob_candi.itertuples()}

        for im in im_chunk.itertuples():
            im_id = f"{im.loc_city_cd}_{im.Index}_{im.cop_no}_{im.booking_no}"

            # Get import dates once
            im_delivery_date = im.estimated_import_delivery_date
            im_size = im.container_size_type

            compatible_exports = []
            for ex in ob_candi.itertuples():
                # Quick compatibility checks first
                if im_size != ex_sizes[ex.Index] and not (im_size in {"D4", "D5"} and ex_sizes[ex.Index] == "X45"):
                    continue

                ex_first_receiving, ex_cutoff = ex_dates[ex.Index]
                if not (ex_cutoff > im_delivery_date >= ex_first_receiving):
                    continue

                ex_id = f"{ex.loc_city_cd}_{ex.Index}_{ex.cop_no}_{ex.booking_no}"

                # Only do expensive distance check if other criteria pass
                is_pass_distance, distance_door, distance_saved = self.is_distance_compatible(
                    im.Index, ex.Index, data_coll, node_cd
                )

                if not is_pass_distance:
                    continue

                # If we get here, all checks passed
                compatible_exports.append(
                    {
                        "im_id": im_id,
                        "ex_id": ex_id,
                        "raw_pair": 0,
                        "cntz": 1,
                        "datetime": 1,
                        "distance": 1,
                        "info": {
                            "distance_door_door": distance_door,
                            "distance_saved": distance_saved,
                            "im_cntz_size": im_size,
                            "ex_cntz_size": ex_sizes[ex.Index],
                            "time_gap": (ex_first_receiving - im_delivery_date).days,
                        },
                    }
                )

            chunk_results.extend(compatible_exports)

        return chunk_results

    @measure_time(logger=logger)
    def create_candidate_pairs(
        self, ib_candi: pd.DataFrame, ob_candi: pd.DataFrame, data_coll: DataCollection, node_cd: str
    ):
        """Generates candidate pairs for imports and exports using optimized parallel processing."""
        # Initialize result dictionaries
        raw_pairs: dict[str, dict[str, int]] = {}
        raw_pairs_info: dict[typing.Any, dict[typing.Any, dict[str, typing.Any]]] = {}
        valid_cntz_sz_pairs: dict[str, dict[str, int]] = {}
        valid_datetime_pairs: dict[str, dict[str, int]] = {}
        valid_distance_pairs: dict[str, dict[str, int]] = {}

        # Initialize dictionaries for all import IDs first
        for im in ib_candi.itertuples():
            im_id = f"{im.loc_city_cd}_{im.Index}_{im.cop_no}_{im.booking_no}"
            raw_pairs[im_id] = {}
            raw_pairs_info[im_id] = {}
            valid_cntz_sz_pairs[im_id] = {}
            valid_datetime_pairs[im_id] = {}
            valid_distance_pairs[im_id] = {}

        # Split imports into chunks for parallel processing
        n_cores = multiprocessing.cpu_count()
        chunk_size = max(1, min(len(ib_candi) // n_cores, 10))  # Limit chunk size
        im_chunks = np.array_split(ib_candi, chunk_size)

        total_pairs = len(ib_candi) * len(ob_candi)
        logger.info(f"Processing {total_pairs} candidate pairs for node {node_cd} in {len(im_chunks)} chunks")

        progress_bar = tqdm(total=total_pairs, desc=f"Processing pairs for node {node_cd}")

        # Process chunks in parallel using processes instead of threads
        with ProcessPoolExecutor(max_workers=n_cores) as executor:
            futures = [
                executor.submit(self._process_chunk, (chunk, ob_candi, data_coll, node_cd)) for chunk in im_chunks
            ]

            # Process results as they complete
            for future in futures:
                chunk_results = future.result()
                progress_bar.update(len(chunk_results))

                # Update dictionaries with valid pairs
                for result in chunk_results:
                    im_id = result["im_id"]
                    ex_id = result["ex_id"]

                    raw_pairs[im_id][ex_id] = result["raw_pair"]
                    valid_cntz_sz_pairs[im_id][ex_id] = result["cntz"]
                    valid_datetime_pairs[im_id][ex_id] = result["datetime"]
                    valid_distance_pairs[im_id][ex_id] = result["distance"]
                    raw_pairs_info[im_id][ex_id] = result["info"]

        progress_bar.close()
        logger.info(f"Finished processing {total_pairs} candidate pairs for node {node_cd}")

        return raw_pairs, valid_cntz_sz_pairs, valid_datetime_pairs, raw_pairs_info, valid_distance_pairs

    @measure_time(logger=logger)
    def filter_with_constraints(self, valid_cntz_sz_pairs, valid_datetime_pairs, valid_distance_pairs):
        """Filter out pairs that do not meet the constraints."""
        valid_pairs = {}
        for key in valid_cntz_sz_pairs.keys():
            valid_pairs[key] = {
                sub_key: 1
                for sub_key in valid_cntz_sz_pairs[key]
                if valid_cntz_sz_pairs[key][sub_key] == 1
                and valid_datetime_pairs[key][sub_key] == 1
                and valid_distance_pairs[key][sub_key] == 1
            }
        return valid_pairs

    @measure_time(logger=logger)
    def lp_solver(
        self,
        data_coll: DataCollection,
        valid_pairs: dict,
        raw_pairs_info: dict,
        node_cd: str,
        n_ex: int,
        n_im: int,
        criteria: str,
        save_dir: Path,
    ):
        """Call the linear solver to solve the LP problem.

        Args:
            data_coll (DataCollection): DataCollection object containing all necessary data.
            valid_pairs (dict): Dictionary with imports as keys and dict of exports as values.
                                Format: {'im1': {'ex1': 1, 'ex2': 1, ...}, 'im2': {'ex1': 1, ...}, ...}
            raw_pairs_info (dict): Dictionary containing raw pairs information.
            node_cd (str): Location code for the current node.
            n_ex (int): Number of exports to match.
            n_im (int): Number of imports to match.
            criteria (str): Criteria for calculating savings.
            distance_method (str): Method to use for distance calculation.
            save_dir (Path): Path to save the results.

        Returns:
            tuple:
                - dict: Dictionary containing the matches.
                - dict: Dictionary containing distance pairs.
                - dict: Dictionary containing distance pair information.
        """
        # Calculate s()avings only for compatible imports and exports
        savings = self.calculate_savings_matrix(
            data_coll=data_coll,
            valid_paris=valid_pairs,
            criteria=criteria,
            raw_pairs_info=raw_pairs_info,
        )

        # Solve linear programming problem if there are any valid savings
        if savings:
            matches = self.linear_solver.solve(
                valid_pairs=valid_pairs, savings=savings, n_im=n_im, n_ex=n_ex, save_dir=save_dir
            )
            return matches, savings
        else:
            logger.warning(f"No compatible matches found for location {node_cd}.")
            return None, savings

    @measure_time(logger=logger)
    def calculate_savings_matrix(
        self,
        data_coll: DataCollection,
        valid_paris: dict,
        criteria: str,
        raw_pairs_info: dict,
    ):
        """Calculate the savings based on preloaded distance data from BigQuery.

        Args:
            data_coll (DataCollection): DataCollection object containing all necessary data.
            valid_paris (dict): Dictionary with imports as keys and dict of exports as values.
                                Format: {'im1': {'ex1': 1, 'ex2': 1, ...}, 'im2': {'ex1': 1, ...}, ...}
            val_distance_pairs_info (dict): __description__
            raw_pairs_info (dict): Dictionary containing raw pairs information.
            criteria (str): Criteria for calculating savings.
            recommended_results (dict): Dictionary containing recommended results for each import-export pair.

        Returns:
            dict: Dictionary with key as (import, export) pair and value as the calculated savings.
        """
        savings: dict = {}

        for im_id, export_dict in valid_paris.items():
            im_index = int(im_id.split("_")[1])
            for ex_id, is_valid in export_dict.items():
                if not is_valid:  # Skip invalid pairs
                    continue

                ex_index = int(ex_id.split("_")[1])

                im_name_street = data_coll.report_data.iloc[im_index]["street_address"]
                im_name_city = data_coll.location_data[im_id.split("_")[0]]["location_name"]

                ex_name_street = data_coll.report_data.iloc[ex_index]["street_address"]
                ex_name_city = data_coll.location_data[ex_id.split("_")[0]]["location_name"]

                distance_door = raw_pairs_info[im_id][ex_id]["distance_door_door"]

                if distance_door == ConstMap.DEFAULT_DISTANCE:
                    logger.info(
                        f"No distance for pair: {im_name_street}--{ex_name_street} and {im_name_city}--{ex_name_city}"
                    )
                    continue
                if int(distance_door) > settings.SYSTEM.DISTANCE_THRES_METERS:
                    # TODO: add convert to miles
                    logger.info(
                        f"Distance between IMPORT and EXPORT = {int(distance_door)} > {settings.SYSTEM.DISTANCE_THRES_METERS / 1000} km(400 miles): {im_name_street}--{ex_name_street} and {im_name_city}--{ex_name_city}"
                    )
                    continue

                if criteria == "distance":
                    savings[(im_id, ex_id)] = distance_door
                # elif criteria == "distance_cost":
                #     savings[(im_id, ex_id)] = log_scale(distance_door) + log_scale()
                # elif criteria == "cost":
                #     savings[(im_id, ex_id)] =

        return savings

    def _get_distance(
        self,
        im_name_street: str,
        ex_name_street: str,
        im_name_city: str,
        ex_name_city: str,
        distance_data,
        distance_method: str,
    ):
        """Helper function to calculate distance based on location details.

        Args:
            im_name_street (str): Street address of the import location.
            ex_name_street (str): Street address of the export location.
            im_name_city (str): City name of the import location.
            ex_name_city (str): City name of the export location.
            distance_data (pd.DataFrame): DataFrame containing distance data.
            distance_method (str): Method to use for distance calculation.

        Returns:
            float: Calculated distance or default distance if not found.
        """
        distance: float = get_distance_between_locations(im_name_street, ex_name_street, distance_data, distance_method)
        if distance == ConstMap.DEFAULT_DISTANCE:
            distance = get_distance_between_locations(im_name_street, ex_name_city, distance_data, distance_method)
        if distance == ConstMap.DEFAULT_DISTANCE:
            distance = get_distance_between_locations(ex_name_street, im_name_city, distance_data, distance_method)
        if distance == ConstMap.DEFAULT_DISTANCE:
            distance = get_distance_between_locations(im_name_city, ex_name_city, distance_data, distance_method)
        if distance == ConstMap.DEFAULT_DISTANCE:
            # Fallback to external API
            full_im_name = f"{im_name_street or ''}, {im_name_city}"
            full_ex_name = f"{ex_name_street or ''}, {ex_name_city}"
            try:
                distance = get_distance_in_meter(full_im_name, full_ex_name, distance_method)
            except GMapsAPIError as e:
                logger.error(
                    f"Error fetching distance from Google Maps API for full_im_name {full_im_name}, full_ex_name {full_ex_name}.\nError: {e}",
                    exc_info=True,
                )
                distance = ConstMap.DEFAULT_DISTANCE

        return distance

    def add_recommended_route(self, im_index, ex_index, report_data, cnt_size_map_data):
        """Adds the matching import-export pair to the recommended routes dataframe."""
        overlap_diff_days = (
            report_data.iloc[ex_index]["export_first_receiving_date"]
            - report_data.iloc[im_index]["estimated_import_delivery_date"]
        ).days

        new_row = {
            "import_bkg_no": report_data.iloc[im_index]["booking_no"],
            "import_cop_no": report_data.iloc[im_index]["cop_no"],
            "import_cntr_no": report_data.iloc[im_index]["container_no"],
            "export_bkg_no": report_data.iloc[ex_index]["booking_no"],
            "export_cop_no": report_data.iloc[ex_index]["cop_no"],
            "export_cntr_no": report_data.iloc[im_index]["container_no"],
            "size_type": cnt_size_map_data[report_data.iloc[im_index]["container_size_type"]],
            "shipline": "ONE",
            "import_door_city": report_data.iloc[im_index]["location_city"],
            "import_door_state": report_data.iloc[im_index]["location_state"],
            "import_port_ramp": report_data.iloc[im_index]["interchange_location"],
            "import_address": report_data.iloc[im_index]["street_address"],
            "import_shipper": report_data.iloc[im_index]["shipper__consignee_name"],
            "import_trucker": report_data.iloc[im_index]["truck_company"],
            "export_door_city": report_data.iloc[ex_index]["location_city"],
            "export_door_state": report_data.iloc[ex_index]["location_state"],
            "export_port_ramp": report_data.iloc[ex_index]["interchange_location"],
            "export_address": report_data.iloc[ex_index]["street_address"],
            "export_shipper": report_data.iloc[ex_index]["shipper__consignee_name"],
            "export_trucker": report_data.iloc[ex_index]["truck_company"],
            "import_available_date": report_data.iloc[im_index]["import_availability_at_final_cy"],
            "import_expire_date": report_data.iloc[im_index]["estimated_import_delivery_date"],
            "export_available_date": report_data.iloc[ex_index]["export_first_receiving_date"],
            "export_expire_date": report_data.iloc[ex_index]["export_cut_off_date"],
            "overlaps": "Y" if overlap_diff_days < 0 else "N",
            "overlap_difference_days": overlap_diff_days,
            "distance_between_import_export": 1197,
            "street_turn_distance": 1197,
            "round_trip_distance": 1197,
            "distance_saved": 1197,
        }

        self.df_recommended_routes = pd.concat([self.df_recommended_routes, pd.DataFrame([new_row])], ignore_index=True)

    def is_container_size_compatible(self, im_index, ex_index, report_data):
        """Checks if container sizes between import and export are compatible."""
        import_size = report_data.loc[im_index, "container_size_type"]
        export_size = report_data.loc[ex_index, "container_size_type"]

        return (
            import_size == export_size or (import_size in {"D4", "D5"} and export_size == "X45"),
            import_size,
            export_size,
        )

    def is_date_compatible(self, im_index, ex_index, report_data):
        """Checks if the dates between import and export are compatible for matching."""
        export_cutoff = report_data.loc[ex_index, "export_cut_off_date"]
        estimated_import_delivery = report_data.loc[im_index, "estimated_import_delivery_date"]
        export_first_receiving = report_data.loc[ex_index, "export_first_receiving_date"]
        if pd.notna(export_cutoff) and pd.notna(estimated_import_delivery) and pd.notna(export_first_receiving):
            overlap_days = (export_first_receiving - estimated_import_delivery).days
            return export_cutoff > estimated_import_delivery >= export_first_receiving, overlap_days
        return False, 0

    def is_distance_compatible(self, im_index, ex_index, data, node_cd):
        """Checks if the distance between import and export is within the threshold."""
        distance_data = data.distance_data
        location_data = data.location_data
        report_data = data.report_data
        is_pass_distance = True

        im_loc_cd = find_key_by_location_name(report_data.loc[im_index, "location_city"], location_data)
        ex_loc_cd = find_key_by_location_name(report_data.loc[ex_index, "location_city"], location_data)

        im_address = report_data.loc[im_index, "street_address"]
        ex_address = report_data.loc[ex_index, "street_address"]

        # Check cache first
        cache_key = (node_cd, im_loc_cd, ex_loc_cd)
        if cache_key in self._distance_cache:
            distance_door, distance_saved = self._distance_cache[cache_key]
        else:
            # Filter distance data if not in cache
            matching_distances = distance_data[
                (distance_data["port"] == node_cd)
                & (distance_data["import_loc_cd"] == im_loc_cd)
                & (distance_data["export_loc_cd"] == ex_loc_cd)
            ]

            # Check if we found matching distance records
            if matching_distances.empty:
                logger.warning(
                    f"No distance data found for port={node_cd}, import_loc={im_loc_cd}, export_loc={ex_loc_cd}. "
                    f"Addresses: {im_address}--{ex_address}"
                )
                # return False, 0, 0
            #     # Store values and update cache
            #     distance_door = matching_distances["distance_door_door"].values[0]
            #     distance_saved = matching_distances["distance_saved"].values[0]
            distance_door = random.randint(0, 200)  # noqa: S311
            distance_saved = random.randint(0, 200)  # noqa: S311

            self._distance_cache[cache_key] = (distance_door, distance_saved)

        if int(distance_door) > settings.SYSTEM.DISTANCE_THRES_METERS:
            is_pass_distance = False
            logger.info(
                f"Distance between IMPORT and EXPORT = {int(distance_door)} > {settings.SYSTEM.DISTANCE_THRES_METERS / 1000} km(400 miles): "
                f"{im_address}--{ex_address} and {im_loc_cd}--{ex_loc_cd}"
            )

        return is_pass_distance, distance_door, distance_saved

    def save_matches(self, matches: dict[str, dict], output_fpath: Path):
        """_summary_."""
        tmp = {}
        for k, v in matches.items():
            tmp[k] = {kk: vv.to_dict() for kk, vv in v.items()}
        save_json(tmp, output_fpath)

    def save_results_to_csv(self, file_path: Path):
        """Saves the recommended routes to a CSV file."""
        self.df_recommended_routes.rename(columns=RM_COULMN_MAP, inplace=True)
        self.df_recommended_routes.to_csv(file_path, index=False)

    def save_metadata_storage(self, metadata_storage, output_file_path, file_format="pickle"):
        """Save the entire metadata_storage to a single file.

        Args:
            metadata_storage (dict): The metadata storage dictionary.
            output_file_path (str): Path to the output file.
            file_format (str): Format to save the file ('pickle' or 'parquet').
        """
        if file_format == "pickle":
            with open(output_file_path, "wb") as file:
                pickle.dump(metadata_storage, file)
            print(f"Metadata storage saved as Pickle file at: {output_file_path}")

        elif file_format == "parquet":
            # Convert the metadata into a single Pandas DataFrame
            data_list = []
            for key, value in metadata_storage.items():
                for export_key, metadata in value.items():
                    row = {
                        "import_key": key,
                        "export_key": export_key,
                        "im_rl": metadata["im_rl"].to_dict()
                        if isinstance(metadata["im_rl"], pd.DataFrame)
                        else metadata["im_rl"],
                        "ex_rl": metadata["ex_rl"].to_dict()
                        if isinstance(metadata["ex_rl"], pd.DataFrame)
                        else metadata["ex_rl"],
                        "im_rl_picked": metadata["im_rl_picked"].to_dict()
                        if isinstance(metadata["im_rl_picked"], pd.DataFrame)
                        else metadata["im_rl_picked"],
                        "ex_rl_picked": metadata["ex_rl_picked"].to_dict()
                        if isinstance(metadata["ex_rl_picked"], pd.DataFrame)
                        else metadata["ex_rl_picked"],
                        "other_metadata": {k: v for k, v in metadata.items() if k not in ["im_rl", "ex_rl"]},
                    }
                    data_list.append(row)

            df = pd.DataFrame(data_list)
            df.to_parquet(output_file_path, index=False)
            print(f"Metadata storage saved as Parquet file at: {output_file_path}")

        else:
            raise ValueError("Unsupported file format. Use 'pickle' or 'parquet'.")

    def filter_non_overlapping_imports_exports(self, data, date_time):
        """Filters and returns lists of non-overlapping imports and exports.

        Args:
            data (pd.DataFrame): The input data containing booking information.
            date_time (str): Current date in the format 'YYYY-MM-DDTHH:MM:SS'.
            node_cd (str): Location code for filtering.

        Returns:
            tuple: (list of non-overlapping import cop_no, list of non-overlapping export cop_no)
        """
        current_date = datetime.strptime(date_time, "%Y-%m-%dT%H:%M:%S")

        # Dictionary to store bookings
        bookings = {}

        # Process imports and exports
        for _, row in data.iterrows():
            if row["bound"] == "IMPORT":
                if current_date > datetime.strptime(str(row["estimated_import_delivery_date"]), "%Y-%m-%d %H:%M:%S"):
                    continue
                bookings[row["cop_no"]] = [
                    datetime.strptime(str(row["import_availability_at_final_cy"]), "%Y-%m-%d %H:%M:%S"),
                    datetime.strptime(str(row["estimated_import_delivery_date"]), "%Y-%m-%d %H:%M:%S"),
                    0,  # Marker for import
                ]
            elif row["bound"] == "EXPORT":
                if current_date > datetime.strptime(str(row["export_cut_off_date"]), "%Y-%m-%d %H:%M:%S"):
                    continue
                bookings[row["cop_no"]] = [
                    datetime.strptime(str(row["export_first_receiving_date"]), "%Y-%m-%d %H:%M:%S"),
                    datetime.strptime(str(row["export_cut_off_date"]), "%Y-%m-%d %H:%M:%S"),
                    1,  # Marker for export
                ]

        # Sort bookings by start date
        sorted_bookings = sorted(bookings.items(), key=lambda x: x[1][0])

        # Separate into imports and exports
        imports = [b for b in sorted_bookings if b[1][2] == 0]
        exports = [b for b in sorted_bookings if b[1][2] == 1]

        # Filter non-overlapping imports and exports
        non_overlapping_imports = []
        non_overlapping_exports = []

        for imp in imports:
            if all(imp[1][1] <= exp[1][0] or imp[1][0] >= exp[1][1] for exp in exports):
                non_overlapping_imports.append(imp[0])  # Append cop_no

        for exp in exports:
            if all(exp[1][1] <= imp[1][0] or exp[1][0] >= imp[1][1] for imp in imports):
                non_overlapping_exports.append(exp[0])  # Append cop_no

        return non_overlapping_imports, non_overlapping_exports
