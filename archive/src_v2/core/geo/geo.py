import math
from enum import Enum

import numpy as np
from geopy.distance import geodesic, great_circle

from archive.src_v2.const import ConstMap


class DistanceUnit(Enum):
    KM = "km"
    MILES = "miles"
    NAUTICAL_MILES = "nautical_miles"


def _parse_unit(unit: str | DistanceUnit) -> DistanceUnit:
    """Convert string unit to DistanceUnit enum."""
    if isinstance(unit, DistanceUnit):
        return unit

    unit_map = {
        "km": DistanceUnit.KM,
        "kilometers": DistanceUnit.KM,
        "mi": DistanceUnit.MILES,
        "miles": DistanceUnit.MILES,
        "nm": DistanceUnit.NAUTICAL_MILES,
        "nautical": DistanceUnit.NAUTICAL_MILES,
    }

    try:
        return unit_map[unit.lower()]
    except KeyError:
        valid_units = ", ".join(unit_map.keys())
        raise ValueError(f"Invalid unit. Use one of: {valid_units}") from None


def convert_distance(distance_km: float, unit: str | DistanceUnit) -> float:
    """Convert distance from kilometers to specified unit."""
    unit_enum = _parse_unit(unit)
    conversion_factors = {DistanceUnit.KM: 1.0, DistanceUnit.MILES: 0.621371, DistanceUnit.NAUTICAL_MILES: 0.539957}
    return distance_km * conversion_factors[unit_enum]


def geopy_distance(
    loc1: tuple[float, float], loc2: tuple[float, float], unit: str | DistanceUnit = DistanceUnit.KM
) -> float:
    """Calculate the distance between two points using geopy library.

    Args:
        loc1: tuple of (latitude, longitude) for first location in decimal degrees
        loc2: tuple of (latitude, longitude) for second location in decimal degrees
        unit: Distance unit ("km", "mi", "nm") or DistanceUnit enum

    Returns:
        Distance between points in specified unit
    """
    distance = geodesic(loc1, loc2)
    if unit == DistanceUnit.KM or unit == "km" or unit == "kilometers":
        return distance.km
    elif unit == DistanceUnit.MILES or unit == "mi" or unit == "miles":
        return distance.miles
    elif unit == DistanceUnit.NAUTICAL_MILES or unit == "nm" or unit == "nautical":
        return distance.nautical
    else:
        raise ValueError("Invalid unit. Use one of: km, mi, nm")


def haversine_distance_v3(
    loc1: tuple[float, float], loc2: tuple[float, float], unit: str | DistanceUnit = DistanceUnit.KM
) -> float:
    """Calculate the great circle distance between two points on Earth using Haversine formula.

    Args:
        loc1: tuple of (latitude, longitude) for first location in decimal degrees
        loc2: tuple of (latitude, longitude) for second location in decimal degrees
        unit: Distance unit ("km", "mi", "nm") or DistanceUnit enum

    Returns:
        Distance between points in specified unit
    """
    distance = great_circle(loc1, loc2)
    if unit == DistanceUnit.KM or unit == "km" or unit == "kilometers":
        return distance.km
    elif unit == DistanceUnit.MILES or unit == "mi" or unit == "miles":
        return distance.miles
    elif unit == DistanceUnit.NAUTICAL_MILES or unit == "nm" or unit == "nautical":
        return distance.nautical
    else:
        raise ValueError("Invalid unit. Use one of: km, mi, nm")


def haversine_distance_v2(
    loc1: tuple[float, float], loc2: tuple[float, float], unit: DistanceUnit = DistanceUnit.KM
) -> float:
    """Calculate the great circle distance between two points on Earth using Haversine formula.

    Args:
        loc1: tuple of (latitude, longitude) for first location in decimal degrees
        loc2: tuple of (latitude, longitude) for second location in decimal degrees
        unit: Distance unit (km, miles, or nautical miles)

    Returns:
        Distance between points in specified unit
    """
    dlat = np.radians(loc2[0] - loc1[0])
    dlon = np.radians(loc2[1] - loc1[1])
    a = np.sin(dlat / 2) ** 2 + np.cos(np.radians(loc1[0])) * np.cos(np.radians(loc2[0])) * np.sin(dlon / 2) ** 2
    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
    distance_km = ConstMap.EARTH_RADIUS * c
    return convert_distance(distance_km, unit)


def haversine_distance(
    loc1: tuple[float, float], loc2: tuple[float, float], unit: str | DistanceUnit = DistanceUnit.KM
) -> float:
    """Calculate the great circle distance between two points using Haversine formula.

    Args:
        loc1: tuple of (latitude, longitude) for first location in decimal degrees
        loc2: tuple of (latitude, longitude) for second location in decimal degrees
        unit: Distance unit ("km", "mi", "nm") or DistanceUnit enum

    Returns:
        Distance between points in specified unit
    """
    lat1, lon1 = math.radians(loc1[0]), math.radians(loc1[1])
    lat2, lon2 = math.radians(loc2[0]), math.radians(loc2[1])

    dlat = lat2 - lat1
    dlon = lon2 - lon1

    a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    c = 2 * math.asin(math.sqrt(a))

    distance_km = ConstMap.EARTH_RADIUS * c
    return convert_distance(distance_km, unit)


def vincenty_distance(
    loc1: tuple[float, float],
    loc2: tuple[float, float],
    unit: str | DistanceUnit = DistanceUnit.KM,
    iterations: int = 100,
    tolerance: float = 1e-12,
) -> float | None:
    """Calculate the distance between two points using Vincenty's formula.

    Args:
        loc1: tuple of (latitude, longitude) for first location in decimal degrees
        loc2: tuple of (latitude, longitude) for second location in decimal degrees
        unit: Distance unit ("km", "mi", "nm") or DistanceUnit enum
        iterations: Maximum number of iterations for convergence
        tolerance: Convergence tolerance

    Returns:
        Distance between points in specified unit, or None if algorithm doesn't converge
    """
    # WGS-84 ellipsoid parameters
    a = 6378137.0  # semi-major axis in meters
    f = 1 / 298.257223563  # flattening
    b = a * (1 - f)  # semi-minor axis

    lat1, lon1 = math.radians(loc1[0]), math.radians(loc1[1])
    lat2, lon2 = math.radians(loc2[0]), math.radians(loc2[1])

    if abs(lon2 - lon1) == math.pi and abs(lat2 + lat1) == 0:
        return None

    _u1 = math.atan((1 - f) * math.tan(lat1))
    _u2 = math.atan((1 - f) * math.tan(lat2))
    _l = lon2 - lon1

    sin_u1, cos_u1 = math.sin(_u1), math.cos(_u1)
    sin_u2, cos_u2 = math.sin(_u2), math.cos(_u2)

    lambda_old = _l

    for _ in range(iterations):
        sin_lambda = math.sin(lambda_old)
        cos_lambda = math.cos(lambda_old)

        sin_sigma = math.sqrt((cos_u2 * sin_lambda) ** 2 + (cos_u1 * sin_u2 - sin_u1 * cos_u2 * cos_lambda) ** 2)

        if sin_sigma == 0:
            return 0.0

        cos_sigma = sin_u1 * sin_u2 + cos_u1 * cos_u2 * cos_lambda
        sigma = math.atan2(sin_sigma, cos_sigma)

        sin_alpha = cos_u1 * cos_u2 * sin_lambda / sin_sigma
        cos2_alpha = 1 - sin_alpha**2

        if cos2_alpha == 0:
            cos2_sigma_m = 0.0
        else:
            cos2_sigma_m = cos_sigma - 2 * sin_u1 * sin_u2 / cos2_alpha

        _c = f / 16 * cos2_alpha * (4 + f * (4 - 3 * cos2_alpha))

        lambda_new = _l + (1 - _c) * f * sin_alpha * (
            sigma + _c * sin_sigma * (cos2_sigma_m + _c * cos_sigma * (-1 + 2 * cos2_sigma_m**2))
        )

        if abs(lambda_new - lambda_old) < tolerance:
            u2 = cos2_alpha * (a**2 - b**2) / b**2
            _a = 1 + u2 / 16384 * (4096 + u2 * (-768 + u2 * (320 - 175 * u2)))
            _b = u2 / 1024 * (256 + u2 * (-128 + u2 * (74 - 47 * u2)))

            delta_sigma = (
                _b
                * sin_sigma
                * (
                    cos2_sigma_m
                    + _b
                    / 4
                    * (
                        cos_sigma * (-1 + 2 * cos2_sigma_m**2)
                        - _b / 6 * cos2_sigma_m * (-3 + 4 * sin_sigma**2) * (-3 + 4 * cos2_sigma_m**2)
                    )
                )
            )

            distance_meters = b * _a * (sigma - delta_sigma)
            distance_km = distance_meters / 1000
            return convert_distance(distance_km, unit)

        lambda_old = lambda_new

    return None  # No convergence
