from pathlib import Path

import pandas as pd

from archive.src_v2.const.config import settings
from archive.src_v2.thirdparty.gcp import BigQueryManager
from archive.src_v2.utils.decorators import measure_time
from archive.src_v2.utils.logger import log_handler

from .base_loader import DataLoaderBase

logger = log_handler.get_logger(name=__name__)


class DistanceDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the DistanceDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self.distance_data: pd.DataFrame = None  # type: ignore [assignment]

    @measure_time(logger=logger)
    def fetch_data(self) -> None:
        """Fetch distance and yard data from BigQuery."""
        query = """
        SELECT *
        FROM `{table_name}`
        --WHERE distance is NOT NULL
        """
        table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.DATAPIPELINE_TABLE_ID}.{settings.DB.DIST_TABLE_ID}"
        query = query.format(table_name=table_name)
        self.distance_data = self.bq_manager.execute_query(query)

    @staticmethod
    def _clean_distance_column(df: pd.DataFrame, column_name: str = "distance") -> pd.DataFrame:
        """Cleans and converts the distance column from a string to a float, removing commas.

        Args:
            df (pd.DataFrame): The DataFrame containing the distance column.
            column_name (str): The name of the column to clean (default is 'Distance').

        Returns:
            pd.DataFrame: The updated DataFrame with the cleaned distance column.
        """
        df[column_name] = df[column_name].astype(str).str.replace(",", "").str.extract(r"(\d+\.?\d*)").astype(float)
        return df

    @measure_time(logger=logger)
    def process_data(self) -> None:
        """Process the fetched distance data, converting distances to numeric values."""
        if self.distance_data is None:
            raise ValueError("Distance data not fetched. Call fetch_data() first.")

        distance_columns = ["distance_port_import", "distance_port_export", "distance_door_door", "distance_saved"]

        for column in distance_columns:
            self.distance_data = self._clean_distance_column(self.distance_data, column)

        logger.info("Distance data loaded successfully.")

    def get_data(self) -> pd.DataFrame:
        """Return the processed distance data as a DataFrame.

        Returns:
            pd.DataFrame: The processed distance data.
        """
        if self.distance_data is None:
            raise ValueError("Distance data not available. Call fetch_data() first.")
        return self.distance_data

    @measure_time(logger=logger)
    def save_data(self, save_dir: Path) -> None:
        """Save the distance data to a file.

        Args:
            save_dir (Path): The directory to save the data to.
        """
        if self.distance_data is None:
            raise ValueError("Data not loaded. Call fetch_data() first.")
        self.distance_data.to_csv(save_dir / "distance_data.csv", index=False)
        logger.info("Distance data saved to distance_data.csv.")

    @measure_time(logger=logger)
    def load_data(self, save_dir: Path) -> None:
        """Load the distance data from a file.

        Args:
            save_dir (Path): The directory to load the data from.
        """
        if not (save_dir / "distance_data.csv").exists():
            raise FileNotFoundError("Distance data file not found. Call save_data() first.")
        self.distance_data = pd.read_csv(save_dir / "distance_data.csv")
        logger.info("Distance data loaded from distance_data.csv.")


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = DistanceDataLoader(bq_manager)
    loader.fetch_data()
    loader.process_data()
    distance_data = loader.get_data()
