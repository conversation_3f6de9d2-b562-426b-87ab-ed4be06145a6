from pathlib import Path

import pandas as pd

from archive.src_v2.const.config import settings
from archive.src_v2.core.data_loader.rl_mapping_loader import RateLaneMappingLoader
from archive.src_v2.thirdparty.gcp import BigQueryManager
from archive.src_v2.utils.decorators import measure_time
from archive.src_v2.utils.logger import log_handler
from root import TMP_DIR

from .cnt_sz_loader import ContainerSizeDataLoader
from .distance_loader import DistanceDataLoader
from .loc_loader import LocationDataLoader
from .rl_loader import RateLaneLoader
from .rpt_loader import ReportDataLoader
from .utils import find_key_by_location_name

logger = log_handler.get_logger(name=__name__)


class DataLoader:
    def __init__(self, cfg: dict, save_dir: Path = Path(".")):
        """Initialize the DataLoader with a BigQuery manager and configuration.

        Args:
            cfg (dict): The configuration for the data loaders.
            save_dir (Path, optional): The directory to save the data. Defaults to Path(".").
        """
        bq_manager = BigQueryManager()

        self.cfg = cfg
        self.loaders = {
            "loc": LocationDataLoader(bq_manager),
            "cnt_sz": ContainerSizeDataLoader(bq_manager),
            "rl": RateLaneLoader(bq_manager),
            "rl_mapping": RateLaneMappingLoader(bq_manager),
            "rpt": ReportDataLoader(bq_manager),
            "dis": DistanceDataLoader(bq_manager),
        }

        self.save_dir = save_dir
        self.save_dir.mkdir(parents=True, exist_ok=True)

    @measure_time(logger=logger)
    def fetch_data(self) -> None:
        """Fetch data from BigQuery using the loaders."""
        for key, loader in self.loaders.items():
            if settings.ENV.ENVIRONMENT == 0:
                try:
                    loader.load_data(save_dir=TMP_DIR)
                except FileNotFoundError:
                    loader.fetch_data(**self.cfg.get(key, {}))
                    loader.process_data()
                    loader.save_data(save_dir=TMP_DIR)
                    loader.save_data(save_dir=self.save_dir)
            else:
                loader.fetch_data(**self.cfg.get(key, {}))
                loader.process_data()
                loader.save_data(save_dir=self.save_dir)

                if settings.ENV.ENVIRONMENT == 0:
                    loader.save_data(save_dir=TMP_DIR)

    def get_data(self, data_type: str):
        """Return the data for a specific data type.

        Args:
            data_type (str): The type of data to return. Must be one of the keys in the loaders dict: `loc`, `cnt_sz`, `rl`, `rpt`, `dis`

        Returns:
            pd.DataFrame: The data for the specified type.
        """
        if data_type not in self.loaders:
            raise ValueError(f"Invalid data type: {data_type}, must be one of {list(self.loaders.keys())}")

        res = self.loaders[data_type].get_data()
        if res is None:
            msg = f"No data found for {data_type} or {data_type} not loaded. Ensure fetch_data() and process_data() are called."
            logger.error(msg, exc_info=True)
            raise ValueError(msg)

        return res

    def process_report_data(self, report_data: pd.DataFrame, location_dict: dict) -> pd.DataFrame:
        """Process and validate report data."""
        report_data["loc_city_cd"] = report_data["location_city"].apply(
            lambda x: find_key_by_location_name(location_name=x, locations_dict=location_dict)
        )

        if report_data["loc_city_cd"].isnull().any():
            raise ValueError("Missing location city code in report data")

        return report_data


# if __name__ == "__main__":
#     data_cfg = {
#         "loc": {"data_source": f"{settings.ENV.PROJECT_ID}.SOURCE_DATA"},
#         "cnt_sz": {"data_source": f"{settings.ENV.PROJECT_ID}.RATE_LANE"},
#         "rl": {"data_source": f"{settings.ENV.PROJECT_ID}.RATE_LANE", "filter_date": None},
#         "rpt": {
#             "data_source": f"{settings.ENV.PROJECT_ID}.REPORT",
#             "run_dt": None,
#             "door_cy_value": "Door",
#             "hazmat_value": "N",
#             "has_customer_nominated_trucker": False,
#             "drop_and_pick_value": "N",
#         },
#         "dis": {"data_source": f"{settings.ENV.PROJECT_ID}.REPORT"},
#     }
#     loader = DataLoader(data_cfg)
#     loader.fetch_data()
