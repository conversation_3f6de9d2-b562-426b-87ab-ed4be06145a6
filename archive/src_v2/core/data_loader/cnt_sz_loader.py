import json
from pathlib import Path

import pandas as pd

from archive.src_v2.const.config import settings
from archive.src_v2.thirdparty.gcp import BigQueryManager
from archive.src_v2.utils.decorators import measure_time
from archive.src_v2.utils.logger import log_handler

from .base_loader import DataLoaderBase
from .col_map import CONTAINER_SIZE_MAP

logger = log_handler.get_logger(name=__name__)


class ContainerSizeDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the ContainerSizeDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self._cnt_data: pd.DataFrame = None  # type: ignore [assignment]
        self.cnt_size_map_dict: dict = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"size_mapping": CONTAINER_SIZE_MAP}

    @measure_time(logger=logger)
    def fetch_data(self) -> None:
        """Fetch location and yard data from BigQuery."""
        query = """
        SELECT *
        FROM `{table_name}`
        """
        table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.RATELANE_DATASET_ID}.{settings.DB.CNTR_SZ_TABLE_ID}"
        query = query.format(table_name=table_name)

        self._cnt_data = self.bq_manager.execute_query(query)

    @measure_time(logger=logger)
    def process_data(self) -> None:
        """Process the fetched data and create the location dictionary."""
        if self._cnt_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self.cnt_size_map_dict = self._cnt_data.set_index("sizetype")["mapsize"].to_dict()

        additional_mappings = {"X45": "40HC", "B4": "B4", "O5": "50OT"}
        self.cnt_size_map_dict.update(additional_mappings)

        logger.info("Container size mapping loaded successfully.")

    def get_data(self) -> dict:
        """Return the container size mapping dictionary.

        Returns:
            dict: A dictionary mapping size types to map sizes
        """
        if self.cnt_size_map_dict is None:
            raise ValueError("Size mapping not loaded. Call process_data() first.")
        return self.cnt_size_map_dict

    @measure_time(logger=logger)
    def save_data(self, save_dir: Path):
        """Save the container size mapping to a file.

        Args:
            save_dir (Path): The directory to save the file to.
        """
        if self.cnt_size_map_dict is None:
            raise ValueError("Size mapping not loaded. Call process_data() first.")
        with open(save_dir / "container_size_mapping.json", "w") as f:
            json.dump(self.cnt_size_map_dict, f)
        logger.info("Container size mapping saved to container_size_mapping.json.")

    @measure_time(logger=logger)
    def load_data(self, save_dir: Path) -> None:
        """Load the container size mapping from a file.

        Args:
            save_dir (Path): The directory to load the file from
        """
        if not (save_dir / "container_size_mapping.json").exists():
            raise FileNotFoundError("container_size_mapping.json not found. Call save_data() first.")
        with open(save_dir / "container_size_mapping.json") as f:
            self.cnt_size_map_dict = json.load(f)
        logger.info("Container size mapping loaded from container_size_mapping.json.")


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = ContainerSizeDataLoader(bq_manager)
    loader.fetch_data()
    loader.process_data()
    cnt_size_map_dict = loader.get_data()
