from pathlib import Path

import numpy as np
import pandas as pd

from archive.src_v2.const.config import settings
from archive.src_v2.thirdparty.gcp import BigQueryManager
from archive.src_v2.utils.decorators import measure_time
from archive.src_v2.utils.logger import log_handler

from .base_loader import DataLoaderBase
from .col_map import RATELANE_MAP

logger = log_handler.get_logger(name=__name__)


class RateLaneLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the RateLaneDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self._ratelane_data: pd.DataFrame = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"rate_lane": RATELANE_MAP}

    @measure_time(logger=logger)
    def fetch_data(self, filter_date: str | None = None, node_cd: tuple[str, ...] = ()) -> None:
        """Fetch rate lane data from BigQuery.

        Args:
            filter_date (str):The date to filter ExpiryDate. If None, uses today's date in UTC.
            node_cd (tuple[str, ...]): A tuple of specific container types to filter by.
        """
        query = """
        SELECT DISTINCT {col}
        FROM `{table_name}`('{date}')
        WHERE 1=1
        """
        if node_cd:
            patterns = [self._generate_location_pattern(loc) for loc in node_cd]
            regex_conditions = " OR ".join(f"REGEXP_CONTAINS(LANE_DESCRIPTION, r'{pattern}')" for pattern in patterns)
            query += f"AND ({regex_conditions})\n"

        table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.DATAPIPELINE_TABLE_ID}.{settings.DB.RATELANE_TABLE_ID}"
        query = query.format(col=self._generate_select_clause("rate_lane"), table_name=table_name, date=filter_date)
        logger.info(f"Fetching rate lane data from BigQuery: {query}")

        self._ratelane_data = self.bq_manager.execute_query(query)
        self._convert_to_float(self._ratelane_data, ["base_rate"])
        self._convert_to_datetime(self._ratelane_data, ["expiry_date", "effective_date"])

    @staticmethod
    def _generate_location_pattern(location: str) -> str:
        """Generate a regex pattern for a specific location.

        The pattern matches strings that start and end with the location (with optional alphanumeric suffixes)
        and have something in between.
        """
        # Pattern for location at start: USATL[A-Z0-9]*_[A-Z0-9]+_[A-Z0-9]+
        start_pattern = f"{location}[A-Z0-9]*_[A-Z0-9]+_[A-Z0-9]+"

        # Pattern for location at end: [A-Z0-9]+_[A-Z0-9]+_USATL[A-Z0-9]*
        end_pattern = f"[A-Z0-9]+_[A-Z0-9]+_{location}[A-Z0-9]*"

        return f"^({start_pattern}|{end_pattern})$"

    @measure_time(logger=logger)
    def process_data(self) -> None:
        """Process the fetched rate lane data."""
        if self._ratelane_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self._replace_empty_string()

        # TODO: define more delicate processing for dedup
        self._ratelane_data.drop_duplicates(inplace=True)

        logger.info("Rate lane data loaded successfully.")

    def _replace_empty_string(self):
        """Replace empty strings with None in the DataFrame."""
        self._ratelane_data = self._ratelane_data.replace("", None)
        self._ratelane_data = self._ratelane_data.replace(np.nan, None)
        self._ratelane_data = self._ratelane_data.replace("nan", None)
        self._ratelane_data = self._ratelane_data.replace("NaN", None)
        self._ratelane_data = self._ratelane_data.replace("None", None)
        self._ratelane_data = self._ratelane_data.replace("null", None)
        self._ratelane_data = self._ratelane_data.replace("NULL", None)
        self._ratelane_data = self._ratelane_data.replace("Null", None)

    def get_data(self) -> pd.DataFrame:
        """Return the processed rate lane data.

        Returns:
            pd.DataFrame: The processed rate lane data.
        """
        if self._ratelane_data is None:
            raise ValueError("Data not processed. Call process_data() first.")
        return self._ratelane_data

    @measure_time(logger=logger)
    def save_data(self, save_dir: Path) -> None:
        """Save the rate lane data to a file.

        Args:
            save_dir (Path): The directory to save the file to.
        """
        if self._ratelane_data is None:
            raise ValueError("Rate lane data not loaded. Call process_data() first.")
        self._ratelane_data.to_csv(save_dir / "rate_lane.csv", index=False)
        logger.info("Rate lane data saved to rate_lane.csv.")

    @measure_time(logger=logger)
    def load_data(self, save_dir: Path) -> None:
        """Load the rate lane data from a file.

        Args:
            save_dir (Path): The directory to load the file from.
        """
        if not (save_dir / "rate_lane.csv").exists():
            raise FileNotFoundError("Rate lane data not found. Call save_data() first.")
        self._ratelane_data = pd.read_csv(save_dir / "rate_lane.csv")
        self._convert_to_float(self._ratelane_data, ["base_rate"])
        # self._convert_to_datetime(self._ratelane_data, ["expiry_date", "effective_date"])

        logger.info("Rate lane data loaded from rate_lane.csv.")


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = RateLaneLoader(bq_manager)
    loader.fetch_data(filter_date="2021-01-01", node_cd=("USATL",))
    loader.process_data()
    import_data, export_data = loader.get_data()
