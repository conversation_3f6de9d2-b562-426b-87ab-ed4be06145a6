import json
from pathlib import Path

import pandas as pd

from archive.src_v2.const.config import settings
from archive.src_v2.thirdparty.gcp import BigQueryManager
from archive.src_v2.utils.decorators import measure_time
from archive.src_v2.utils.logger import log_handler

from .base_loader import DataLoaderBase
from .col_map import LOC_MAP, YARD_MAP

logger = log_handler.get_logger(name=__name__)


class LocationDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the LocationDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self._location_data: pd.DataFrame = None  # type: ignore [assignment]
        self._yard_data: pd.DataFrame = None  # type: ignore [assignment]
        self.location_dict: dict = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"location": LOC_MAP, "yard": YARD_MAP}

    @measure_time(logger=logger)
    def fetch_data(self) -> None:
        """Fetch location and yard data from BigQuery."""
        location_query = """
        SELECT {col}
        FROM `{table_name}`
        """
        table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.SRC_DATASET_ID}.{settings.DB.LOCATION_TABLE_ID}"
        location_query = location_query.format(col=self._generate_select_clause("location"), table_name=table_name)

        yard_query = """
        SELECT {col}
        FROM `{table_name}`
        """
        table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.SRC_DATASET_ID}.{settings.DB.YARD_TABLE_ID}"
        yard_query = yard_query.format(col=self._generate_select_clause("yard"), table_name=table_name)

        self._location_data = self.bq_manager.execute_query(location_query)
        self._yard_data = self.bq_manager.execute_query(yard_query)
        self._convert_to_float(self._location_data, ["latitude", "longitude"])
        self._convert_to_float(self._yard_data, ["yard_latitude", "yard_longitude"])

    @measure_time(logger=logger)
    def process_data(self) -> None:
        """Process the fetched data and create the location dictionary."""
        if self._location_data is None or self._yard_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        self.location_dict = self._location_data.set_index("location_code").to_dict(orient="index")

        yard_groups = self._yard_data.groupby("location_code")
        for loc_code, yards in yard_groups:
            yard_list = yards[["yard_code", "yard_latitude", "yard_longitude"]].to_dict(orient="records")
            if loc_code in self.location_dict:
                self.location_dict[loc_code]["yards"] = yard_list
            else:
                self.location_dict[loc_code] = {
                    "location_name": None,
                    "region_code": None,
                    "latitude": None,
                    "longitude": None,
                    "yards": yard_list,
                }
        # Ensure that all locations have a "yards" key. TODO: may not be necessary
        for _, v in self.location_dict.items():  # noqa: B007
            if "yards" not in v:
                v["yards"] = []

        logger.info("Location data loaded successfully.")

    @measure_time(logger=logger)
    def get_data(self) -> dict:
        """Return the processed location data.

        Returns:
            dict: A dictionary containing location and yard information
        """
        if self.location_dict is None:
            raise ValueError("Data not processed. Call process_data() first.")
        return self.location_dict

    @measure_time(logger=logger)
    def save_data(self, save_dir: Path):
        """Save the location data to a file.

        Args:
            save_dir (Path): The directory to save the data to.
        """
        if self.location_dict is None:
            raise ValueError("Location data not loaded. Call process_data() first.")
        with open(save_dir / "location_data.json", "w") as f:
            json.dump(self.location_dict, f)
        logger.info("Location data saved to location_data.json.")

    def load_data(self, save_dir: Path) -> None:
        """Load the location data from a file.

        Args:
            save_dir (Path): The directory to load the data from.
        """
        if not (save_dir / "location_data.json").exists():
            raise FileNotFoundError("Location data file not found. Call save_data() first.")
        with open(save_dir / "location_data.json") as f:
            self.location_dict = json.load(f)
        logger.info("Location data loaded from location_data.json.")


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = LocationDataLoader(bq_manager)
    # loader.update_column_mapping('location', {'new_column': 'NEW_COL_NAME'})

    loader.fetch_data()
    loader.process_data()
    location_dict = loader.get_data()
    print(location_dict)
