import datetime as dt
from pathlib import Path

import pandas as pd

from archive.src_v2.const.config import settings
from archive.src_v2.thirdparty.gcp import BigQueryManager
from archive.src_v2.utils.decorators import measure_time
from archive.src_v2.utils.logger import log_handler

from .base_loader import DataLoaderBase
from .col_map import REPORT_MAP

THRES_SPLIT_DT = dt.time(16, 30)  # 4:30 PM in UTC / 12:30 PM in UTC-4

logger = log_handler.get_logger(name=__name__)


class ReportDataLoader(DataLoaderBase):
    def __init__(self, bq_manager: BigQueryManager):
        """Initialize the ReportDataLoader with a BigQuery client.

        Args:
            bq_manager (BigQueryManager): A BigQuery manager.
        """
        super().__init__(bq_manager)
        self.report_data: pd.DataFrame = None  # type: ignore [assignment]
        self.column_mappings: dict[str, dict[str, str]] = {"report": REPORT_MAP}

    @staticmethod
    def _validate_run_dt(run_dt: str | None = None):
        if run_dt is None:
            curr_dt = dt.datetime.now(dt.timezone.utc)
            curr_time = curr_dt.time()
            run_dt = (
                curr_dt.strftime("%Y-%m-%dT05:00:00")
                if curr_time < THRES_SPLIT_DT
                else curr_dt.strftime("%Y-%m-%dT12:30:00")
            )
        else:
            # Validate date format, the date format should be "YYYY-MM-DD" and the time format should be "HH:MM:SS"
            try:
                parsed_dt = dt.datetime.strptime(run_dt, "%Y-%m-%dT%H:%M:%S")
            except ValueError as e:
                raise ValueError("Invalid date format. Use 'YYYY-MM-DDTHH:MM:SS'.") from e
            if parsed_dt.time() not in [dt.time(5, 0), dt.time(12, 30)]:
                raise ValueError("Invalid time value. Time must be either '05:00:00' or '12:30:00'.")

        return run_dt

    @measure_time(logger=logger)
    def fetch_data(
        self,
        run_dt: str | None = None,
        door_cy_value: str = "Door",
        hazmat: str = "N",
        customer_nominated_trucker: bool = False,
        drop_and_pick: str | None = "N",
        node_cd: tuple[str, ...] = (),
    ) -> None:
        """Fetch report data from BigQuery with dynamic parameters for filtering.

        Args:
            run_dt (str): The date and time for filtering the edw_upd_dt column (format: "YYYY-MM-DDTHH:MM").
            door_cy_value (str): The value to filter for the door_cy column.
            hazmat (str): The value to filter for the hazmat column.
            customer_nominated_trucker (bool): The flag to filter for NULL customer_nominated_trucker column.
            drop_and_pick (str, optional): The value to filter for drop_and_pick column ("N" or None for NULL).
            node_cd (list[str], optional): A list of specific CY values to filter for.

        Raises:
            ValueError: If the run_dt is invalid.
        """
        # run_dt = self._validate_run_dt(run_dt)
        query = """
        SELECT {col}
        FROM `{table_name}`('{date_time}')
        WHERE 0=0
        --AND EDW_UPD_DT = DATETIME('{date_time}')
        --AND COP_STS_CD IN ("T", "C")
        AND ((BOUND = "EXPORT" AND CNTR_NO is null) OR (BOUND = "IMPORT" AND CNTR_NO is not null))
        """
        if door_cy_value:
            query += f"AND DOOR_CY = '{door_cy_value}'\n"
        if hazmat:
            query += f"AND HAZMAT = '{hazmat}'\n"
        if not customer_nominated_trucker:
            query += "AND CUSTOMER_NOMINATED_TRUCKER IS NULL\n"
        if drop_and_pick == "N":
            query += f"AND (DROP_AND_PICK IS NULL OR DROP_AND_PICK = '{drop_and_pick}')\n"
        else:
            query += f"AND DROP_AND_PICK = '{drop_and_pick}'\n"
        if node_cd or len(node_cd) > 0:
            tmp = ", ".join([f"'{cy}'" for cy in node_cd])
            query += f"AND INTERCHANGE_LOCATION IN ({tmp})\n"

        table_name = f"{settings.ENV.PROJECT_ID}.{settings.DB.DATAPIPELINE_TABLE_ID}.{settings.DB.REPORT_TABLE_ID}"
        query = query.format(col=self._generate_select_clause("report"), table_name=table_name, date_time=run_dt)
        logger.info(f"Fetching report data from BigQuery: {query}")
        self.report_data = self.bq_manager.execute_query(query)
        self._convert_data()

    def _convert_data(self) -> None:
        self._convert_to_datetime(
            self.report_data,
            [
                "import_availability_at_final_cy",
                "estimated_import_delivery_date",
                "export_first_receiving_date",
                "export_cut_off_date",
                "so_create_date",
                "first_port_of_load_cutoff_date",
                # "edw_upd_dt",
            ],
        )
        self._convert_to_float(
            self.report_data, ["container_qty", "container_tare_weight_lbs", "container_tare_weight_lbs"]
        )

    @measure_time(logger=logger)
    def process_data(self) -> None:
        """Process the fetched report data."""
        if self.report_data is None:
            raise ValueError("Data not fetched. Call fetch_data() first.")

        logger.info("Report data loaded successfully.")

    def get_data(self) -> pd.DataFrame:
        """Return the processed report data.

        Returns:
            pd.DataFrame: The processed report DataFrame
        """
        if self.report_data is None:
            raise ValueError("Data not processed. Call process_data() first.")
        return self.report_data

    @measure_time(logger=logger)
    def save_data(self, save_dir: Path) -> None:
        """Save the report data to a file.

        Args:
            save_dir (Path): The directory to save the data to.
        """
        if self.report_data is None:
            raise ValueError("Data not loaded. Call fetch_data() first.")
        self.report_data.to_csv(save_dir / "report_data.csv", index=False)
        logger.info("Report data saved to report_data.csv.")

    @measure_time(logger=logger)
    def load_data(self, save_dir: Path) -> None:
        """Load the report data from a file.

        Args:
            save_dir (Path): The directory to load the data from.
        """
        if not (save_dir / "report_data.csv").exists():
            raise FileNotFoundError("Report data file not found. Call save_data() first.")
        self.report_data = pd.read_csv(save_dir / "report_data.csv")
        self._convert_data()
        logger.info("Report data loaded from report_data.csv.")


if __name__ == "__main__":
    bq_manager = BigQueryManager()
    loader = ReportDataLoader(bq_manager)

    # loader._validate_run_dt("2024-10-08T12:30")

    # Fetch data
    loader.fetch_data(
        door_cy_value="Door", hazmat="N", customer_nominated_trucker=False, drop_and_pick="N", node_cd=("USATL63",)
    )
    loader.process_data()
    df_report = loader.get_data()
