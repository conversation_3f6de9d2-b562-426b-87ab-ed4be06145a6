LOC_MAP = {
    "location_code": "LOC_CD",
    "location_name": "LOC_NM",
    "region_code": "RGN_CD",
    "yard_code": "MTY_PKUP_YD_CD",
    "latitude": "NEW_LOC_LAT",
    "longitude": "NEW_LOC_LON",
}

YARD_MAP = {
    "location_code": "LOC_CD",
    "yard_code": "YD_CD",
    "yard_name": "YD_NM",
    "yard_address": "YD_ADDR",
    "zip_code": "ZIP_CD",
    "yard_latitude": "YD_LAT",
    "yard_longitude": "YD_LON",
}

CONTAINER_SIZE_MAP = {"sizetype": "sizetype", "mapsize": "mapsize"}

RATELANE_MAP = {
    "lane_description": "LANE_DESCRIPTION",
    "origin_location_code": "ORIGIN_LOCATION_CODE",
    "origin_location_name": "ORIGIN_LOCATION_NAME",
    "origin_location_type": "ORIGIN_LOCATION_TYPE",
    "origin_geo_hierarchy": "GEO_HIERARCHY_ORIGIN",
    "origin_address_1": "ORIGIN_ADDRESS1",
    "origin_city": "ORIGIN_CITY",
    "origin_state": "ORIGIN_STATE",
    "origin_country": "ORIGIN_COUNTRY",
    "destination_location_code": "DESTINATION_LOCATION_CODE",
    "destination_location_name": "DESTINATION_LOCATION_NAME",
    "destination_location_type": "DESTINATION_LOCATION_TYPE",
    "destination_geo_hierarchy": "GEO_HIERARCHY_DESTINATION",
    "destination_address_1": "DESTINATION_ADDRESS1",
    "destination_city": "DESTINATION_CITY",
    "destination_state": "DESTINATION_STATE",
    "destination_country": "DESTINATION_COUNTRY",
    "via_point1": "VIA_POINT1",
    "base_rate": "BASE_RATE",
    "currency_cd": "CURRENCY_CODE",
    "trip_type": "TRIP_TYPE",
    "effective_date": "EFFECTIVE_DATE",
    "expiry_date": "EXPIRATION_DATE",
    "commodity_class": "COMMODITY_CLASS",
    "is_hazmat": "HAZMAT",
    "equipment_class": "EQUIPMENT_CLASS",
    "equipment_size_type": "EQUIPMENT_SIZE_TYPE",
    "vendor_cd": "VENDOR_CODE",
    "carrier_cd": "CARRIER_CODE",
}

RATELANE_MAPPING = {
    "port_cd": "PORT_CD",
    "import_loc_cd": "IMPORT_LOC_CD",
    "export_loc_cd": "EXPORT_LOC_CD",
    "import_base_rate": "IMPORT_BASE_RATE",
    "export_base_rate": "EXPORT_BASE_RATE",
    "import_trip_type": "IMPORT_TRIP_TYPE",
    "export_trip_type": "EXPORT_TRIP_TYPE",
    "import_vendor": "IMPORT_VENDOR",
    "export_vendor": "EXPORT_VENDOR",
}

REPORT_MAP = {
    "bound": "BOUND",
    "reuse": "REUSE",
    "shipping_line": "SHIPPING_LINE",
    "truck_company_sequence": "TRUCK_COMPANY_SEQUENCE",
    "truck_company": "TRUCK_COMPANY",
    "cop_no": "COP_NO",
    "booking_no": "BKG_NO",
    "container_no": "CNTR_NO",
    "shipper__consignee_name": "SHIPPER_CONSIGNEE_NAME",
    "container_qty": "CONTAINER_QUANTITY",
    "container_size_type": "CONTAINER_TYPES_SIZE",
    "location_city": "LOCATION_CITY",
    "location_state": "LOCATION_STATE",
    "location_zip": "LOCATION_ZIP",
    "location_country": "LOCATION_COUNTRY",
    "interchange_location": "INTERCHANGE_LOCATION",
    "interchange_location_city": "INTERCHANGE_LOCATION_CITY",
    "transport_mode": "TRANSPORT_MODE",
    "import_availability_at_final_cy": "IMPORT_AVAILABLE_AT_FINAL_CY",
    "estimated_import_delivery_date": "ESTIMATED_IMPORT_DELIVERY_DATE",
    "export_first_receiving_date": "EXPORT_N1ST_RECEIVING_DATE",
    "export_cut_off_date": "EXPORT_CUTOFF_DATE",
    "hazmat": "HAZMAT",
    "container_tare_weight_lbs": "CONTAINER_TARE_WEIGHT_LBS",
    "estimate_cargo_weight_lbs": "ESTIMATED_CARGO_WEIGHT_LBS",
    "flex_height": "FLEX_HEIGHT",
    "control_office": "CONTROL_OFFICE",
    "so_creator": "SERVICE_ORDER_CREATOR",
    "so_create_date": "SERVICE_ORDER_CREATION_DATE",
    "service_order_no": "SERVICE_ORDER_NUMBER",
    "work_order_no": "WORK_ORDER_NUMBER",
    "first_port_of_load_location": "FIRST_PORT_OF_LOAD_LOCATION",
    "first_port_of_load_cutoff_date": "FIRST_PORT_OF_LOAD_CUTOFF_DATE",
    "last_port_of_discharge_location": "LAST_PORT_OF_DISCHARGE_LOCATION",
    "drop_and_pick": "DROP_AND_PICK",
    "foc_cleared_status": "FOC_CLEARED_STATUS",
    "customer_nominated_trucker": "CUSTOMER_NOMINATED_TRUCKER",
    "commodity": "COMMODITY",
    "trucking_company_scac": "TRUCKING_COMPANY_SCAC",
    "pol_pod": "POL_POD",
    "inland_transport_mode": "INLAND_TRANSPORT_MODE",
    "rail_scac": "RAIL_SCAC",
    "door_cy": "DOOR_CY",
    "stcc_code": "STCC_CODE",
    "street_address": "STREET_ADDRESS",
}
