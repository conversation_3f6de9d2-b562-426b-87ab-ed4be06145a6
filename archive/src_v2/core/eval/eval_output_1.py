import pandas as pd


def eval(df_report, x, imports, exports):
    """_summary_.

    Args:
        df_report (_type_): _description_
        x (_type_): _description_
        imports (_type_): _description_
        exports (_type_): _description_
    """
    optimal_tours_mb = pd.read_excel(
        "../data/optimal_tours/ONE Systematics and Optimal Tours - Aug 9, 2024 at 7_50 AM.xlsx",
        sheet_name="Tours Optimized",
    )
    # optimal_tours_our = pd.read_csv("../data/ours_optimal_tours/our_optimal_tours_09_Aug.csv")

    print(
        f"Length of MB Optimal Tours: {len(optimal_tours_mb)} \n",
        "Length of Our Optimal Tours: {len(optimal_tours_our)}",
    )

    optimal_tours_mb.columns = optimal_tours_mb.columns.str.strip()
    total_street_turn = len(optimal_tours_mb)
    matched = 0

    for _, row in optimal_tours_mb.iterrows():  # noqa: B007
        imp_cand = row["Import Unique ID"]
        exp_cand = row["Export Unique ID"]

        ibk_num, icop_num = imp_cand.split(" - ")
        ebk_num, ecop_num = exp_cand.split(" - ")

        try:
            im_idx = df_report[(df_report["Booking No."] == ibk_num) & (df_report["COP Number"] == icop_num)].index[0]
            ex_edx = df_report[(df_report["Booking No."] == ebk_num) & (df_report["COP Number"] == ecop_num)].index[0]

            imp_val = next((item for item in imports if str(im_idx) in item), None)
            exp_val = next((item for item in exports if str(ex_edx) in item), None)

            if x[imp_val][exp_val]:
                print(x[imp_val][exp_val])
                matched += 1
            else:
                print(f"No match, imp: {imp_val} - exp: {exp_val}")

        except Exception:
            total_street_turn -= 1
            print(f"Import: {imp_cand} or Export: {exp_cand} not in the MB report ")

    print(
        f">>> Optimal street-turn from MB is {round(matched / total_street_turn, 2) * 100}% in our possible street-turn candidates"
    )
    print(
        f">>> Optimal street-turn from MB(include history data) is {round(matched / len(optimal_tours_mb), 2) * 100}% in our possible street-turn candidates"
    )
