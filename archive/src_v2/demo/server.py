import json

import pandas as pd
import pandas_gbq
import pulp
import streamlit as st
from google.cloud import bigquery


class LinearSolver:
    def __init__(self):
        """Initializes the LinearSolver class."""
        pass

    def solve(
        self,
        valid_pairs: dict[str, dict[str, int]],
        savings: dict[tuple[str, str], float],
        n_im: int,
        n_ex: int,
    ) -> dict[str, dict[str, pulp.LpVariable]]:
        """Solve the linear programming problem for maximizing savings with a dictionary of pairs.

        Args:
            save_dir (Path): saving path.
            valid_pairs (Dict[str, Dict[str, int]]): Dictionary of valid import-export pairs.
            savings (Dict[Tuple[str, str], float]): Savings between imports and exports.
            n_ex (int): Maximum number of exports that can be matched.
            n_im (int): Maximum number of imports that can be matched.
            loc (str): location name.

        Returns:
            Dict[str, Dict[str, pulp.LpVariable]]: The matching result as a dictionary of decision variables.
        """
        # Create a linear programming problem
        saving_model = pulp.LpProblem("Maximize_Savings", pulp.LpMaximize)  # maximize
        # saving_model = pulp.LpProblem("Minimize_Distance", pulp.LpMinimize)  # minimize

        # Extract all unique imports and exports from valid_pairs
        imports = list(valid_pairs.keys())
        exports = {ex for export_dict in valid_pairs.values() for ex in export_dict.keys()}

        # Decision variables
        x = pulp.LpVariable.dicts("match", (imports, exports), cat="Binary")

        # Objective function
        saving_model += (
            pulp.lpSum([savings[(i, j)] * x[i][j] for i in valid_pairs for j in valid_pairs[i] if (i, j) in savings]),
            "Total_Savings",
        )

        # Constraints
        for j in exports:
            saving_model += (
                pulp.lpSum([x[i][j] for i in imports if j in valid_pairs[i]]) <= n_ex,
                f"Export_{j}_constraint",
            )
        for i in imports:
            saving_model += pulp.lpSum([x[i][j] for j in valid_pairs[i]]) <= n_im, f"Import_{i}_constraint"

        # Solve the problem
        saving_model.solve(pulp.PULP_CBC_CMD(msg=True))
        # saving_model.solve(pulp.GLPK_CMD(msg=False))

        return x


def load_savings_from_bq(project_id: str, table_id: str, run_datetime: str = "") -> dict:
    """Load savings data from BigQuery and convert it back to the original dictionary format."""
    try:
        # Build the query with optional datetime filter using parameter
        query = """
        SELECT import_index, export_index, distance_saved_km
        FROM `{}`.`{}`
        """
        query = query.format(project_id, table_id)
        if run_datetime:
            query += " WHERE run_datetime = TIMESTAMP(?)"

        # Execute query
        savings_df = pandas_gbq.read_gbq(query, project_id=project_id)

        # Convert DataFrame to dictionary with tuple keys
        savings_dict = {
            (row["import_index"], row["export_index"]): row["distance_saved_km"] for _, row in savings_df.iterrows()
        }

        print(f"Loaded {len(savings_dict)} savings records from BigQuery.")
        return savings_dict

    except Exception as e:
        print(f"Failed to load savings data from BigQuery: {str(e)}")
        return {}


@st.cache_data
def load_valid_pairs_from_bq(project_id: str, table_id: str, run_datetime: str = "") -> dict:
    """Load valid pairs data from BigQuery and convert it back to the original dictionary format."""
    try:
        # Build the query with optional datetime filter using parameter
        query = """
        SELECT import_index, export_index, distance_saved, cntz_size_im, cntz_size_ex, time_gap, round_trip, street_turn, distance_doors
        FROM `{}`.`{}`
        """
        query = query.format(project_id, table_id)
        if run_datetime:
            query += " WHERE run_datetime = TIMESTAMP(?)"

        # Execute query
        valid_pairs_df = pandas_gbq.read_gbq(query, project_id=project_id)

        # Convert DataFrame to nested dictionary format
        valid_pairs_dict: dict[str, dict[str, list]] = {}
        for _, row in valid_pairs_df.iterrows():
            import_index = row["import_index"]
            export_index = row["export_index"]
            distance_saved = row["distance_saved"]
            cntz_size_im = row["cntz_size_im"]
            cntz_size_ex = row["cntz_size_ex"]
            time_gap = row["time_gap"]
            round_trip = row["round_trip"]
            street_turn = row["street_turn"]
            distance_doors = row["distance_doors"]

            if import_index not in valid_pairs_dict:
                valid_pairs_dict[import_index] = {}

            valid_pairs_dict[import_index][export_index] = [
                distance_saved,
                cntz_size_im,
                cntz_size_ex,
                time_gap,
                round_trip,
                street_turn,
                distance_doors,
            ]

        print(f"Loaded {len(valid_pairs_df)} valid pair records from BigQuery.")
        return valid_pairs_dict

    except Exception as e:
        print(f"Failed to load valid pairs data from BigQuery: {str(e)}")
        return {}


@st.cache_data
def load_ratelane_meta_data(project_id, dataset_id, table_id):
    """Loads rate lane metadata from BigQuery and converts it to a nested dictionary."""
    client = bigquery.Client(project=project_id)
    query = """
    SELECT * FROM `{}`.`{}`.`{}`
    """
    query = query.format(project_id, dataset_id, table_id)
    df = client.query(query).to_dataframe()

    # Initialize an empty dictionary
    rlane_meta_data = {}

    for _, row in df.iterrows():
        import_key = row["import_key"]
        export_key = row["export_key"]

        # Convert JSON string back to dictionary & then DataFrame
        im_rate_lane_df = pd.DataFrame(json.loads(row["im_rate_lane"])) if row["im_rate_lane"] else None
        ex_rate_lane_df = pd.DataFrame(json.loads(row["ex_rate_lane"])) if row["ex_rate_lane"] else None

        # Ensure the import key exists
        if import_key not in rlane_meta_data:
            rlane_meta_data[import_key] = {}

        # Store the export key with its corresponding rate lanes
        rlane_meta_data[import_key][export_key] = {"im_rate_lane": im_rate_lane_df, "ex_rate_lane": ex_rate_lane_df}

    return rlane_meta_data


def get_run_datetimes():
    """Fetch available run_datetime values from BigQuery."""
    query = """
    SELECT DISTINCT FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', run_datetime) as run_datetime
    FROM `{}`.`{}`
    ORDER BY run_datetime DESC
    """
    query = query.format(project_id, valid_pairs_table_id)
    try:
        df = pandas_gbq.read_gbq(query, project_id=project_id)
        return df["run_datetime"].tolist()
    except Exception as e:
        st.error(f"Failed to load run_datetime values: {e}")
        return []


def load_location(client) -> pd.DataFrame:
    """Load location data from BigQuery.

    Args:
        client: BigQuery client instance.

    Returns:
        pd.DataFrame: DataFrame containing location information with columns:
            Location_code, Location_name, Region_code, Yard_code, lat, long.
    """
    query = """
    SELECT distinct LOC_CD as Location_code,
            LOC_NM as Location_name,
            RGN_CD as Region_code,
            MTY_PKUP_YD_CD as Yard_code,
            NEW_LOC_LAT as lat,
            NEW_LOC_LON as long
    FROM `one-global-dilab-matchback-dev.SOURCE_DATA.DWC_LOCATION`
    """

    df_location_info = client.query(query).to_dataframe()
    return df_location_info


def load_yards(client) -> pd.DataFrame:
    """Load yard information from BigQuery.

    Args:
        client: BigQuery client instance.

    Returns:
        pd.DataFrame: DataFrame containing yard information with columns:
            Yard_code, Location_code, Yard_name, Yard_address, Zip_code, Yard_lat, Yard_lon.
    """
    query = """
    SELECT YD_CD as Yard_code,
        LOC_CD as Location_code,
        YD_NM as Yard_name,
        YD_ADDR as Yard_address,
        ZIP_CD as Zip_code,
        YD_LAT as Yard_lat,
        YD_LON as Yard_lon
    FROM `one-global-dilab-matchback-dev.SOURCE_DATA.DWC_YARD`
    """

    df_yard_info = client.query(query).to_dataframe()
    return df_yard_info


@st.cache_data
def create_location_dict() -> dict:
    """Create a dictionary containing location and yard information from BigQuery.

    Returns:
        dict: A dictionary where keys are location codes and values are dictionaries containing:
            - Location_name (str): Name of the location
            - Region_code (str): Region code
            - lat (float): Location latitude
            - long (float): Location longitude
            - yards (list): List of dictionaries containing yard information
    """
    # Create the dictionary
    location_dict = {}
    df_location_info = load_location(client)
    df_yard_info = load_yards(client)

    # Add location details from df_location_info
    for _, row in df_location_info.iterrows():
        loc_code = row["Location_code"]
        location_info = {
            "Location_name": row["Location_name"],
            "Region_code": row["Region_code"],
            "lat": float(row["lat"]) if row["lat"] is not None else None,
            "long": float(row["long"]) if row["long"] is not None else None,
            "yards": [],  # Initialize with an empty list to hold yard details
        }
        location_dict[loc_code] = location_info

    # Add yard details from df_yard_info
    for _, row in df_yard_info.iterrows():
        loc_code = row["Location_code"]
        yard_info = {
            "Yard_code": row["Yard_code"],
            "Yard_lat": float(row["Yard_lat"]) if row["Yard_lat"] is not None else None,
            "Yard_lon": float(row["Yard_lon"]) if row["Yard_lon"] is not None else None,
        }
        if loc_code in location_dict:
            location_dict[loc_code]["yards"].append(yard_info)
        else:
            location_dict[loc_code] = {
                "Location_name": None,
                "Region_code": None,
                "lat": None,
                "long": None,
                "yards": [yard_info],
            }
    return location_dict


def is_container_size_compatible(import_size, export_size):
    """Checks if import and export container sizes are compatible.

    Args:
        import_size (str): Container size for the import.
        export_size (str): Container size for the export.

    Returns:
        bool: True if the container sizes are compatible, otherwise False.
    """
    return import_size == export_size or (import_size in {"D4", "D5"} and export_size == "X45")


def filter_valid_pairs(raw_pairs, min_distance=None, max_distance=None, exact_time_gap=None, allowed_cntz_size=None):
    """Filters the raw_pairs dictionary based on user-defined constraints.

    Args:
        raw_pairs (dict): The dictionary containing import-export pairs with their attributes.
        min_distance (float, optional): Minimum required distance saved.
        max_distance (float, optional): Maximum allowed distance saved.
        exact_time_gap (int, optional): The exact required time gap.
        allowed_cntz_size (list, optional): List of allowed container sizes for imports.

    Returns:
        dict: Filtered dictionary containing only valid import-export pairs based on constraints.
    """
    filtered_pairs = {}

    for import_key, export_candidates in raw_pairs.items():
        valid_exports = {}

        for export_key, values in export_candidates.items():
            distance_saved, cntz_size_im, cntz_size_ex, time_gap, round_trip, street_turn, distance_doors = (
                values  # Extract values
            )

            # Apply constraints
            if (min_distance is not None and distance_doors < min_distance) or (
                max_distance is not None and distance_doors > max_distance
            ):
                continue  # Skip if distance does not meet the criteria

            # Check if import/export container sizes are allowed by the user
            if allowed_cntz_size is not None and cntz_size_im not in allowed_cntz_size:
                continue  # Skip if import container size is not allowed

            if allowed_cntz_size is not None and cntz_size_ex not in allowed_cntz_size:
                continue  # Skip if export container size is not allowed

            # Check if import and export container sizes are compatible
            if not is_container_size_compatible(cntz_size_im, cntz_size_ex):
                continue  # Skip if import/export container sizes are not compatible

            if exact_time_gap is not None and time_gap <= -exact_time_gap:
                continue  # Skip if time gap does not match the exact required value

            # If all conditions pass, add the export to valid list
            valid_exports[export_key] = values

        # Only add the import key if it has valid export candidates
        if valid_exports:
            filtered_pairs[import_key] = valid_exports

    return filtered_pairs


def merge_result(import_ratelane_df, export_ratelane_df):
    """Merge import and export rate lane information to find optimal street turns and round trips.

    Args:
        import_ratelane_df (pd.DataFrame): DataFrame containing import rate lane information
        export_ratelane_df (pd.DataFrame): DataFrame containing export rate lane information

    Returns:
        tuple[pd.DataFrame, pd.DataFrame]: A tuple containing:
            - DataFrame with merged street turn and round trip results
            - Original merged DataFrame with basic rate lane information
    """
    # add prefix to import_ratelane_df
    im = import_ratelane_df.add_prefix("import_")
    # add prefix to export_ratelane_df
    ex = export_ratelane_df.add_prefix("export_")

    tmp_df = im.merge(ex, how="cross")
    tmp_df = tmp_df[
        [
            "import_lane_description",
            "import_vendor_cd",
            "import_carrier_cd",
            "import_base_rate",
            "import_trip_type",
            "export_lane_description",
            "export_vendor_cd",
            "export_carrier_cd",
            "export_base_rate",
            "export_trip_type",
        ]
    ]
    tmp_df["total_cost"] = tmp_df["import_base_rate"] + tmp_df["export_base_rate"]
    tmp_df["optimal"] = False

    # Filter street turns based on trip types
    street_turn = tmp_df[(tmp_df["import_trip_type"] == "RETURN") & (tmp_df["export_trip_type"] == "ONE WAY")]
    street_turn.drop_duplicates(inplace=True)
    if street_turn.empty:
        return pd.DataFrame(), tmp_df

    # Get all possible street turns
    street_turn_result = street_turn[street_turn["import_vendor_cd"] == street_turn["export_vendor_cd"]].copy()

    # Identify the optimal street turn
    optimal_street_turn = street_turn_result[
        street_turn_result["total_cost"] == street_turn_result["total_cost"].min()
    ].head(1)
    # Add prefix to street turn result
    street_turn_result = street_turn_result.add_prefix("street_turn_")
    # Add a primary choice column: True for optimal, False for others
    street_turn_result.loc[:, "street_turn_optimal"] = False
    street_turn_result.loc[optimal_street_turn.index, "street_turn_optimal"] = True

    # Filter round trips based on trip types
    df_merge = pd.DataFrame()
    for _, candidate in street_turn_result.iterrows():
        round_trip = tmp_df[(tmp_df["import_trip_type"] == "RETURN") & (tmp_df["export_trip_type"] == "RETURN")]
        round_trip.drop_duplicates(inplace=True)

        # Filter round trips that match the current export_vendor_code in the candidate
        round_trip_result = round_trip[round_trip["import_vendor_cd"] == candidate["street_turn_export_vendor_cd"]]

        # Get the round trip with minimum import and export base rates
        round_trip_result = round_trip_result[
            (round_trip_result["import_base_rate"] == round_trip_result["import_base_rate"].min())
            & (round_trip_result["export_base_rate"] == round_trip_result["export_base_rate"].min())
        ]

        # Add prefix to round trip result and select one as the primary choice
        round_trip_result = round_trip_result.add_prefix("round_trip_")
        round_trip_result.loc[round_trip_result.sample().index, "round_trip_optimal"] = True

        # Merge only the current candidate with the round trip result
        df_final = pd.DataFrame([candidate]).merge(round_trip_result, how="cross")

        # Mark the final optimal combination where both street_turn and round_trip are primary choices
        df_final["optimal"] = False
        df_final.loc[
            (df_final["street_turn_optimal"]) & (df_final["round_trip_optimal"]),
            "optimal",
        ] = True

        df_final["cost_save"] = df_final["round_trip_total_cost"] - df_final["street_turn_total_cost"]

        # Concatenate the result into df_merge
        df_merge = pd.concat([df_merge, df_final], ignore_index=True)

    return df_merge, tmp_df


def simplify_name(full_name: str, location_dict: dict) -> str:
    """Simplify a location name by extracting relevant parts.

    Args:
        full_name (str): The full location name with underscore separators
        location_dict (dict): Dictionary containing location information

    Returns:
        str: Simplified name in format "Location_name - Part" or original name if can't be simplified
    """
    parts = full_name.split("_")
    if len(parts) >= 3:
        return f"{location_dict[parts[0]]['Location_name']} - {parts[-2]}"
    return full_name


def find_key_by_location_name(location_name: str, locations_dict: dict) -> str | None:
    """Find the location key in the dictionary by its location name.

    Args:
        location_name (str): The name of the location to search for
        locations_dict (dict): Dictionary containing location information

    Returns:
        str | None: The key corresponding to the location name if found, None otherwise
    """
    for key, value in locations_dict.items():
        if value["Location_name"] == location_name:
            return key
    return None


##------------------------------------------------------------*******************------------------------------------------------------------##


### Variables Config
project_id = "one-global-dilab-matchback-dev"
dataset_id = "OUTPUTS"
valid_pairs_table_id = "OUTPUTS.VALID_RAW_PAIRS_V2"
rate_lane_meta_data = "RATE_LANE_META_DATA"


# Initialize session state variables
if "constraints_applied" not in st.session_state:
    st.session_state.constraints_applied = False
if "generate_route_clicked" not in st.session_state:
    st.session_state.generate_route_clicked = False


linear_soler = LinearSolver()
client = bigquery.Client()


def main():
    """Main function to run the Streamlit app for Inland Route Optimization Demo."""
    # Page configuration
    st.set_page_config(page_title="Inland Route Optimization Demo", layout="wide")

    # Custom styles
    st.markdown(
        """
        <style>
            html, body, [class*="stApp"] {
                font-size: 18px !important;
            }
            h1 {
                font-size: 48px !important;
            }
            h2, .stHeader {
                font-size: 32px !important;
            }
            .stButton>button {
                font-size: 20px !important;
            }
        </style>
    """,
        unsafe_allow_html=True,
    )

    st.title("Inland Route Optimization Demo")

    # 🚀 **Sidebar - Filters & Constraints**
    st.sidebar.header("⚙️ Filters & Constraints")

    # ✅ **Filters as Dropdowns**
    st.sidebar.selectbox("🚪 DOOR_CY", ["Door"], index=0)
    st.sidebar.selectbox("☢️ Hazmat", ["Null"], index=0)
    st.sidebar.selectbox("🚚 Customer Nominated Trucker", ["Null"], index=0)
    st.sidebar.selectbox("📦 Drop & Pick", ["Null"], index=0)

    # Load available timestamps
    # 🎯 **Constraint Selection in Sidebar**
    st.sidebar.subheader("🎛 Constraint Selection")
    run_datetimes = get_run_datetimes()
    selected_run_datetime = st.sidebar.selectbox("📅 Select Run Datetime:", sorted(run_datetimes, reverse=True))

    if selected_run_datetime:
        # Fetch data from BigQuery
        valid_pairs_dict = load_valid_pairs_from_bq(project_id, valid_pairs_table_id, selected_run_datetime)
        location_dict = create_location_dict()
        rlane_meta_data = load_ratelane_meta_data(project_id, dataset_id, rate_lane_meta_data)

        # Creating savings_dict from valid_pairs_dict
        savings_dict = {
            (import_key, export_key): values[0]  # Extracting distance_saved from the list
            for import_key, export_dict in valid_pairs_dict.items()
            for export_key, values in export_dict.items()
        }

        min_distance = st.sidebar.number_input("📏 Min Distance Doors (km)", min_value=0.0, value=0.0, step=10.0)
        max_distance = st.sidebar.number_input("📏 Max Distance Doors (km)", min_value=0.0, value=600.0, step=10.0)
        time_gap = st.sidebar.number_input("⏳ Time Gap (days)", min_value=0, value=1, step=1)

        # Extract unique import and export container sizes from valid_pairs_dict
        unique_container_sizes = sorted(
            {
                size
                for export_dict in valid_pairs_dict.values()
                for values in export_dict.values()
                for size in (values[1], values[2])  # Extract both import and export sizes
            }
        )

        allowed_cntz_size = st.sidebar.multiselect(
            "📦 Allowed Container Sizes", options=unique_container_sizes, default=[unique_container_sizes[0]]
        )
        # **Apply Constraints Button**
        if st.sidebar.button("✅ Apply Constraints"):
            st.session_state.constraints_applied = True  # Set flag
            st.session_state.generate_route_clicked = False  # Reset route click state

        # ✅ **Apply Constraints if Clicked**
        if st.session_state.constraints_applied:
            valid_pairs_dict_filtered = filter_valid_pairs(
                valid_pairs_dict,
                min_distance=min_distance,
                max_distance=max_distance,
                exact_time_gap=time_gap,
                allowed_cntz_size=allowed_cntz_size,
            )

            original_import_mapping = {
                simplify_name(key, location_dict): key for key in valid_pairs_dict_filtered.keys()
            }

            # Extract unique Import candidates
            import_candidates = list(valid_pairs_dict_filtered.keys())

            # Create mapping
            import_name_mapping = {simplify_name(name, location_dict): name for name in import_candidates}

            lp_results = linear_soler.solve(valid_pairs_dict_filtered, savings_dict, 1, 1)

            matched_pairs = [
                {
                    "Import 🔻": f"{location_dict[im.split('_')[0]]['Location_name']} - {im.split('_')[-2]}",
                    "Export 🔺": f"{location_dict[ex.split('_')[0]]['Location_name']} - {ex.split('_')[-2]}",
                    "Distance Saved (km) ✅": savings_dict[im, ex],
                }
                for im, export_dict in valid_pairs_dict_filtered.items()
                for ex, val in export_dict.items()
                if lp_results[im][ex].varValue == 1
            ]

            df_matched = pd.DataFrame(matched_pairs)

            if df_matched.empty:
                st.warning("❌ No matching pairs found!")
            else:
                st.success(f"✅ Found {len(df_matched)} recommended matching pairs!")
                df_matched = df_matched.sort_values(by="Distance Saved (km) ✅", ascending=False)

            st.header("📊 Matched Import-Export Pairs")
            st.dataframe(df_matched, use_container_width=True, hide_index=True)

            # Compute total distance saved
            total_distance_saved = df_matched["Distance Saved (km) ✅"].sum()

            # Display total below the DataFrame
            st.write(f"**🌍 Total Distance Saved:** `{total_distance_saved:.1f} km`")

            st.header("🔄 Alternative Cost")
            df_alter_rlane = pd.DataFrame()
            for _, row in df_matched.iterrows():
                im = row["Import 🔻"]
                ex = row["Export 🔺"]
                im_loc_cd = find_key_by_location_name(im.split("-")[0].strip(), location_dict)
                ex_loc_cd = find_key_by_location_name(ex.split("-")[0].strip(), location_dict)

                im_rate_lane = rlane_meta_data[im_loc_cd][ex_loc_cd]["im_rate_lane"]
                ex_rate_lane = rlane_meta_data[im_loc_cd][ex_loc_cd]["ex_rate_lane"]

                alter_rlane, _ = merge_result(im_rate_lane, ex_rate_lane)
                alter_rlane = alter_rlane[
                    [
                        "street_turn_import_lane_description",
                        "street_turn_import_vendor_cd",
                        "street_turn_export_lane_description",
                        "street_turn_export_vendor_cd",
                        "optimal",
                        "cost_save",
                    ]
                ]

                pair_df = pd.DataFrame({"Import 🔻": [im], "Export 🔺": [ex]})
                merged_df = pair_df.merge(alter_rlane, how="cross")

                df_alter_rlane = pd.concat([df_alter_rlane, merged_df], ignore_index=True)

            st.dataframe(df_alter_rlane, use_container_width=True, hide_index=True)

            # **🚀 Import & Export Selection (Main Section)**
            st.write("### 🏗 Select Import For Remove")
            col5, col6 = st.columns(2)

            with col5:
                import_selection_display = st.multiselect(
                    "Choose Import Locations - Booking Number",
                    options=list(import_name_mapping.keys()),
                    default=[],
                    help="Scroll or search to select import locations.",
                )

            # with col6:
            #     export_selection_display = st.multiselect(
            #         "Choose Export Locations - Booking Number",
            #         options=list(export_name_mapping.keys()),
            #         default=[],
            #         help="Scroll or search to select export locations."
            #     )

            # **Generate Route Button**
            if st.button("🚀 Generate"):
                st.session_state.generate_route_clicked = True  # Set flag

            # ✅ **Generate Route Processing if Button Clicked**
            if st.session_state.generate_route_clicked:
                import_selection = [import_name_mapping[name] for name in import_selection_display]
                import_selection_simplify = [simplify_name(name, location_dict) for name in import_selection]

                # Remove selected imports from df_matched
                df_matched_removed = df_matched[~df_matched["Import 🔻"].isin(import_selection_simplify)]

                if not import_selection_simplify:
                    st.warning("⚠️ Please select at least one import and one export location.")
                else:
                    st.success(f"✅ Removed {import_selection_simplify} matched pairs from the list.")

                    # Display updated DataFrame
                    st.header("📊 Updated Matched Import-Export Pairs")
                    st.dataframe(df_matched_removed, use_container_width=True, hide_index=True)

                    # Compute total distance saved after filtering
                    total_distance_saved = df_matched_removed["Distance Saved (km) ✅"].sum()
                    st.write(f"**🌍 Total Distance Saved After Filtering:** `{total_distance_saved:.1f} km`")

                    # 🎯 Alternative Exports DataFrame
                    alternative_exports = []

                    for im in df_matched_removed["Import 🔻"].tolist():
                        original_import_key = original_import_mapping.get(im)

                        if original_import_key and original_import_key in valid_pairs_dict_filtered:
                            already_matched_exports = df_matched_removed[df_matched_removed["Import 🔻"] == im][
                                "Export 🔺"
                            ].tolist()

                            sorted_exports = sorted(
                                valid_pairs_dict_filtered[original_import_key].items(),
                                key=lambda x: savings_dict.get((original_import_key, x[0]), 0),
                                reverse=True,
                            )

                            for ex, _ in sorted_exports:
                                if ex not in already_matched_exports:
                                    alternative_exports.append(
                                        {
                                            "Import 🔻": im,
                                            "Alternative Export": f"{location_dict[ex.split('_')[0]]['Location_name']}_{ex.split('_')[-1]}",
                                            "Distance Saved (km) ✅": savings_dict.get((original_import_key, ex), 0),
                                        }
                                    )

                    df_alternative_exports = pd.DataFrame(alternative_exports)

                    if not df_alternative_exports.empty:
                        st.header("🔄 Alternative Distance")
                        st.dataframe(df_alternative_exports, use_container_width=True, hide_index=True)
                    else:
                        st.info("No alternative exports available for the selected imports.")

                st.session_state.generate_route_clicked = False


if __name__ == "__main__":
    main()
