# import base64
import datetime as dt

from fastapi import APIRouter, BackgroundTasks, HTTPException
from pydantic import BaseModel

from archive.main_v2 import main, setup

router = APIRouter()


class PubSubMessage(BaseModel):
    """Model for the Pub/Sub message data."""

    data: str
    attributes: dict[str, str] | None = None
    message_id: str
    publish_time: str


class PubSubEnvelope(BaseModel):
    """Model for the complete Pub/Sub push message envelope."""

    message: PubSubMessage
    subscription: str


@router.post("/exec-code")
async def exec_code(envelope: PubSubEnvelope, background_tasks: BackgroundTasks):
    """Handle incoming Google Cloud Pub/Sub messages via HTTP push subscription."""
    try:
        print("receive message from pubsub")
        # data_decoded = base64.b64decode(envelope.message.data).decode("utf-8").strip()
        print(
            "Processing for message ID = ",
            envelope.message.message_id,
            " at ",
            envelope.message.publish_time,
        )

        output_dir = setup()
        datetime = dt.datetime.now(dt.timezone(dt.timedelta(hours=8))).strftime("%Y-%m-%dT%H:%M:%S")
        print(datetime)
        background_tasks.add_task(main, datetime, output_dir)

        return {"message": "Street Turn Matching Run Success"}  # Return 200 status code with success message

    except Exception as err:
        raise HTTPException(
            status_code=400, detail=f"Error processing message: {str(err)}"
        ) from err  # Added 'from err' to properly chain exceptions
