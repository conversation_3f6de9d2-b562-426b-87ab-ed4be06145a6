import base64
import json
import time
from typing import Any

import google.cloud.bigquery as bigquery
import pandas as pd
from google.oauth2 import service_account

from archive.src_v2.const.config import settings
from archive.src_v2.utils.exceptions import AuthenticationError, BigQueryError
from archive.src_v2.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


class BaseBigQueryManager:
    """Base class for BigQuery operations."""

    RETRY_ATTEMPTS = 3
    RETRY_DELAY = 1  # seconds

    def __init__(self) -> None:
        """Initialize the BigQueryInteraction class."""
        self.client = self.__authenticate_service_account()
        logger.info(f"{self.__class__.__name__} initialized for project: {settings.ENV.PROJECT_ID}")

    def __load_service_account(self) -> dict[str, Any]:
        """Load the service account info from the provided file."""
        logger.info(f"Loading GCP service account credentials for {self.__class__.__name__}")
        try:
            __base64_str = settings.ENV.SERVICE_ACCOUNT_ENCODE

            __decoded_bytes = base64.b64decode(__base64_str.get_secret_value())
            creds_dict = json.loads(__decoded_bytes.decode("utf-8"))
            logger.info(
                f"{self.__class__.__name__}: Successfully loaded service account credentials for project: {creds_dict.get('project_id')}"
            )
            return creds_dict
        except json.JSONDecodeError as e:
            error_msg = f"{self.__class__.__name__}: Invalid JSON format in decoded credentials: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e
        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to load service account credentials. Exception type: {type(e).__name__}, Details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e

    def __authenticate_service_account(self) -> bigquery.Client:
        try:
            logger.info(f"{self.__class__.__name__}: Authenticating with service account credentials")
            credentials = service_account.Credentials.from_service_account_info(self.__load_service_account())
            client = bigquery.Client(credentials=credentials, project=settings.ENV.PROJECT_ID)
            logger.info(f"{self.__class__.__name__}: Authentication successful for project {settings.ENV.PROJECT_ID}")
            return client
        except Exception as e:
            msg = f"{self.__class__.__name__}: Failed to authenticate with BigQuery: {str(e)}"
            logger.error(msg, exc_info=True)
            raise AuthenticationError(msg) from e

    def execute_query(
        self, query: str, job_config: bigquery.QueryJobConfig | None = None, **kwargs: Any
    ) -> pd.DataFrame:
        """Execute a BigQuery SQL query.

        Args:
            query (str): The SQL query to execute.
            job_config (bigquery.QueryJobConfig, optional): The configuration for the query job. Defaults to None.
            **kwargs: Additional keyword arguments to pass to the query method.

        Returns:
            pd.DataFrame: The results of the query.

        Raises:
            BigQueryError: If there's an error executing the query.
        """
        start_time = time.time()
        logger.info(f"Executing BigQuery query: {query}")

        try:
            query_job = self.client.query(query, job_config=job_config, **kwargs)
            job_id = query_job.job_id
            logger.info(f"Query job {job_id} started")

            results = query_job.result()
            df = results.to_dataframe()

            elapsed_time = time.time() - start_time
            row_count = len(df)
            logger.info(f"Query job {job_id} completed successfully in {elapsed_time:.2f}s, returning {row_count} rows")
            return df
        except Exception as e:
            elapsed_time = time.time() - start_time
            error_msg = f"Query execution failed after {elapsed_time:.2f}s.\nError type: {type(e).__name__}\nError details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise BigQueryError(f"Error executing query: {str(e)}") from e

    def _validate_dataset_table(self, dataset_id: str, table_id: str) -> None:
        logger.info(f"Validating dataset '{dataset_id}' and table '{table_id}'")
        try:
            self.client.get_dataset(dataset_id)
            logger.info(f"Dataset '{dataset_id}' exists")
            try:
                table_ref = f"{dataset_id}.{table_id}"
                self.client.get_table(table_ref)
                logger.info(f"Table '{table_ref}' exists")
            except Exception as e:
                logger.warning(f"Table validation failed for '{table_id}' in dataset '{dataset_id}': {str(e)}")
        except Exception as e:
            error_msg = f"Invalid dataset '{dataset_id}': {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg) from None
