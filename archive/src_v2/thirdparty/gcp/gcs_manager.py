"""A wrapper for the Google Cloud Storage client."""

import base64
import json
import os
from typing import Any

from google.cloud import storage
from google.oauth2 import service_account

from archive.src_v2.const.config import settings
from archive.src_v2.utils.exceptions import AuthenticationError
from archive.src_v2.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


class GCSManager:
    """Base class for Google Cloud Storage operations."""

    def __init__(self, bucket_name: str):
        """Initializes the GCSBucketWrapper.

        Args:
            bucket_name (str): The name of the bucket.
        """
        self.bucket_name: str = bucket_name
        self.client = self.__authenticate_service_account()
        self.bucket = self.client.get_bucket(bucket_or_name=bucket_name)
        logger.info(f"{self.__class__.__name__} initialized for project: {settings.ENV.PROJECT_ID}")

    def __load_service_account(self) -> dict[str, Any]:
        """Load the service account info from the provided file."""
        logger.info(f"Loading GCP service account credentials for {self.__class__.__name__}")
        try:
            __base64_str = settings.ENV.SERVICE_ACCOUNT_ENCODE

            __decoded_bytes = base64.b64decode(__base64_str.get_secret_value())
            creds_dict = json.loads(__decoded_bytes.decode("utf-8"))
            logger.info(
                f"{self.__class__.__name__}: Successfully loaded service account credentials for project: {creds_dict.get('project_id')}"
            )
            return creds_dict
        except json.JSONDecodeError as e:
            error_msg = f"{self.__class__.__name__}: Invalid JSON format in decoded credentials: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e
        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to load service account credentials. Exception type: {type(e).__name__}, Details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e

    def __authenticate_service_account(self) -> storage.Client:
        """Authenticate using the provided service account info and build the GCS client."""
        try:
            logger.info(f"{self.__class__.__name__}: Authenticating with service account credentials")
            credentials = service_account.Credentials.from_service_account_info(self.__load_service_account())
            client = storage.Client(credentials=credentials, project=settings.ENV.PROJECT_ID)
            logger.info(f"{self.__class__.__name__}: Authentication successful for project {settings.ENV.PROJECT_ID}")
            return client
        except Exception as e:
            msg = f"{self.__class__.__name__}: Failed to authenticate with GCS: {str(e)}"
            logger.error(msg, exc_info=True)
            raise AuthenticationError(msg) from e

    def upload_to_fname(self, source_fname: str, destination_blob_name: str):
        """Uploads a file to the bucket.

        Args:
            source_fname (str): The path to the file to upload.
            destination_blob_name (str): The name of the blob in the bucket.
        """
        blob = self.bucket.blob(blob_name=destination_blob_name)
        blob.upload_from_filename(filename=source_fname)

    def download_to_fname(self, source_blob_name: str, destination_fname: str):
        """Downloads a blob from the bucket.

        Args:
            source_blob_name (str): The name of the blob in the bucket.
            destination_fname (str): The path to save the downloaded file.
        """
        blob = self.bucket.blob(blob_name=source_blob_name)
        blob.download_to_filename(filename=destination_fname)

    def list_blobs(self) -> list:
        """Lists all the blobs in the bucket."""
        blobs = self.bucket.list_blobs()
        return [blob.name for blob in blobs]

    def upload_folder(self, source_folder: str, destination_folder: str | None = None):
        """Uploads a folder to the bucket.

        Args:
            source_folder (str): The path to the folder to upload.
            destination_folder (str): The name of the folder in the bucket.
        """
        for root, _, files in os.walk(source_folder):
            for file in files:
                local_path: str = os.path.join(root, file)
                blob_path: str = os.path.relpath(local_path, source_folder)
                if destination_folder:
                    blob_path = os.path.join(destination_folder, blob_path)

                self.upload_to_fname(source_fname=local_path, destination_blob_name=blob_path)

    def download_folder(self, source_folder: str, destination_folder: str):
        """Downloads a folder from the bucket.

        Args:
            source_folder (str): The name of the folder in the bucket.
            destination_folder (str): The path to save the downloaded folder.
        """
        blobs = self.list_blobs()
        for blob in blobs:
            if source_folder in blob:
                local_path = os.path.join(destination_folder, blob)
                self.download_to_fname(source_blob_name=blob, destination_fname=local_path)
