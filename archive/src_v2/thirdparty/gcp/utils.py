from datetime import datetime
from pathlib import Path

import pandas as pd
import pandas_gbq
from google.cloud import bigquery

from archive.src_v2.const import settings
from archive.src_v2.core.data_model.data_models import DataCollection
from archive.src_v2.utils.logger import log_handler
from archive.src_v2.utils.preprocess import find_key_by_location_name
from root import ROOT_DIR

from .gcs_manager import GCSManager

logger = log_handler.get_logger(name=__name__)


def sanitize_column_name(column: str) -> str:
    """Sanitize a column name by converting it to uppercase and replacing certain characters.

    Args:
        column (str): The original column name.

    Returns:
        str: The sanitized column name.
    """
    replacements = {
        " ": "_",
        "/": "_",
        "&": "and",
        ".": "",
        "?": "",
        "(": "",
        ")": "",
        "-": "_",
        "'": "",
        ",": "",
        "CONTAINER": "CNTR",
        "NUMBER": "NO",
        "BOOKING": "BKG",
    }

    result = column.upper()
    for old, new in replacements.items():
        result = result.replace(old, new)
    return result.upper()


def upload_to_gcs(output_dir: Path):
    """Upload output directory to Google Cloud Storage.

    Args:
        output_dir (Path): Path to the output directory
        run_report_dt (str): Report date used as folder name in GCS
    """
    # Upload to Google Cloud Storage
    gcs_manager = GCSManager(bucket_name=settings.GCP.STORAGE.BUCKET_NAME)
    destination_folder = str(output_dir.relative_to(ROOT_DIR))
    gcs_manager.upload_folder(source_folder=str(output_dir), destination_folder=destination_folder)
    logger.info(f"Output directory uploaded to GCS: {destination_folder}")


def upload_to_drive(output_dir: Path, run_report_dt: str):
    """Upload output directory to Google Drive.

    Args:
        output_dir (Path): Path to the output directory
        run_report_dt (str): Report date used as folder name in Drive
    """
    # TODO: Implement Google Drive upload when needed
    # drive_manager = GoogleDriveManager()
    # parent_folder_id = settings.GCP.DRIVE.PARENT_FOLDER_ID
    # drive_manager.upload_file(file_path=output_dir, parent_ids=[parent_folder_id])
    pass


def upload_run_info_to_bq(run_info: list[dict], project_id: str, table_id: str):
    """Upload run information to BigQuery.

    Args:
        run_info (list[dict]): List of dictionaries containing run information
        project_id (str): Google Cloud project ID
        table_id (str): Full BigQuery table ID in format 'project.dataset.table'
        run_id (str): Unique identifier for this run
    """
    df = pd.DataFrame(run_info)

    # Rename columns to match schema
    column_mapping = {
        "node_cd": "NODE_CD",
        "run_dt": "RUN_DATETIME",
        "time_available_gap": "TIME_AVAILABLE_GAP",
        "distance_thres": "DISTANCE_THRES",
        "no_cal_gmaps_api": "NO_CAL_GMAPS_API",
        "cache_len": "CACHE_LEN",
        "matches_count": "MATCHES_COUNT",
        "raw_pairs_count": "RAW_PAIRS_COUNT",
        "cntr_sz_valid_pairs_count": "CNTR_SZ_VALID_PAIRS_COUNT",
        "time_valid_pairs_count": "TIME_VALID_PAIRS_COUNT",
        "cntr_sz_time_valid_pairs_count": "CNTR_SZ_TIME_VALID_PAIRS_COUNT",
        # "dist_valid_pairs_count": "DIST_VALID_PAIRS_COUNT",
        "total_cost_save": "TOTAL_COST_SAVE_USD",
        "total_distance_save": "TOTAL_DISTANCE_SAVE_KM",
        "container_reuse": "CONTAINER_REUSE",
    }
    df = df.rename(columns=column_mapping)

    # Convert datetime columns
    df["RUN_DATETIME"] = pd.to_datetime(df["RUN_DATETIME"]).astype("datetime64[us]")

    # Add audit columns
    current_timestamp_sgt = pd.Timestamp.now(tz="Asia/Singapore")
    current_timestamp_utc = pd.Timestamp.now(tz="UTC")
    user_id = "SYSTEM"  # or get from environment/settings if available
    df["LOCL_CRE_DT"] = current_timestamp_sgt
    df["LOCL_UPD_DT"] = current_timestamp_sgt
    df["CRE_USR_ID"] = user_id
    df["CRE_DT"] = current_timestamp_utc
    df["UPD_USR_ID"] = user_id
    df["UPD_DT"] = current_timestamp_utc
    df["EDW_UPD_DT"] = current_timestamp_utc
    df["DELT_FLG"] = "N"

    # Ensure all required columns are present and in correct order
    required_columns = [
        "NODE_CD",
        "RUN_DATETIME",
        "TIME_AVAILABLE_GAP",
        "DISTANCE_THRES",
        "NO_CAL_GMAPS_API",
        "CACHE_LEN",
        "MATCHES_COUNT",
        "RAW_PAIRS_COUNT",
        "CNTR_SZ_VALID_PAIRS_COUNT",
        "TIME_VALID_PAIRS_COUNT",
        "CNTR_SZ_TIME_VALID_PAIRS_COUNT",
        # "DIST_VALID_PAIRS_COUNT",
        "TOTAL_COST_SAVE_USD",
        "TOTAL_DISTANCE_SAVE_KM",
        "CONTAINER_REUSE",
        "LOCL_CRE_DT",
        "LOCL_UPD_DT",
        "CRE_USR_ID",
        "CRE_DT",
        "UPD_USR_ID",
        "UPD_DT",
        "EDW_UPD_DT",
        "DELT_FLG",
    ]

    # Ensure all integer columns are of type int64
    int_columns = [
        "TIME_AVAILABLE_GAP",
        "DISTANCE_THRES",
        "NO_CAL_GMAPS_API",
        "CACHE_LEN",
        "MATCHES_COUNT",
        "RAW_PAIRS_COUNT",
        "CNTR_SZ_VALID_PAIRS_COUNT",
        "TIME_VALID_PAIRS_COUNT",
        "CNTR_SZ_TIME_VALID_PAIRS_COUNT",
        # "DIST_VALID_PAIRS_COUNT",
        "CONTAINER_REUSE",
    ]
    for col in int_columns:
        df[col] = df[col].astype("int64")

    df = df[required_columns]

    try:
        # Create BigQuery client
        client = bigquery.Client(project=project_id)
        dataset_id, table_name = table_id.split(".")
        dataset_ref = client.dataset(dataset_id)
        table_ref = dataset_ref.table(table_name)

        # Check if table exists, create if not
        try:
            client.get_table(table_ref)
            # Table exists, proceed with regular upload
            existing_df = pandas_gbq.read_gbq(f"SELECT * FROM {table_id}", project_id=project_id)  # noqa: S608
            if existing_df.empty:
                pandas_gbq.to_gbq(df, destination_table=table_id, project_id=project_id, if_exists="append")
            else:
                existing_df["RUN_DATETIME"] = existing_df["RUN_DATETIME"].dt.tz_localize(None)
                # assign delf_flag in df to Y if the row is in the existing_df
                existing_df.loc[
                    (
                        existing_df["NODE_CD"].isin(df["NODE_CD"])
                        & existing_df["RUN_DATETIME"].isin(df["RUN_DATETIME"])
                        & existing_df["TIME_AVAILABLE_GAP"].isin(df["TIME_AVAILABLE_GAP"])
                        & existing_df["DISTANCE_THRES"].isin(df["DISTANCE_THRES"])
                    ),
                    "DELT_FLG",
                ] = "Y"
                pandas_gbq.to_gbq(existing_df, destination_table=table_id, project_id=project_id, if_exists="replace")
                pandas_gbq.to_gbq(df, destination_table=table_id, project_id=project_id, if_exists="append")
        except Exception:
            # Table doesn't exist, create it with the DataFrame schema
            logger.info(f"Table {table_id} does not exist. Creating a new table.")
            pandas_gbq.to_gbq(df, destination_table=table_id, project_id=project_id, if_exists="fail")
            logger.info(f"Created table {table_id}.")

        logger.info(f"Successfully uploaded {len(df)} rows to BigQuery table: {table_id}")
    except Exception as e:
        logger.error(f"Failed to upload run info to BigQuery table: {table_id}.\nError: {e}", exc_info=True)


def upload_street_turn_to_bq(df: pd.DataFrame, meta_info: dict, project_id: str, table_id: str):
    """Upload street turn data to BigQuery.

    Args:
        df (pd.DataFrame): DataFrame containing street turn data
        meta_info (dict): Dictionary containing meta information
        project_id (str): Google Cloud project ID
        table_id (str): Full BigQuery table ID in format 'project.dataset.table'
    """
    if df.empty:
        logger.warning("No data available for upload. The DataFrame is empty. Skipping upload to BigQuery.")
        return

    column_mapping = {
        "Import BKG NO": "IMPORT_BKG_NO",
        "Import COP NO": "IMPORT_COP_NO",
        "Import CNTR NO": "IMPORT_CNTR_NO",
        "Export BKG NO": "EXPORT_BKG_NO",
        "Export COP NO": "EXPORT_COP_NO",
        "Export CNTR NO": "EXPORT_CNTR_NO",
        "Size Type": "SIZE_TYPE",
        "Shipline": "SHIPLINE",
        "Import Door City": "IMPORT_DOOR_CITY",
        "Import Door State": "IMPORT_DOOR_STATE",
        "Import Port Ramp": "IMPORT_PORT_RAMP",
        "Import Address": "IMPORT_ADDRESS",
        "Import Shipper": "IMPORT_SHIPPER",
        "Import Trucker": "IMPORT_TRUCKER",
        "Export Door City": "EXPORT_DOOR_CITY",
        "Export Door State": "EXPORT_DOOR_STATE",
        "Export Port Ramp": "EXPORT_PORT_RAMP",
        "Export Address": "EXPORT_ADDRESS",
        "Export Shipper": "EXPORT_SHIPPER",
        "Export Trucker": "EXPORT_TRUCKER",
        "Import Available Date": "IMPORT_AVAILABLE_DATE",
        "Import Expire Date": "IMPORT_EXPIRE_DATE",
        "Export Available Date": "EXPORT_AVAILABLE_DATE",
        "Export Expire Date": "EXPORT_EXPIRE_DATE",
        "Overlaps": "OVERLAPS",
        "Overlap Difference Days": "OVERLAP_DIFFERENCE_DAYS",
        "Distance Between Import and Export (km)": "DISTANCE_BETWEEN_IMPORT_AND_EXPORT_KM",
        "Street Turn Distance (km)": "STREET_TURN_DISTANCE_KM",
        "Round Trip Distance (km)": "ROUND_TRIP_DISTANCE_KM",
        "Distance Saved (km)": "DISTANCE_SAVED_KM",
        "street_turn_import_lane_description": "STREET_TURN_IMPORT_LANE_DESCRIPTION",
        "street_turn_import_vendor": "STREET_TURN_IMPORT_VENDOR_CD",
        "street_turn_import_carrier_cd": "STREET_TURN_IMPORT_CARRIER_CD",
        "street_turn_import_base_rate": "STREET_TURN_IMPORT_BASE_RATE",
        "street_turn_import_trip_type": "STREET_TURN_IMPORT_TRIP_TYPE",
        "street_turn_export_lane_description": "STREET_TURN_EXPORT_LANE_DESCRIPTION",
        "street_turn_export_vendor": "STREET_TURN_EXPORT_VENDOR_CD",
        "street_turn_export_carrier_cd": "STREET_TURN_EXPORT_CARRIER_CD",
        "street_turn_export_base_rate": "STREET_TURN_EXPORT_BASE_RATE",
        "street_turn_export_trip_type": "STREET_TURN_EXPORT_TRIP_TYPE",
        "street_turn_total_cost": "STREET_TURN_TOTAL_COST",
        "round_trip_import_lane_description": "ROUND_TRIP_IMPORT_LANE_DESCRIPTION",
        "round_trip_import_vendor": "ROUND_TRIP_IMPORT_VENDOR_CD",
        "round_trip_import_carrier_cd": "ROUND_TRIP_IMPORT_CARRIER_CD",
        "round_trip_import_base_rate": "ROUND_TRIP_IMPORT_BASE_RATE",
        "round_trip_import_trip_type": "ROUND_TRIP_IMPORT_TRIP_TYPE",
        "round_trip_export_lane_description": "ROUND_TRIP_EXPORT_LANE_DESCRIPTION",
        "round_trip_export_vendor": "ROUND_TRIP_EXPORT_VENDOR_CD",
        "round_trip_export_carrier_cd": "ROUND_TRIP_EXPORT_CARRIER_CD",
        "round_trip_export_base_rate": "ROUND_TRIP_EXPORT_BASE_RATE",
        "round_trip_export_trip_type": "ROUND_TRIP_EXPORT_TRIP_TYPE",
        "round_trip_total_cost": "ROUND_TRIP_TOTAL_COST",
        "optimal": "OPTIMAL",
        "cost_save": "COST_SAVE",
    }

    df = df.rename(columns=column_mapping)
    df["NODE_CD"] = meta_info["node_cd"]
    df["RUN_DATETIME"] = pd.to_datetime(meta_info["run_dt"])
    df["RUN_DATETIME"] = df["RUN_DATETIME"].astype("datetime64[us]")
    df["TIME_AVAILABLE_GAP"] = meta_info["time_available_gap"]
    df["DISTANCE_THRES"] = meta_info["distance_thres"]
    df["IMPORT_AVAILABLE_DATE"] = pd.to_datetime(df["IMPORT_AVAILABLE_DATE"])
    df["IMPORT_EXPIRE_DATE"] = pd.to_datetime(df["IMPORT_EXPIRE_DATE"])
    df["EXPORT_AVAILABLE_DATE"] = pd.to_datetime(df["EXPORT_AVAILABLE_DATE"])
    df["EXPORT_EXPIRE_DATE"] = pd.to_datetime(df["EXPORT_EXPIRE_DATE"])

    # Add audit columns
    current_timestamp_sgt = pd.Timestamp.now(tz="Asia/Singapore")
    current_timestamp_utc = pd.Timestamp.now(tz="UTC")
    user_id = "SYSTEM"  # or get from environment/settings if available
    df["LOCL_CRE_DT"] = current_timestamp_sgt
    df["LOCL_UPD_DT"] = current_timestamp_sgt
    df["CRE_USR_ID"] = user_id
    df["CRE_DT"] = current_timestamp_utc
    df["UPD_USR_ID"] = user_id
    df["UPD_DT"] = current_timestamp_utc
    df["EDW_UPD_DT"] = current_timestamp_utc
    df["DELT_FLG"] = "N"

    try:
        # Create BigQuery client
        client = bigquery.Client(project=project_id)
        dataset_id, table_name = table_id.split(".")
        dataset_ref = client.dataset(dataset_id)
        table_ref = dataset_ref.table(table_name)

        # Check if table exists, create if not
        try:
            client.get_table(table_ref)
            # Table exists, proceed with regular upload
            existing_df = pandas_gbq.read_gbq(f"SELECT * FROM {table_id}", project_id=project_id)  # noqa: S608
            if existing_df.empty:
                pandas_gbq.to_gbq(df, destination_table=table_id, project_id=project_id, if_exists="append")
            else:
                existing_df["RUN_DATETIME"] = existing_df["RUN_DATETIME"].dt.tz_localize(None)
                # assign delf_flag in df to Y if the row is in the existing_df
                existing_df.loc[
                    (
                        existing_df["NODE_CD"].isin(df["NODE_CD"])
                        & existing_df["RUN_DATETIME"].isin(df["RUN_DATETIME"])
                        & existing_df["TIME_AVAILABLE_GAP"].isin(df["TIME_AVAILABLE_GAP"])
                        & existing_df["DISTANCE_THRES"].isin(df["DISTANCE_THRES"])
                    ),
                    "DELT_FLG",
                ] = "Y"
                pandas_gbq.to_gbq(existing_df, destination_table=table_id, project_id=project_id, if_exists="replace")
                pandas_gbq.to_gbq(df, destination_table=table_id, project_id=project_id, if_exists="append")

        except Exception:
            # Table doesn't exist, create it with the DataFrame schema
            logger.info(f"Table {table_id} does not exist. Creating a new table.")
            pandas_gbq.to_gbq(df, destination_table=table_id, project_id=project_id, if_exists="fail")
            logger.info(f"Created table {table_id}.")

        logger.info(f"Successfully uploaded {len(df)} rows to BigQuery table: {table_id}")
    except Exception as e:
        logger.error(f"Failed to upload street turn data to BigQuery: {str(e)}", exc_info=True)


def upload_savings_to_bq(savings: dict, project_id: str, table_id: str, run_datetime: str):
    """Upload savings data to BigQuery with metadata.

    Args:
        savings (dict): Dictionary of savings with import-export pairs as keys and distance saved as values.
        project_id (str): Google Cloud project ID.
        table_id (str): Full BigQuery table ID in format 'project.dataset.table'.
        run_datetime (str): Datetime of the run (default: current time).
    """
    try:
        run_datetime = pd.to_datetime(run_datetime or datetime.now())

        # Convert savings dictionary to DataFrame
        savings_df = pd.DataFrame(
            [(key[0], key[1], value, run_datetime) for key, value in savings.items()],
            columns=["import_index", "export_index", "distance_saved_km", "run_datetime"],
        )

        # Ensure datetime conversion for BigQuery compatibility
        savings_df["run_datetime"] = pd.to_datetime(savings_df["run_datetime"])

        # Create BigQuery client
        client = bigquery.Client(project=project_id)
        dataset_id, table_name = table_id.split(".")
        dataset_ref = client.dataset(dataset_id)
        table_ref = dataset_ref.table(table_name)

        # Check if table exists, create if not
        try:
            client.get_table(table_ref)
        except Exception:
            schema = [
                bigquery.SchemaField("import_index", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("export_index", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("distance_saved_km", "FLOAT", mode="NULLABLE"),
                bigquery.SchemaField("run_datetime", "TIMESTAMP", mode="REQUIRED"),
            ]
            table = bigquery.Table(table_ref, schema=schema)
            client.create_table(table)
            print(f"Created table {table_id}.")

        # Upload to BigQuery
        pandas_gbq.to_gbq(savings_df, destination_table=table_id, project_id=project_id, if_exists="append")

        print(
            f"Successfully uploaded {len(savings_df)} rows to BigQuery table: {table_id} with run_datetime: {run_datetime}"
        )

    except Exception as e:
        print(f"Failed to upload savings data to BigQuery: {str(e)}")


def upload_valid_pairs_to_bq(valid_pairs: dict, project_id: str, table_id: str, run_datetime: str):
    """Upload valid pairs data to BigQuery with metadata.

    Args:
        valid_pairs (dict): Dictionary of valid import-export pairs.
        project_id (str): Google Cloud project ID.
        table_id (str): Full BigQuery table ID in format 'project.dataset.table'.
        run_datetime (str): Datetime of the run (default: current time).
    """
    try:
        run_datetime = pd.to_datetime(run_datetime or datetime.now())

        # Convert valid_pairs dictionary to DataFrame
        valid_pairs_df = pd.DataFrame(
            [
                (import_index, export_index, valid, run_datetime)
                for import_index, exports in valid_pairs.items()
                for export_index, valid in exports.items()
            ],
            columns=["import_index", "export_index", "valid", "run_datetime"],
        )

        # Ensure datetime conversion for BigQuery compatibility
        valid_pairs_df["run_datetime"] = pd.to_datetime(valid_pairs_df["run_datetime"])

        # Create BigQuery client
        client = bigquery.Client(project=project_id)
        dataset_id, table_name = table_id.split(".")
        dataset_ref = client.dataset(dataset_id)
        table_ref = dataset_ref.table(table_name)

        # Check if table exists, create if not
        try:
            client.get_table(table_ref)
        except Exception:
            schema = [
                bigquery.SchemaField("import_index", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("export_index", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("valid", "INTEGER", mode="REQUIRED"),
                bigquery.SchemaField("run_datetime", "TIMESTAMP", mode="REQUIRED"),
            ]
            table = bigquery.Table(table_ref, schema=schema)
            client.create_table(table)
            print(f"Created table {table_id}.")

        # Upload to BigQuery
        pandas_gbq.to_gbq(valid_pairs_df, destination_table=table_id, project_id=project_id, if_exists="append")

        print(
            f"Successfully uploaded {len(valid_pairs_df)} rows to BigQuery table: {table_id} with run_datetime: {run_datetime}"
        )

    except Exception as e:
        print(f"Failed to upload valid pairs data to BigQuery: {str(e)}")


def upload_raw_pairs_to_bq(
    node_cd: str, data: DataCollection, raw_pairs_info: dict, project_id: str, table_id: str, run_datetime: str
):
    """Upload raw pairs info data to BigQuery with metadata.

    Args:
        node_cd (str): Node code.
        data (DataCollection): Data collection containing report data.
        raw_pairs_info (dict): Dictionary of raw import-export pairs info.
        project_id (str): Google Cloud project ID.
        table_id (str): Full BigQuery table ID in format 'project.dataset.table'.
        run_datetime (str): Datetime of the run (default: current time).
    """
    try:
        run_datetime = pd.to_datetime(run_datetime or datetime.now())

        # Convert raw_pairs_info dictionary to DataFrame
        raw_pairs_info_df = pd.DataFrame(
            [
                (
                    node_cd,
                    find_key_by_location_name(
                        data.report_data.iloc[int(import_index.split("_")[1])]["location_city"], data.location_data
                    ),
                    find_key_by_location_name(
                        data.report_data.iloc[int(export_index.split("_")[1])]["location_city"], data.location_data
                    ),
                    data.report_data.iloc[int(import_index.split("_")[1])]["booking_no"],
                    data.report_data.iloc[int(export_index.split("_")[1])]["booking_no"],
                    data.report_data.iloc[int(import_index.split("_")[1])]["cop_no"],
                    data.report_data.iloc[int(export_index.split("_")[1])]["cop_no"],
                    import_index,
                    export_index,
                    info["distance_door_door"],
                    info["distance_saved"],
                    info["im_cntz_size"],
                    info["ex_cntz_size"],
                    info["time_gap"],
                )
                for import_index, exports in raw_pairs_info.items()
                for export_index, info in exports.items()
            ],
            columns=[
                "port_cd",
                "import_loc_cd",
                "export_loc_cd",
                "import_bkg_no",
                "export_bkg_no",
                "import_cop_no",
                "export_cop_no",
                "import_id",
                "export_id",
                "distance_door_door",
                "distance_saved",
                "import_cntz_size",
                "export_cntz_size",
                "time_gap",
            ],
        )

        # Ensure datetime conversion for BigQuery compatibility
        raw_pairs_info_df["run_datetime"] = pd.to_datetime(run_datetime)

        # Create BigQuery client
        client = bigquery.Client(project=project_id)
        dataset_id, table_name = table_id.split(".")
        dataset_ref = client.dataset(dataset_id)
        table_ref = dataset_ref.table(table_name)

        # Check if table exists, create if not
        try:
            client.get_table(table_ref)
        except Exception:
            schema = [
                bigquery.SchemaField("port_cd", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("import_loc_cd", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("export_loc_cd", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("import_bkg_no", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("export_bkg_no", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("import_cop_no", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("export_cop_no", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("import_id", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("export_id", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("distance_door_door", "FLOAT", mode="REQUIRED"),
                bigquery.SchemaField("distance_saved", "FLOAT", mode="REQUIRED"),
                bigquery.SchemaField("import_cntz_size", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("export_cntz_size", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("time_gap", "INTEGER", mode="REQUIRED"),
                bigquery.SchemaField("run_datetime", "TIMESTAMP", mode="REQUIRED"),
            ]
            table = bigquery.Table(table_ref, schema=schema)
            client.create_table(table)
            print(f"Created table {table_id}.")

        # Upload to BigQuery
        pandas_gbq.to_gbq(raw_pairs_info_df, destination_table=table_id, project_id=project_id, if_exists="append")

        print(
            f"Successfully uploaded {len(raw_pairs_info_df)} rows to BigQuery table: {table_id} with run_datetime: {run_datetime}"
        )

    except Exception as e:
        print(f"Failed to upload raw pairs info data to BigQuery: {str(e)}")
