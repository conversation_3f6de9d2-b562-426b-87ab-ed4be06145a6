import googlemaps

from archive.src_v2.const import settings
from archive.src_v2.utils.exceptions import GMapsAPIError
from archive.src_v2.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


class GMapsManager:
    """Manages Google Maps API interactions with caching and error handling."""

    client = googlemaps.Client(key=settings.ENV.GMAP_API_KEY.get_secret_value())

    def __init__(self):
        """Initialize GMapsManager with API key."""
        self.api_call_count = 0

        logger.info("GMapsManager initialized.")

    def get_distance_matrix(self, origin: str, destination: str, mode: str = "driving") -> dict:
        """Get distance matrix between origin and destination.

        Args:
            origin: Start location
            destination: End location
            mode: Travel mode (driving, walking, etc)

        Returns:
            dict: Distance matrix result from Google Maps API
        """
        # TODO: research on how to handle rate limiting and batch requests
        res = self.client.distance_matrix([origin], [destination], mode=mode, units="metric", language="en")
        self.api_call_count += 1
        return res

    def get_distance_duration(self, origin: str, destination: str) -> tuple[float, str]:
        """Get distance in meters and duration between two points.

        Args:
            origin: Start location
            destination: End location

        Returns:
            Tuple[float, str]: Distance in km and duration text
        """
        try:
            result = self.get_distance_matrix(origin, destination)
            element = result["rows"][0]["elements"][0]

            # Extract duration
            distance_text = element["distance"]["text"]

            # Extract distance in km or m
            distance_value = float(distance_text.replace(",", "").split()[0])
            if "km" in distance_text:
                distance_m = distance_value * 1000
            elif "m" in distance_text:
                distance_m = distance_value
            else:
                raise ValueError(f"Unknown distance unit in Google Maps API response. Got: {distance_text}")

            # TODO: handle convert duration in seconds
            duration_text = element["duration"]["text"]

            return distance_m, duration_text

        except Exception as e:
            logger.warning(f"Error getting distance matrix from {origin} and {destination} {e}")
            raise GMapsAPIError("Error getting distance matrix") from e

    def get_api_call_count(self) -> int:
        """Get total number of API calls made."""
        return self.api_call_count
