import os
from collections.abc import Sequence
from pathlib import Path

import google.cloud.bigquery as bigquery
import pandas as pd

from archive.src_v2.core.data_model.schemas import REPORT_RAW as REPORT_RAW_SCHEMA
from archive.src_v2.utils.file_utils import extract_datetime_from_filename
from archive.src_v2.utils.logger import log_handler

from .base_bq_manager import BaseBigQueryManager
from .utils import sanitize_column_name

logger = log_handler.get_logger(name=__name__)


class ReportBigQueryManager(BaseBigQueryManager):
    """Manager for report-specific BigQuery operations."""

    def load_report_raw_to_bq(self, csv_paths: Sequence[Path], dataset_id: str, table_id: str) -> None:
        """Load CSV files to BigQuery with validation.

        Args:
            csv_paths (Sequence[Path]): The list of file paths to load.
            dataset_id (str): _description_
            table_id (str): _description_
        """
        if not csv_paths:
            logger.warning("No files found to load.")
            return

        self._validate_paths(csv_paths)
        self._validate_dataset_table(dataset_id, table_id)

        table_ref = self.client.dataset(dataset_id).table(table_id)
        job_config = self._get_load_job_config()

        for fname in csv_paths:
            df = self._prepare_dataframe(fname)
            job = self.client.load_table_from_dataframe(df, table_ref, job_config=job_config)
            job.result()
            logger.info("Loaded %d rows into %s.%s", len(df), dataset_id, table_id)

    def _get_load_job_config(self) -> bigquery.LoadJobConfig:
        return bigquery.LoadJobConfig(
            schema=REPORT_RAW_SCHEMA,
            source_format=bigquery.SourceFormat.CSV,
            write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
            time_partitioning=bigquery.TimePartitioning(
                type_=bigquery.TimePartitioningType.DAY, field="EDW_UPD_DT", require_partition_filter=True
            ),
        )

    def _prepare_dataframe(self, file_path: Path) -> pd.DataFrame:
        df = pd.read_csv(file_path, sep="\t")
        df.rename(columns={col: sanitize_column_name(col) for col in df.columns}, inplace=True)

        df[df.columns[[17, 18, 19, 20]]] = df[df.columns[[17, 18, 19, 20]]].apply(pd.to_datetime, errors="coerce")
        # this was to fix the case where the value of time is outside the range of the datetime64 type, maybe due to typo in data (2424-10-24 00:01:00)
        df["CRE_DT"] = pd.Timestamp.now()
        df["UPD_DT"] = pd.Timestamp.now()
        df["DEL_DT"] = None
        df["EDW_UPD_DT"] = extract_datetime_from_filename(file_path.name)

        for col in [
            "REASON",
            "REMARK",
            "RATE_TYPE",
            "SERVICE_TYPE",
            "DOOR_APPOINTMENT_ESTIMATED",
            "DOOR_APPOINTMENT_ACTUAL",
            "BASE_RATE",
            "FUEL_SURCHARGE",
            "OTHER_RATES",
            "RATE_TOTAL",
            "RATE_CURRENCY",
        ]:
            if col in df.columns:
                df.drop(col, axis=1, inplace=True)

        return df

    def _validate_paths(self, paths: Sequence[Path]) -> None:
        for path in paths:
            if not path.exists():
                raise FileNotFoundError(f"File not found: {path}")
            if not os.access(path, os.R_OK):
                raise PermissionError(f"No read permission for file: {path}")

    def process_report_raw(self, edw_upd_dt_str: str, dataset_id: str, table_id: str, table_raw_id: str) -> None:
        """Process the report raw data by joining it with the DWL_COP_HIS table."""
        query = """
        INSERT INTO `{dataset_id}.{table_id}`
        (
            WITH utc_report AS(
                SELECT
                    *,
                    -- CASE
                    --   WHEN CNTR_NO IS NULL THEN "COMU0000000"
                    --   ELSE CNTR_NO
                    -- END CNTR_NO_CONVERT,
                    TIMESTAMP_ADD(EDW_UPD_DT, INTERVAL 4 HOUR) AS EDW_UPD_DT_UTC
                FROM `{dataset_id}.{table_raw_id}`
                WHERE EDW_UPD_DT = DATETIME("{edw_upd_dt}")
            ),
            ranked_report AS (
                SELECT
                    a.*,
                    b.COP_STS_CD,
                    b.BKG_STS_CD,
                    b.BKG_EVNT_TP_CD,
                    b.CRE_DT AS DWL_COP_HIS_CRE_DT
                FROM utc_report a
                LEFT JOIN `SOURCE_DATA.DWL_COP_HIS` b
                ON
                    a.COP_NO = b.COP_NO
                    AND a.BKG_NO = b.BKG_NO
                    -- AND a.CNTR_NO_CONVERT = b.CNTR_NO
                    AND a.EDW_UPD_DT_UTC >= b.CRE_DT
                QUALIFY RANK () OVER (PARTITION BY a.COP_NO, a.BKG_NO, a.EDW_UPD_DT_UTC ORDER BY b.CRE_DT DESC) = 1
            )
            SELECT *
            FROM ranked_report
        )
        """
        query = query.format(
            dataset_id=dataset_id,
            table_id=table_id,
            table_raw_id=table_raw_id,
            edw_upd_dt=edw_upd_dt_str,
        )
        query_job = self.client.query(query)
        query_job.result()  # Wait for the job to complete

        logger.info("Processed report raw data for EDW_UPD_DT: %s", edw_upd_dt_str)
