"""A wrapper for the Google Drive client."""

import base64
import io
import json
import time
from pathlib import Path
from typing import Any

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload, MediaIoBaseDownload

from archive.src_v2.const.config import settings
from archive.src_v2.utils.exceptions import AuthenticationError
from archive.src_v2.utils.logger import log_handler

logger = log_handler.get_logger(name=__name__)


class GoogleDriveManager:
    """A wrapper for the Google Drive client."""

    def __init__(self, chunksize: int = 5) -> None:
        """Initialize the GoogleDriveManager with service account credentials."""
        self.chunksize = chunksize
        self.config = settings.GCP.DRIVE
        self.service = self.__authenticate_service_account()
        logger.info(f"{self.__class__.__name__} initialized.")

    def __load_service_account(self) -> dict[str, Any]:
        """Load the service account info from the provided file."""
        logger.info(f"Loading GCP service account credentials for {self.__class__.__name__}")
        try:
            __base64_str = settings.ENV.SERVICE_ACCOUNT_ENCODE

            __decoded_bytes = base64.b64decode(__base64_str.get_secret_value())
            creds_dict = json.loads(__decoded_bytes.decode("utf-8"))
            logger.info(
                f"{self.__class__.__name__}: Successfully loaded service account credentials for project: {creds_dict.get('project_id')}"
            )
            return creds_dict
        except json.JSONDecodeError as e:
            error_msg = f"{self.__class__.__name__}: Invalid JSON format in decoded credentials: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e
        except Exception as e:
            error_msg = f"{self.__class__.__name__}: Failed to load service account credentials. Exception type: {type(e).__name__}, Details: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e

    def __authenticate_service_account(self):
        """Authenticate using the provided service account info and build the Drive service."""
        try:
            logger.info(f"{self.__class__.__name__}: Authenticating with service account credentials")
            credentials = service_account.Credentials.from_service_account_info(
                self.__load_service_account(), scopes=self.config.SCOPES
            )
            service = build(self.config.API_SERVICE_NAME, self.config.API_VERSION, credentials=credentials)
            logger.info(f"{self.__class__.__name__}: Authentication successful")
            return service
        except Exception as e:
            msg = f"{self.__class__.__name__}: Failed to authenticate with Google Drive: {str(e)}"
            logger.error(msg, exc_info=True)
            raise AuthenticationError(msg) from e

    def __execute_list_request(self, query: str, page_token: str | None, page_size: int) -> dict[str, Any]:
        """Execute a file list request to the Google Drive API.

        Args:
            query (str): The search query.
            page_token (Optional[str]): The token for the next page of results.
            page_size (int): The number of files to return per page.

        Returns:
            Dict[str, Any]: The API response containing the list of files.
        """
        return (
            self.service.files()
            .list(
                q=query,
                spaces="drive",
                fields="nextPageToken, files(id, name, modifiedTime, createdTime)",
                includeItemsFromAllDrives=True,
                supportsAllDrives=True,
                pageToken=page_token,
                pageSize=page_size,
                orderBy="createdTime desc",
            )
            .execute()
        )

    @staticmethod
    def __log_results(files: list[dict[str, Any]], parent_id: str, verbose: bool) -> None:
        """Log the results of a file list operation.

        Args:
            files (List[Dict[str, Any]]): The list of files to log.
            parent_id (str): The ID of the parent folder.
            verbose (bool): Whether to log detailed information about each file.
        """
        if verbose:
            for file in files:
                logger.info(
                    "Found file: %s: %s, created: %s, modified: %s",
                    file.get("id"),
                    file.get("name"),
                    file.get("createdTime"),
                    file.get("modifiedTime"),
                )
        logger.info("Found %d files in folder ID %s.", len(files), parent_id)

    def list_files(
        self, parent_id: str, page_size: int = 20, return_all: bool = False, verbose: bool = False
    ) -> list[dict[str, Any]]:
        """Search for files in a Google Drive folder.

        Args:
            parent_id (str): The ID of the parent folder to search in.
            page_size (int): The number of files to return per page.
            return_all (bool): Whether to return all files or just the first page.
            verbose (bool): Whether to log the files found.

        Returns:
            List[Dict[str, Any]]: The list of files if the search was successful. Otherwise, an empty list is
                returned.
        """
        start_time = time.time()
        query = f"'{parent_id}' in parents and trashed=false"
        files = []
        page_token = None

        try:
            logger.info(f"{self.__class__.__name__}: Listing files from parent_id: {parent_id}")
            while True:
                results = self.__execute_list_request(query, page_token, page_size)
                new_files = results.get("files", [])
                files.extend(new_files)

                if not return_all or not (page_token := results.get("nextPageToken")):
                    break
                self.__log_results(files, parent_id, verbose)

            elapsed_time = time.time() - start_time
            logger.info(
                f"{self.__class__.__name__}: Listed {len(files)} files from folder {parent_id} in {elapsed_time:.2f}s"
            )
            return files

        except HttpError as e:
            elapsed_time = time.time() - start_time
            msg = f"{self.__class__.__name__}: Error listing files after {elapsed_time:.2f}s. Error: {str(e)}"
            logger.error(msg, exc_info=True)
            return files

    def download_file(self, file_id: str, dest_fpath: Path) -> None:
        """Download a file from Google Drive.

        Args:
            file_id (str): The ID of the file to download.
            dest_fpath (Path): The path to save the downloaded file.
        """
        start_time = time.time()
        save_dir = dest_fpath.parent
        save_dir.mkdir(parents=True, exist_ok=True)

        try:
            logger.info(f"{self.__class__.__name__}: Downloading file {file_id} to {dest_fpath}")
            request = self.service.files().get_media(fileId=file_id, supportsAllDrives=True)
            fh = io.FileIO(dest_fpath, "wb")
            downloader = MediaIoBaseDownload(fh, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk(num_retries=3)
                logger.info(f"{self.__class__.__name__}: Download {int(status.progress() * 100)}%")

            fh.close()
            elapsed_time = time.time() - start_time
            logger.info(f"{self.__class__.__name__}: File downloaded to {dest_fpath} in {elapsed_time:.2f}s")

        except HttpError as e:
            elapsed_time = time.time() - start_time
            msg = f"{self.__class__.__name__}: Error downloading file {file_id} after {elapsed_time:.2f}s. Error: {str(e)}"
            logger.error(msg, exc_info=True)
            raise

        except OSError as e:
            elapsed_time = time.time() - start_time
            msg = f"{self.__class__.__name__}: Error writing file {file_id} after {elapsed_time:.2f}s. Error: {str(e)}"
            logger.error(msg, exc_info=True)
            raise

    def upload_file(self, file_path: Path, parent_ids: list[str]) -> dict[str, Any] | None:
        """Upload a file to Google Drive.

        Args:
            file_path (Path): The path to the file to upload.
            parent_ids (List[str]): The parent folder IDs to upload the file to.

        Returns:
            Optional[Dict[str, Any]]: The response from the Google Drive API if the upload was successful,
                otherwise None.
        """
        start_time = time.time()
        try:
            logger.info(f"{self.__class__.__name__}: Uploading file {file_path} to parent folders {parent_ids}")
            file_metadata = {"name": file_path.name, "parents": parent_ids}
            media = MediaFileUpload(
                filename=str(file_path),
                resumable=True,
                chunksize=self.chunksize * 1024 * 1024,
            )
            file = self.service.files().create(
                body=file_metadata, media_body=media, fields="id, name", supportsAllDrives=True
            )
            result = None
            while result is None:
                status, result = file.next_chunk()
                if status:
                    logger.info(f"{self.__class__.__name__}: Uploaded {int(status.progress() * 100)}%")

            elapsed_time = time.time() - start_time
            logger.info(
                f"{self.__class__.__name__}: File {result.get('name')} uploaded with ID {result.get('id')} in {elapsed_time:.2f}s"
            )
            return result

        except FileNotFoundError as e:
            elapsed_time = time.time() - start_time
            msg = f"{self.__class__.__name__}: File not found: {file_path} after {elapsed_time:.2f}s. Error: {str(e)}"
            logger.error(msg, exc_info=True)
            raise

        except HttpError as e:
            elapsed_time = time.time() - start_time
            msg = f"{self.__class__.__name__}: Error uploading file after {elapsed_time:.2f}s. Error: {str(e)}"
            logger.error(msg, exc_info=True)
            raise


if __name__ == "__main__":
    drive_manager = GoogleDriveManager()

    # List files in a folder
    folder_id = ""
    files = drive_manager.list_files(parent_id=folder_id, verbose=True)
