import urllib.request


def get_project_id() -> str:
    """Get the project id from the metadata server.

    Raises:
        ValueError: If the metadata server is not available.

    Returns:
        _type_: _description_
    """
    # Only works on runtime.
    url = "http://metadata.google.internal/computeMetadata/v1/project/project-id"
    if not url.startswith(("http:", "https:")):
        raise ValueError("URL must start with 'http:' or 'https:'")

    req = urllib.request.Request(url=url)  # noqa: S310
    req.add_header(key="Metadata-Flavor", val="Google")

    if req.type not in ("http", "https"):
        raise ValueError("URL scheme must be 'http' or 'https'")
    with urllib.request.urlopen(req) as response:  # noqa: S310
        project_id = response.read().decode()

    if not project_id:
        raise ValueError("Could not get a value for PROJECT_ID")

    return str(project_id)
