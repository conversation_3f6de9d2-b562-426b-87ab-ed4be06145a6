import asyncio
import functools
import logging
import time
from collections.abc import Callable
from typing import Any


def measure_time(func: Callable | None = None, *, logger: logging.Logger | None = None):
    """Flexible decorator that can be used with or without arguments.

    Usage:
        @measure_time
        def simple_function(x: int) -> int:
            return x * 2

        # Usage with custom logger
        custom_logger = logging.getLogger("my_app")
        @measure_time(logger=custom_logger)
        def function_with_logger(x: int, y: int) -> int:
            return x + y

        # Async function
        @measure_time(logger=custom_logger)
        async def async_function(x: int) -> int:
            await asyncio.sleep(1)
            return x
    """

    def actual_decorator(fn: Callable) -> Callable:
        # Use provided logger or get default logger
        _logger = logger or logging.getLogger(fn.__module__)

        @functools.wraps(fn)
        async def async_wrapper(*args, **kwargs) -> Any:
            start_time = time.perf_counter()
            try:
                result = await fn(*args, **kwargs)
                end_time = time.perf_counter()
                _logger.info(f"{fn.__name__} completed in {end_time - start_time:.4f} seconds")
                return result
            except Exception as e:
                end_time = time.perf_counter()
                _logger.error(
                    f"{fn.__name__} failed after {end_time - start_time:.4f} seconds.\nError: {e}", exc_info=True
                )
                raise

        @functools.wraps(fn)
        def sync_wrapper(*args, **kwargs) -> Any:
            start_time = time.perf_counter()
            try:
                result = fn(*args, **kwargs)
                end_time = time.perf_counter()
                _logger.info(f"{fn.__name__} completed in {end_time - start_time:.4f} seconds")
                return result
            except Exception as e:
                end_time = time.perf_counter()
                _logger.error(
                    f"{fn.__name__} failed after {end_time - start_time:.4f} seconds.\nError: {e}", exc_info=True
                )
                raise

        return async_wrapper if asyncio.iscoroutinefunction(fn) else sync_wrapper

    # Handle both @measure_time and @measure_time(logger=...)
    if func is not None:
        return actual_decorator(func)
    return actual_decorator


def retry_func(func, max_retry=10, logger: logging.Logger | None = None):
    """Retry the function `func` until it succeeds or max_retry is reached.

    Args:
        func (Callable): The function that needs to be retried.
        max_retry (int): Maximum retry of `func` function, default is `10`.
        logger (logging.Logger): Logger instance to log retry attempts.

    Returns:
        Callable: func

    Raises:
        RetryException: If retries exceeded than max_retry.
    """
    _logger = logger or logging.getLogger(func.__module__)

    for retry in range(1, max_retry + 1):
        try:
            return func()
        except Exception as e:
            _logger.info(f"Failed to call {func.func}, in retry({retry}/{max_retry}). Error: {e}", exc_info=True)
    # else:
    #     raise RetryError(e, max_retry)
