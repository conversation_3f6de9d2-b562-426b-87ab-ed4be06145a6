from typing import Any

from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, status
from fastapi.requests import Request
from fastapi.responses import JSONResponse
from pydantic import ValidationError as PydanticValidationError

ValidationException = PydanticValidationError | Exception


class AuthenticationError(Exception):
    """Custom exception for authentication errors."""

    pass


class BigQueryError(Exception):
    """Custom exception for BigQuery operations errors."""

    pass


class StorageError(Exception):
    """Custom exception for Storage operations errors."""

    pass


class GMapsAPIError(Exception):
    """Custom exception for Google Maps API errors."""

    pass


class GDriveAPIError(Exception):
    """Custom exception for Google Drive API errors."""

    pass


class RetryError(Exception):
    u_str = "Exception ({}) raised after {} tries."

    def __init__(self, exp, max_retry):
        """Initialize RetryException."""
        self.exp = exp
        self.max_retry = max_retry

    def __unicode__(self):
        """Return a string representation of RetryException."""
        return self.u_str.format(self.exp, self.max_retry)

    def __str__(self):
        """Return a string representation of RetryException."""
        return self.__unicode__()


class APIError(HTTPException):
    """Base API Exception."""

    status_code: int = 500
    error_code: str = "INTERNAL_ERROR"

    def __init__(self, message: str, detail: Any = None, data: Any = None, headers: dict = {}):  # noqa: B006
        """Initialize APIError."""
        super().__init__(status_code=self.status_code, detail=detail, headers=headers)
        self.message = message
        self.data = data


class BadRequestError(APIError):
    """400 Bad Request."""

    status_code = status.HTTP_400_BAD_REQUEST
    error_code = "BAD_REQUEST"


class UnauthorizedError(APIError):
    """401 Unauthorized."""

    status_code = status.HTTP_401_UNAUTHORIZED
    error_code = "UNAUTHORIZED"


class ForbiddenError(APIError):
    """403 Forbidden."""

    status_code = status.HTTP_403_FORBIDDEN
    error_code = "FORBIDDEN"


class NotFoundError(APIError):
    """404 Not Found."""

    status_code = status.HTTP_404_NOT_FOUND
    error_code = "NOT_FOUND"


class ValidationError(APIError):
    """422 Validation Error."""

    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    error_code = "VALIDATION_ERROR"


class ConflictError(APIError):
    """409 Conflict."""

    status_code = status.HTTP_409_CONFLICT
    error_code = "CONFLICT"


class RateLimitError(APIError):
    """429 Too Many Requests."""

    status_code = status.HTTP_429_TOO_MANY_REQUESTS
    error_code = "RATE_LIMIT"


class DatabaseError(APIError):
    """500 Database Error."""

    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_code = "DATABASE_ERROR"


class ExternalServiceError(APIError):
    """502 External Service Error."""

    status_code = status.HTTP_502_BAD_GATEWAY
    error_code = "EXTERNAL_SERVICE_ERROR"


class ServiceUnavailableError(APIError):
    """503 Service Unavailable."""

    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    error_code = "SERVICE_UNAVAILABLE"


async def api_error_handler(request: Request, exc: Any) -> JSONResponse:
    """Handle API errors."""
    return JSONResponse(status_code=exc.status_code, content={"success": False, "error": exc.message, "data": exc.data})


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions."""
    detail = str(exc) if request.app.debug else None

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "error": "The server encountered an error and could not complete your request.",
            "detail": detail,
        },
    )


def format_validation_errors(exc: PydanticValidationError) -> list[str]:
    """Format pydantic validation errors into readable messages."""
    error_messages = []
    for error in exc.errors():
        field = " -> ".join(str(x) for x in error["loc"])
        message = error["msg"]
        error_messages.append(f"{field}: {message}")
    return error_messages


async def pydantic_error_handler(request: Request, exc: ValidationException) -> JSONResponse:
    """Handle pydantic validation errors."""
    if isinstance(exc, PydanticValidationError):
        errors = format_validation_errors(exc)
        return JSONResponse(status_code=422, content={"message": "Validation Error", "error": errors})

    return JSONResponse(status_code=422, content={"message": "Validation Error", "error": str(exc)})
