import datetime as dt
import json
import pickle
from pathlib import Path

import tomli as tomllib

from .logger import log_handler

logger = log_handler.get_logger(name=__name__)


def extract_datetime_from_filename(filename: str) -> str:
    """Extract and format the datetime part from a filename.

    Args:
        filename (str): The filename containing the datetime part.

    Returns:
        str: The formatted datetime string in the format 'YYYY-MM-DD HH:MM:SS'.
    """
    # Extract the datetime part from the filename
    datetime_str = filename.split("_")[1].split(".")[0]
    # Convert to datetime object
    datetime_obj = dt.datetime.strptime(datetime_str, "%Y%m%d%H%M")
    # Format as YYYY-MM-DD HH:MM:SS
    return datetime_obj.strftime("%Y-%m-%d %H:%M:%S")


def load_toml_config(config_path: str) -> dict:
    """Load a TOML configuration file.

    Args:
        config_path (str): The path to the TOML configuration file.

    Returns:
        dict: The configuration settings.
    """
    try:
        with open(config_path, "rb") as f:
            config = tomllib.load(f)
        return config
    except FileNotFoundError:
        raise FileNotFoundError(f"Config file not found: {config_path}") from None
    except tomllib.TOMLDecodeError:
        raise ValueError("Invalid TOML format in config file") from None


def save_json(data: dict | list[dict], filename: Path):
    """Save data to a JSON file.

    Args:
        data (_type_): _description_
        filename (Path): _description_
    """
    # convert None to null
    with open(filename, "w") as f:
        json.dump(data, f, indent=4)


def load_json(filename: Path):
    """Load data from a JSON file.

    Args:
        filename (Path): _description_

    Returns:
        _type_: _description_
    """
    with open(filename) as f:
        data = json.load(f)
    return data


def save_dict_to_pickle(data: dict, file_path: str) -> None:
    """Save a dictionary to a pickle file.

    Args:
        data (dict): The dictionary to save.
        file_path (str): The path to the pickle file.
    """
    with open(file_path, "wb") as file:
        pickle.dump(data, file)


def load_pickle(file_path: Path) -> dict:
    """Load a dictionary from a pickle file.

    Args:
        file_path (Path): The path to the pickle file.

    Returns:
        dict: The loaded dictionary.
    """
    with open(file_path, "rb") as file:
        return pickle.load(file)  # noqa: S301
