import logging
from logging.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON>, TimedRotatingFileHandler
from pathlib import Path

from rich.logging import RichHandler

from archive.src_v2.const.config import settings


class Handlers:
    def __init__(self):
        """Initialize the log handlers."""
        self.formatter = logging.Formatter(settings.ENV.LOG_FORMATTER, datefmt="%Y-%m-%d %H:%M:%S")
        self.rotation = settings.ENV.LOG_ROTATION

    def get_console_handler(self):
        """Get a console handler."""
        # console_handler = logging.StreamHandler(sys.stdout.flush())
        console_handler = RichHandler(rich_tracebacks=True, markup=True)
        console_handler.setFormatter(logging.Formatter("%(module)s:%(funcName)s(%(lineno)d): %(message)s"))
        return console_handler

    def get_file_handler(self, log_filename: Path):
        """Get a file handler."""
        file_handler = TimedRotatingFileHandler(
            filename=log_filename,
            when=self.rotation,
            backupCount=10,
            encoding="utf-8",
        )
        file_handler.setFormatter(self.formatter)
        return file_handler

    def get_socket_handler(self):
        """Get a socket handler."""
        socket_handler = SocketHandler("127.0.0.1", 19996)  # default listening address
        return socket_handler

    def get_handlers(self):
        """Get all available handlers."""
        return [
            self.get_console_handler(),
            self.get_socket_handler(),
        ]
