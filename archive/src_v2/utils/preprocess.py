import datetime as dt
import math

import numpy as np
import pandas as pd
import pytz
from google.cloud import bigquery

from archive.src_v2.const import ConstMap
from archive.src_v2.thirdparty.gcp import GMapsManager
from archive.src_v2.utils.exceptions import GMapsAPIError
from archive.src_v2.utils.logger import log_handler

bq_client = bigquery.Client()
gmaps_manager = GMapsManager()

gg_api_call_count = 0
cache_distance: dict[tuple[str, str], float] = {}
cache_distance_api: dict[tuple[str, str], tuple[str | None, float, str, dt.datetime, dt.datetime]] = {}

logger = log_handler.get_logger(name=__name__)


def get_distance_in_meter(origin: str, destination: str, distance_method: str) -> float:
    """Calculate the distance in meters between two locations using the specified method.

    This function supports multiple distance calculation methods:
    - "random": Returns a random distance between 1000-10000 meters (for testing)
    - "google_maps": Uses Google Maps Distance Matrix API
    - "harversine": Not implemented yet, will raise NotImplementedError
    For unsupported methods, returns DEFAULT_DISTANCE.

    Args:
        origin (str): The origin location (e.g., "GAINESVILLE, GA")
        destination (str): The destination location (e.g., "POWDER SPRINGS, GA")
        distance_method (str): The method to calculate distance ("random", "google_maps", or "harversine")

    Returns:
        float: The calculated distance in meters. For unsupported methods, returns DEFAULT_DISTANCE.

    Raises:
        NotImplementedError: If the distance method is "harversine"
        GMapsAPIError: If there's an error with the Google Maps API call
    """
    if distance_method == "random":
        return float(np.random.randint(1000, 10000))

    if distance_method == "google_maps":
        try:
            # Call the Distance Matrix API
            distance_meters, duration_text = gmaps_manager.get_distance_duration(origin, destination)
        except GMapsAPIError as e:
            raise e

        current_dt: dt.datetime = dt.datetime.now(pytz.timezone("Asia/Singapore"))
        cache_distance_api[(origin, destination)] = (
            "all",
            distance_meters,
            duration_text,
            current_dt,
            current_dt,
        )
        return float(distance_meters)

    if distance_method == "harversine":
        logger.error(
            f"Unsupported distance method: {distance_method}. Only 'random' and 'google_maps' are supported.",
            exc_info=True,
        )
        raise NotImplementedError("Harversine distance calculation is not implemented yet.")

    return ConstMap.DEFAULT_DISTANCE


def get_distance_between_locations(
    origin: str, destination: str, distance_data: pd.DataFrame, distance_method: str
) -> float:
    """Calculate the distance between two locations using cached data or distance calculation.

    This function follows a hierarchical approach to find the distance:
    1. Check if distance is in cache
    2. Look up in pre-calculated distance data (both origin->destination and destination->origin)
    3. Calculate using specified method via get_distance_in_meter
    4. Fall back to DEFAULT_DISTANCE if calculation fails

    The calculated distances are cached for both directions (origin->destination and destination->origin).

    Args:
        origin (str): The origin location name/address
        destination (str): The destination location name/address
        distance_data (pd.DataFrame): DataFrame containing pre-calculated distances with columns:
            - origin: Origin location
            - destination: Destination location
            - distance: Distance in meters
        distance_method (str): Method to calculate distance - "random", "google_maps", or "harversine"

    Returns:
        float: The distance in meters between the locations. Returns DEFAULT_DISTANCE if calculation fails.
    """
    if (origin, destination) in cache_distance:
        return float(cache_distance[(origin, destination)])

    distance_row = distance_data[(distance_data["origin"] == origin) & (distance_data["destination"] == destination)]
    if distance_row.empty:
        distance_row = distance_data[
            (distance_data["origin"] == destination) & (distance_data["destination"] == origin)
        ]

    if distance_row.empty:
        try:
            distance: float = get_distance_in_meter(origin, destination, distance_method)
        except (GMapsAPIError, NotImplementedError):
            distance = ConstMap.DEFAULT_DISTANCE
    else:
        distance = float(distance_row.iloc[0]["distance"])

    cache_distance[(origin, destination)] = distance
    cache_distance[(destination, origin)] = distance

    return distance


def find_key_by_location_name(location_name: str, locations_dict: dict) -> str | None:
    """Find the location key in a dictionary by matching the location name.

    Searches through a dictionary of location data to find an entry where the
    location_name matches the provided name.

    Args:
        location_name (str): The location name to search for
        locations_dict (dict): Dictionary mapping location keys to location details.
            Expected format: {key: {"location_name": str, ...}, ...}

    Returns:
        str | None: The key for the matching location, or None if not found
    """
    for key, value in locations_dict.items():
        if value["location_name"] == location_name:
            return key
    return None


def get_total_distance_v2(row, location_data, distance_data, im_address, ex_address, bound: str, distance_method: str):
    """Calculate total distance between a sequence of locations using distance dataset or Google Maps API.

    Computes the distance between city yard and door + door to city yard, handling import/export separately.

    Args:
        row: DataFrame row containing the lane description
        location_data: Dictionary mapping location codes to location details
        distance_data: DataFrame containing pre-calculated distances
        im_address: Import door address
        ex_address: Export door address
        bound: Direction - "im" for import or "ex" for export
        distance_method: Method to calculate distance

    Returns:
        float: Total calculated distance in meters

    Raises:
        ValueError: If bound is not "im" or "ex"
    """
    if bound not in ["im", "ex"]:
        raise ValueError("bound must be in ['im', 'ex']")
    cy_to_door: float = ConstMap.DEFAULT_DISTANCE
    door_to_cy: float = ConstMap.DEFAULT_DISTANCE
    loc1, loc2, loc3 = row["lane_description"].split("_")
    # Get value from via_point1 if available
    loc3 = row["via_point1"] if not pd.isna(row["via_point1"]) else loc1

    loc1_name = location_data[loc1[:5]]["location_name"]
    loc2_name = location_data[loc2[:5]]["location_name"]
    loc3_name = location_data[loc3[:5]]["location_name"]

    if bound == "im":
        cy_to_door = get_distance_between_locations(loc1_name, im_address, distance_data, distance_method)
        door_to_cy = get_distance_between_locations(im_address, loc3_name, distance_data, distance_method)

    elif bound == "ex":
        cy_to_door = get_distance_between_locations(loc1_name, ex_address, distance_data, distance_method)
        door_to_cy = get_distance_between_locations(ex_address, loc3_name, distance_data, distance_method)

    if cy_to_door == ConstMap.DEFAULT_DISTANCE:
        cy_to_door = get_distance_between_locations(loc1_name, loc2_name, distance_data, distance_method)

    if door_to_cy == ConstMap.DEFAULT_DISTANCE:
        door_to_cy = get_distance_between_locations(loc2_name, loc3_name, distance_data, distance_method)

    return cy_to_door + door_to_cy


def get_distance_saved_v2(
    importland, exportland, location_data, distance_data, im_address, ex_address, distance_method: str
) -> float:
    """Calculate total distance saved by street turn compared to round trip.

    Computes the difference between standard round trip distance and street turn distance
    between import and export locations.

    Args:
        importland: Import lane description
        exportland: Export lane description
        location_data: Dictionary mapping location codes to location details
        distance_data: DataFrame containing pre-calculated distances
        im_address: Import door address
        ex_address: Export door address
        distance_method: Method to calculate distance

    Returns:
        float: Distance saved in meters by using street turn instead of round trip
    """
    iloc1, iloc2, _ = importland.split("_")
    eloc1, eloc2, _ = exportland.split("_")

    iloc1_name = location_data[iloc1[:5]]["location_name"]
    iloc2_name = location_data[iloc2[:5]]["location_name"]

    eloc1_name = location_data[eloc1[:5]]["location_name"]
    eloc2_name = location_data[eloc2[:5]]["location_name"]

    cy_to_imdoor: float = get_distance_between_locations(im_address, iloc1_name, distance_data, distance_method)
    if cy_to_imdoor == ConstMap.DEFAULT_DISTANCE:
        cy_to_imdoor = get_distance_between_locations(iloc1_name, iloc2_name, distance_data, distance_method)
    imdoor_to_exdoor: float = get_distance_between_locations(im_address, ex_address, distance_data, distance_method)
    if imdoor_to_exdoor == ConstMap.DEFAULT_DISTANCE:
        imdoor_to_exdoor = get_distance_between_locations(iloc2_name, eloc1_name, distance_data, distance_method)

    exdoor_to_cy: float = get_distance_between_locations(ex_address, eloc1_name, distance_data, distance_method)
    if exdoor_to_cy == ConstMap.DEFAULT_DISTANCE:
        exdoor_to_cy = get_distance_between_locations(eloc1_name, eloc2_name, distance_data, distance_method)

    return cy_to_imdoor + imdoor_to_exdoor + exdoor_to_cy


def get_run_info() -> tuple[int, int]:
    """Get the total number of API calls made to the Google Maps API."""
    return gmaps_manager.get_api_call_count(), len(cache_distance)


def _insert_cached_pairs_to_bq(client, table_ref, cache_distance_api):
    """Insert new (origin, destination) pairs from the cache into a BigQuery table.

    This function checks if a BigQuery table exists. If not, it creates the table and inserts
    the new cached pairs (origin, destination) along with additional information like label,
    distance, duration, creation date, and update date.

    Args:
        client (bigquery.Client): A BigQuery client instance to execute queries.
        table_ref (str): The BigQuery table reference where the data should be inserted.
        cache_distance_api (dict): A dictionary with key as (origin, destination) and value as
                                   (label, distance_km, duration, current_dt, current_dt).

    Returns:
        None
    """
    # Check if the table exists, if not, create one
    try:
        client.get_table(table_ref)  # Make an API request to get the table
        logger.info(f"Table {table_ref} exists.")
    except Exception:
        # Create the table if not found
        logger.info(f"Table {table_ref} not found. Creating a new table.")
        schema = [
            bigquery.SchemaField("origin", "STRING", mode="REQUIRED"),
            bigquery.SchemaField("destination", "STRING", mode="REQUIRED"),
            bigquery.SchemaField("category", "STRING", mode="NULLABLE"),
            bigquery.SchemaField("distance", "FLOAT", mode="NULLABLE"),
            bigquery.SchemaField("duration", "STRING", mode="NULLABLE"),
            # bigquery.SchemaField("LOC_LAT", "STRING", mode="NULLABLE", description="Location Latitude"),
            bigquery.SchemaField(
                "cre_at",
                "DATETIME",
                mode="NULLABLE",
                description="Creation Datetime",
                default_value_expression="CURRENT_DATETIME('Singapore')",
            ),
            bigquery.SchemaField(
                "upd_at",
                "DATETIME",
                mode="NULLABLE",
                description="Update Datetime",
                default_value_expression="CURRENT_DATETIME('Singapore')",
            ),
            # bigquery.SchemaField("DEL_AT", "DATETIME", mode="NULLABLE", description="Delete Datetime"),
        ]
        table = bigquery.Table(table_ref, schema=schema)
        client.create_table(table)
        logger.info(f"Created table {table_ref}.")

    # Prepare data to be inserted
    rows_to_insert = []
    count = 0
    current_dt: dt.datetime = dt.datetime.now(pytz.timezone("Asia/Singapore"))
    for (origin, destination), (label, distance_km, duration, created_at, updated_at) in cache_distance_api.items():
        # Ensure that created_at and updated_at are datetime objects, otherwise handle them
        if isinstance(created_at, dt.datetime):
            created_at_str = created_at.strftime("%Y-%m-%dT%H:%M:%S")
        else:
            created_at_str = current_dt.strftime("%Y-%m-%dT%H:%M:%S")

        if isinstance(updated_at, dt.datetime):
            updated_at_str = updated_at.strftime("%Y-%m-%dT%H:%M:%S")
        else:
            updated_at_str = current_dt.strftime("%Y-%m-%dT%H:%M:%S")

        # Prepare row for insertion
        row = {
            "origin": origin,
            "destination": destination,
            "category": label,
            "distance": distance_km,
            "duration": duration,
            "cre_at": created_at_str,  # Correct datetime format
            "upd_at": updated_at_str,  # Correct datetime format
        }
        rows_to_insert.append(row)
        count += 1

    # Insert data into BigQuery table
    try:
        client.insert_rows_json(table_ref, rows_to_insert)
        logger.info(f"Added {count} new pairs to the {table_ref}")
    except Exception as e:
        logger.error(f"Errors occurred while inserting rows.\nError: {e}", exc_info=True)


def insert_cached_pairs_to_bq():
    """_summary_."""
    if len(cache_distance_api) == 0:
        logger.info("No new cached pairs to insert.")
        return

    _insert_cached_pairs_to_bq(
        bq_client,
        table_ref="one-global-dilab-matchback-dev.REPORT.DISTANCES_TEST",
        cache_distance_api=cache_distance_api,
    )


def log_scale(value):
    """_summary_."""
    return math.log1p(value)
