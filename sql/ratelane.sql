SELECT DISTINCT
    LaneDescription,
    ShipperCode,
    OriginLocationCode,
    OriginLocationName,
    OriginLocationType,
    GeoHierarchyOrigin,
    OriginAddress1,
    OriginAddress2,
    OriginCity,
    OriginState,
    OriginZIP,
    OriginZoneCode,
    OriginMetroName,
    OriginCountry,
    DestinationLocationCode,
    DestinationLocationName,
    DestinationLocationType,
    GeoHierarchyDestination,
    DestinationAddress1,
    DestinationAddress2,
    DestinationCity,
    DestinationState,
    DestinationZIP,
    DestinationZoneCode,
    DestinationMetroName,
    DestinationCountry,
    CarrierCode,
    VendorCode,
    CustomerCode,
    CommodityClass,
    IsHazmat,
    EquipmentClass,
    sztp.mapsize `EquipmentSizeType`,
    FORMAT_DATETIME ('%m/%d/%Y', EffectiveDate) EffectiveDate,
    FORMAT_DATETIME ('%m/%d/%Y', ExpiryDate) ExpiryDate,
    RateBasis,
    BaseRate,
    MinimumCharge,
    CurrencyCode,
    TripType,
    RateMinDistance,
    RateMaxDistance,
    DistanceUOM,
    RateType,
    Comments,
    Incoterm,
    Mode,
    CASE
        WHEN vp.viayard IS NOT NULL THEN vp.viayard
        ELSE xx.vp1
    END `ViaPoint1`,
    ViaPoint2,
    BuyingTerms,
    ContractRefCode,
    Representative,
    MinimumBracketValue,
    MaximumBracketValue,
    TieredRate,
    TieredBracketUom,
    TieredRateCurrencyCode,
    OriginLatitude,
    OriginLongitude,
    DestinationLatitude,
    DestinationLongitude
FROM
    (
        SELECT
            *
        FROM
            ratelane_sub1
        UNION DISTINCT
        SELECT
            *
        FROM
            ratelane_sub2
        UNION DISTINCT
        SELECT
            *
        FROM
            ratelane_sub3
        UNION DISTINCT
        SELECT
            *
        FROM
            ratelane_sub4
    ) xx
    LEFT OUTER JOIN (
        SELECT
            'ALAL' sizetype,
            NULL mapsize
        UNION DISTINCT
        SELECT
            'D2' sizetype,
            '20ST' mapsize
        UNION DISTINCT
        SELECT
            'D3' sizetype,
            '20HC' mapsize
        UNION DISTINCT
        SELECT
            'D4' sizetype,
            '40ST' mapsize
        UNION DISTINCT
        SELECT
            'D5' sizetype,
            '40HC' mapsize
        UNION DISTINCT
        SELECT
            'D7' sizetype,
            '45HC' mapsize
        UNION DISTINCT
        SELECT
            'F2' sizetype,
            '20FB' mapsize
        UNION DISTINCT
        SELECT
            'F4' sizetype,
            '40FR' mapsize
        UNION DISTINCT
        SELECT
            'F5' sizetype,
            '45FB' mapsize
        UNION DISTINCT
        SELECT
            'O2' sizetype,
            '20OT' mapsize
        UNION DISTINCT
        SELECT
            'O4' sizetype,
            '40OT' mapsize
        UNION DISTINCT
        SELECT
            'P4' sizetype,
            '40FB' mapsize
        UNION DISTINCT
        SELECT
            'P7' sizetype,
            '45FB' mapsize
        UNION DISTINCT
        SELECT
            'R2' sizetype,
            '20RF' mapsize
        UNION DISTINCT
        SELECT
            'R4' sizetype,
            '40RF' mapsize
        UNION DISTINCT
        SELECT
            'R5' sizetype,
            '40HR' mapsize
        UNION DISTINCT
        SELECT
            'R7' sizetype,
            '45HR' mapsize
        UNION DISTINCT
        SELECT
            'T2' sizetype,
            '20TK' mapsize
        UNION DISTINCT
        SELECT
            'T4' sizetype,
            '40TK' mapsize
        UNION DISTINCT
        SELECT
            'AL7' sizetype,
            '45HC' mapsize
        UNION DISTINCT
        SELECT
            'AL7' sizetype,
            '45FB' mapsize
        UNION DISTINCT
        SELECT
            'AL7' sizetype,
            '45HR' mapsize
        UNION DISTINCT
        SELECT
            'TAL' sizetype,
            '20TK' mapsize
        UNION DISTINCT
        SELECT
            'TAL' sizetype,
            '40TK' mapsize
        UNION DISTINCT
        SELECT
            'AL4' sizetype,
            '40ST' mapsize
        UNION DISTINCT
        SELECT
            'AL4' sizetype,
            '40FR' mapsize
        UNION DISTINCT
        SELECT
            'AL4' sizetype,
            '40OT' mapsize
        UNION DISTINCT
        SELECT
            'AL4' sizetype,
            '40FB' mapsize
        UNION DISTINCT
        SELECT
            'AL4' sizetype,
            '40RF' mapsize
        UNION DISTINCT
        SELECT
            'FAL' sizetype,
            '20FB' mapsize
        UNION DISTINCT
        SELECT
            'FAL' sizetype,
            '40FR' mapsize
        UNION DISTINCT
        SELECT
            'FAL' sizetype,
            '45FB' mapsize
        UNION DISTINCT
        SELECT
            'PAL' sizetype,
            '40FB' mapsize
        UNION DISTINCT
        SELECT
            'PAL' sizetype,
            '45FB' mapsize
        UNION DISTINCT
        SELECT
            'AL5' sizetype,
            '40HC' mapsize
        UNION DISTINCT
        SELECT
            'AL5' sizetype,
            '45FB' mapsize
        UNION DISTINCT
        SELECT
            'AL5' sizetype,
            '40HR' mapsize
        UNION DISTINCT
        SELECT
            'RAL' sizetype,
            '20RF' mapsize
        UNION DISTINCT
        SELECT
            'RAL' sizetype,
            '40RF' mapsize
        UNION DISTINCT
        SELECT
            'RAL' sizetype,
            '40HR' mapsize
        UNION DISTINCT
        SELECT
            'RAL' sizetype,
            '45HR' mapsize
        UNION DISTINCT
        SELECT
            'DAL' sizetype,
            '20ST' mapsize
        UNION DISTINCT
        SELECT
            'DAL' sizetype,
            '20HC' mapsize
        UNION DISTINCT
        SELECT
            'DAL' sizetype,
            '40ST' mapsize
        UNION DISTINCT
        SELECT
            'DAL' sizetype,
            '40HC' mapsize
        UNION DISTINCT
        SELECT
            'DAL' sizetype,
            '45HC' mapsize
        UNION DISTINCT
        SELECT
            'OAL' sizetype,
            '20OT' mapsize
        UNION DISTINCT
        SELECT
            'OAL' sizetype,
            '40OT' mapsize
    ) sztp ON (xx.ESZTP = sztp.sizetype)
    LEFT OUTER JOIN (
        SELECT
            'USAVD' viap,
            'USAVD30' viayard
        UNION DISTINCT
        SELECT
            'USAWM' viap,
            'USAWM31' viayard
        UNION DISTINCT
        SELECT
            'USAWM' viap,
            'USAWM32' viayard
        UNION DISTINCT
        SELECT
            'USAWN' viap,
            'USAWN60' viayard
        UNION DISTINCT
        SELECT
            'USCHI' viap,
            'USCHI40' viayard
        UNION DISTINCT
        SELECT
            'USCHI' viap,
            'USCHI77' viayard
        UNION DISTINCT
        SELECT
            'USCHS' viap,
            'USCHS01' viayard
        UNION DISTINCT
        SELECT
            'USCHS' viap,
            'USCHS02' viayard
        UNION DISTINCT
        SELECT
            'USCLT' viap,
            'USCLT61' viayard
        UNION DISTINCT
        SELECT
            'USCLT' viap,
            'USCLT32' viayard
        UNION DISTINCT
        SELECT
            'USESL' viap,
            'USESL32' viayard
        UNION DISTINCT
        SELECT
            'USEZA' viap,
            'USEZA32' viayard
        UNION DISTINCT
        SELECT
            'USHOU' viap,
            'USHOU01' viayard
        UNION DISTINCT
        SELECT
            'USIND' viap,
            'USIND41' viayard
        UNION DISTINCT
        SELECT
            'USKCK' viap,
            'USKCK63' viayard
        UNION DISTINCT
        SELECT
            'USKCK' viap,
            'USKCK65' viayard
        UNION DISTINCT
        SELECT
            'USLAX' viap,
            'USLAX01' viayard
        UNION DISTINCT
        SELECT
            'USLAX' viap,
            'USLAX03' viayard
        UNION DISTINCT
        SELECT
            'USLAX' viap,
            'USLAX06' viayard
        UNION DISTINCT
        SELECT
            'USLAX' viap,
            'USLAX08' viayard
        UNION DISTINCT
        SELECT
            'USLGB' viap,
            'USLGB02' viayard
        UNION DISTINCT
        SELECT
            'USLGB' viap,
            'USLGB05' viayard
        UNION DISTINCT
        SELECT
            'USMEM' viap,
            'USMEM67' viayard
        UNION DISTINCT
        SELECT
            'USMKC' viap,
            'USMKC32' viayard
        UNION DISTINCT
        SELECT
            'USMSY' viap,
            'USMSY01' viayard
        UNION DISTINCT
        SELECT
            'USNYC' viap,
            'USNYC01' viayard
        UNION DISTINCT
        SELECT
            'USNYC' viap,
            'USNYC05' viayard
        UNION DISTINCT
        SELECT
            'USNYC' viap,
            'USNYC06' viayard
        UNION DISTINCT
        SELECT
            'USOAK' viap,
            'USOAK01' viayard
        UNION DISTINCT
        SELECT
            'USOAK' viap,
            'USOAK08' viayard
        UNION DISTINCT
        SELECT
            'USOMA' viap,
            'USOMA31' viayard
        UNION DISTINCT
        SELECT
            'USSTL' viap,
            'USSTL60' viayard
        UNION DISTINCT
        SELECT
            'USSXT' viap,
            'USSXT40' viayard
        UNION DISTINCT
        SELECT
            'USUQF' viap,
            'USUQF30' viayard
        UNION DISTINCT
        SELECT
            'USPDS' viap,
            'USPDS30' viayard
        UNION DISTINCT
        SELECT
            'USHDO' viap,
            'USHDO31' viayard
        UNION DISTINCT
        SELECT
            'USEEU' viap,
            'USEEU31' viayard
        UNION DISTINCT
        SELECT
            'USWTN' viap,
            'USWTN32' viayard
        UNION DISTINCT
        SELECT
            'USPHX' viap,
            'USPHX33' viayard
        UNION DISTINCT
        SELECT
            'USCMH' viap,
            'USCMH31' viayard
        UNION DISTINCT
        SELECT
            'USCMH' viap,
            'USCMH41' viayard
        UNION DISTINCT
        SELECT
            'USCMH' viap,
            'USCMH60' viayard
        UNION DISTINCT
        SELECT
            'USSEA' viap,
            'USSEA01' viayard
        UNION DISTINCT
        SELECT
            'USSEA' viap,
            'USSEA33' viayard
        UNION DISTINCT
        SELECT
            'USSEA' viap,
            'USSEA35' viayard
        UNION DISTINCT
        SELECT
            'USPDX' viap,
            'USPDX31' viayard
        UNION DISTINCT
        SELECT
            'USPDX' viap,
            'USPDX32' viayard
        UNION DISTINCT
        SELECT
            'USPDX' viap,
            'USPDX63' viayard
        UNION DISTINCT
        SELECT
            'USTIW' viap,
            'USTIW02' viayard
        UNION DISTINCT
        SELECT
            'USTIW' viap,
            'USTIW05' viayard
        UNION DISTINCT
        SELECT
            'USTIW' viap,
            'USTIW31' viayard
        UNION DISTINCT
        SELECT
            'USDEN' viap,
            'USDEN61' viayard
        UNION DISTINCT
        SELECT
            'USPHL' viap,
            'USPHL01' viayard
        UNION DISTINCT
        SELECT
            'USPHL' viap,
            'USPHL02' viayard
        UNION DISTINCT
        SELECT
            'USPHL' viap,
            'USPHL62' viayard
        UNION DISTINCT
        SELECT
            'USBOS' viap,
            'USBOS01' viayard
        UNION DISTINCT
        SELECT
            'USBOS' viap,
            'USBOS61' viayard
        UNION DISTINCT
        SELECT
            'USMRV' viap,
            'USMRV61' viayard
        UNION DISTINCT
        SELECT
            'USMRV' viap,
            'USMRV31' viayard
        UNION DISTINCT
        SELECT
            'USSAV' viap,
            'USSAV01' viayard
        UNION DISTINCT
        SELECT
            'USDET' viap,
            'USDET31' viayard
        UNION DISTINCT
        SELECT
            'USDET' viap,
            'USDET65' viayard
        UNION DISTINCT
        SELECT
            'USDET' viap,
            'USDET61' viayard
        UNION DISTINCT
        SELECT
            'USCVG' viap,
            'USCVG34' viayard
        UNION DISTINCT
        SELECT
            'USCVG' viap,
            'USCVG61' viayard
        UNION DISTINCT
        SELECT
            'USCVG' viap,
            'USCVG63' viayard
        UNION DISTINCT
        SELECT
            'USCVG' viap,
            'USCVG36' viayard
        UNION DISTINCT
        SELECT
            'USPIT' viap,
            'USPIT61' viayard
        UNION DISTINCT
        SELECT
            'USORF' viap,
            'USORF01' viayard
        UNION DISTINCT
        SELECT
            'USORF' viap,
            'USORF03' viayard
        UNION DISTINCT
        SELECT
            'USORF' viap,
            'USORF61' viayard
        UNION DISTINCT
        SELECT
            'USAWP' viap,
            'USAWP30' viayard
        UNION DISTINCT
        SELECT
            'USFRR' viap,
            'USFRR61' viayard
        UNION DISTINCT
        SELECT
            'USFRR' viap,
            'USFRR80' viayard
        UNION DISTINCT
        SELECT
            'USGXX' viap,
            'USGXX61' viayard
        UNION DISTINCT
        SELECT
            'USGXX' viap,
            'USGXX30' viayard
        UNION DISTINCT
        SELECT
            'USGRR' viap,
            'USGRR31' viayard
        UNION DISTINCT
        SELECT
            'USGRR' viap,
            'USGRR80' viayard
        UNION DISTINCT
        SELECT
            'USHRY' viap,
            'USHRY40' viayard
        UNION DISTINCT
        SELECT
            'USHRY' viap,
            'USHRY41' viayard
        UNION DISTINCT
        SELECT
            'USHRY' viap,
            'USHRY60' viayard
        UNION DISTINCT
        SELECT
            'USSAU' viap,
            'USSAU30' viayard
        UNION DISTINCT
        SELECT
            'USBNA' viap,
            'USBNA30' viayard
        UNION DISTINCT
        SELECT
            'USBNA' viap,
            'USBNA31' viayard
        UNION DISTINCT
        SELECT
            'USBNA' viap,
            'USBNA32' viayard
        UNION DISTINCT
        SELECT
            'USBNA' viap,
            'USBNA33' viayard
        UNION DISTINCT
        SELECT
            'USBNA' viap,
            'USBNA34' viayard
        UNION DISTINCT
        SELECT
            'USBNA' viap,
            'USBNA35' viayard
        UNION DISTINCT
        SELECT
            'USBNA' viap,
            'USBNA36' viayard
        UNION DISTINCT
        SELECT
            'USBNA' viap,
            'USBNA61' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI31' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI32' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI33' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI35' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI36' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI37' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI38' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI39' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI42' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI61' viayard
        UNION DISTINCT
        SELECT
            'USLUI' viap,
            'USLUI63' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP30' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP30' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP31' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP32' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP33' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP34' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP35' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP36' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP37' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP39' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP41' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP42' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP43' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP30' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP60' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP61' viayard
        UNION DISTINCT
        SELECT
            'USELP' viap,
            'USELP64' viayard
        UNION DISTINCT
        SELECT
            'USPEF' viap,
            'USPEF03' viayard
        UNION DISTINCT
        SELECT
            'USPEF' viap,
            'USPEF01' viayard
        UNION DISTINCT
        SELECT
            'USPEF' viap,
            'USPEF04' viayard
        UNION DISTINCT
        SELECT
            'USPEF' viap,
            'USPEF30' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA02' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA03' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA31' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA40' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA41' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA42' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA43' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA44' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA45' viayard
        UNION DISTINCT
        SELECT
            'USMIA' viap,
            'USMIA61' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX46' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX45' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX44' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX43' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX42' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX41' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX40' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX39' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX38' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX37' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX36' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX35' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX34' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX33' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX32' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX31' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX30' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX03' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX02' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX01' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX47' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX50' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX61' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX62' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX63' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX64' viayard
        UNION DISTINCT
        SELECT
            'USJAX' viap,
            'USJAX65' viayard
        UNION DISTINCT
        SELECT
            'USCHA' viap,
            'USCHA30' viayard
        UNION DISTINCT
        SELECT
            'USPTM' viap,
            'USPTM30' viayard
        UNION DISTINCT
        SELECT
            'USPTM' viap,
            'USPTM31' viayard
        UNION DISTINCT
        SELECT
            'USPTM' viap,
            'USPTM32' viayard
        UNION DISTINCT
        SELECT
            'USPTM' viap,
            'USPTM33' viayard
        UNION DISTINCT
        SELECT
            'USPTM' viap,
            'USPTM34' viayard
        UNION DISTINCT
        SELECT
            'USPTM' viap,
            'USPTM61' viayard
        UNION DISTINCT
        SELECT
            'USPTM' viap,
            'USPTM62' viayard
        UNION DISTINCT
        SELECT
            'USPTM' viap,
            'USPTM63' viayard
        UNION DISTINCT
        SELECT
            'USZD1' viap,
            'USZD160' viayard
        UNION DISTINCT
        SELECT
            'USZD1' viap,
            'USZD130' viayard
        UNION DISTINCT
        SELECT
            'USSAT' viap,
            'USSAT31' viayard
        UNION DISTINCT
        SELECT
            'USSAT' viap,
            'USSAT32' viayard
        UNION DISTINCT
        SELECT
            'USSAT' viap,
            'USSAT33' viayard
        UNION DISTINCT
        SELECT
            'USSAT' viap,
            'USSAT41' viayard
        UNION DISTINCT
        SELECT
            'USSAT' viap,
            'USSAT61' viayard
        UNION DISTINCT
        SELECT
            'USSAT' viap,
            'USSAT62' viayard
        UNION DISTINCT
        SELECT
            'USSAT' viap,
            'USSAT63' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM11' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM31' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM33' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM34' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM35' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM36' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM60' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM61' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM62' viayard
        UNION DISTINCT
        SELECT
            'USBHM' viap,
            'USBHM70' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL11' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL12' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL17' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL18' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL31' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL32' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL33' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL34' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL35' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL36' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL43' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL44' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL61' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL62' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL63' viayard
        UNION DISTINCT
        SELECT
            'USATL' viap,
            'USATL64' viayard
        UNION DISTINCT
        SELECT
            'USHSV' viap,
            'USHSV31' viayard
        UNION DISTINCT
        SELECT
            'USHSV' viap,
            'USHSV32' viayard
        UNION DISTINCT
        SELECT
            'USHSV' viap,
            'USHSV61' viayard
        UNION DISTINCT
        SELECT
            'USPFX' viap,
            'USPFX31' viayard
        UNION DISTINCT
        SELECT
            'USPFX' viap,
            'USPFX32' viayard
        UNION DISTINCT
        SELECT
            'USRIC' viap,
            'USRIC01' viayard
        UNION DISTINCT
        SELECT
            'USRIC' viap,
            'USRIC31' viayard
        UNION DISTINCT
        SELECT
            'USRIC' viap,
            'USRIC32' viayard
        UNION DISTINCT
        SELECT
            'USWXR' viap,
            'USWXR31' viayard
        UNION DISTINCT
        SELECT
            'USWXR' viap,
            'USWXR32' viayard
        UNION DISTINCT
        SELECT
            'USTXH' viap,
            'USTXH30' viayard
        UNION DISTINCT
        SELECT
            'USTXH' viap,
            'USTXH31' viayard
        UNION DISTINCT
        SELECT
            'USTXH' viap,
            'USTXH61' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL11' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL30' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL31' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL33' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL34' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL35' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL36' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL37' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL38' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL39' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL42' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL50' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL60' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL61' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL62' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL63' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL64' viayard
        UNION DISTINCT
        SELECT
            'USDAL' viap,
            'USDAL65' viayard
        UNION DISTINCT
        SELECT
            'CASLA' viap,
            'CASLA30' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL10' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL11' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL30' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL31' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL32' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL33' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL34' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL35' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL36' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL37' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL39' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL40' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL81' viayard
        UNION DISTINCT
        SELECT
            'CADEL' viap,
            'CADEL82' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN01' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN02' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN03' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN04' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN12' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN15' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN16' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN31' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN32' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN33' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN34' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN35' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN39' viayard
        UNION DISTINCT
        SELECT
            'CAVAN' viap,
            'CAVAN60' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR01' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR02' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR04' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR05' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR07' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR09' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR10' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR31' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR32' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR33' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR34' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR40' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR41' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR42' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR43' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR61' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR62' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR63' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR64' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR65' viayard
        UNION DISTINCT
        SELECT
            'CAMTR' viap,
            'CAMTR80' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC01' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC30' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC31' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC32' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC33' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC36' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC80' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC81' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC83' viayard
        UNION DISTINCT
        SELECT
            'CARBC' viap,
            'CARBC84' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS01' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS30' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS31' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS32' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS33' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS34' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS35' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS36' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS37' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS40' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS41' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS61' viayard
        UNION DISTINCT
        SELECT
            'CAMIS' viap,
            'CAMIS62' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR02' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR03' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR04' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR05' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR31' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR32' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR35' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR37' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR60' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR61' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR64' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR65' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR66' viayard
        UNION DISTINCT
        SELECT
            'CATOR' viap,
            'CATOR67' viayard
        UNION DISTINCT
        SELECT
            'CAHAL' viap,
            'CAHAL01' viayard
        UNION DISTINCT
        SELECT
            'CAHAL' viap,
            'CAHAL02' viayard
        UNION DISTINCT
        SELECT
            'CAHAL' viap,
            'CAHAL03' viayard
        UNION DISTINCT
        SELECT
            'CAHAL' viap,
            'CAHAL30' viayard
        UNION DISTINCT
        SELECT
            'CAHAL' viap,
            'CAHAL31' viayard
        UNION DISTINCT
        SELECT
            'CAHAL' viap,
            'CAHAL32' viayard
        UNION DISTINCT
        SELECT
            'CAHAL' viap,
            'CAHAL61' viayard
        UNION DISTINCT
        SELECT
            'CAHAL' viap,
            'CAHAL62' viayard
        UNION DISTINCT
        SELECT
            'CAHAL' viap,
            'CAHAL63' viayard
        UNION DISTINCT
        SELECT
            'CABLT' viap,
            'CABLT31' viayard
        UNION DISTINCT
        SELECT
            'CADAR' viap,
            'CADAR31' viayard
        UNION DISTINCT
        SELECT
            'CADAR' viap,
            'CADAR33' viayard
        UNION DISTINCT
        SELECT
            'CADAR' viap,
            'CADAR34' viayard
        UNION DISTINCT
        SELECT
            'CADAR' viap,
            'CADAR35' viayard
        UNION DISTINCT
        SELECT
            'CADAR' viap,
            'CADAR36' viayard
        UNION DISTINCT
        SELECT
            'CADAR' viap,
            'CADAR37' viayard
        UNION DISTINCT
        SELECT
            'CADAR' viap,
            'CADAR38' viayard
        UNION DISTINCT
        SELECT
            'CADAR' viap,
            'CADAR39' viayard
        UNION DISTINCT
        SELECT
            'USSAN' viap,
            'USSAN01' viayard
        UNION DISTINCT
        SELECT
            'USSAN' viap,
            'USSAN30' viayard
        UNION DISTINCT
        SELECT
            'USSAN' viap,
            'USSAN31' viayard
        UNION DISTINCT
        SELECT
            'USSAN' viap,
            'USSAN32' viayard
        UNION DISTINCT
        SELECT
            'CAKBG' viap,
            'CAKBG31' viayard
        UNION DISTINCT
        SELECT
            'CAKBG' viap,
            'CAKBG32' viayard
        UNION DISTINCT
        SELECT
            'CAKBG' viap,
            'CAKBG33' viayard
        UNION DISTINCT
        SELECT
            'CAKBG' viap,
            'CAKBG34' viayard
    ) vp ON (xx.VP1 = vp.viap)
WHERE
    (
        xx.upd_dt > DATETIME_SUB (CURRENT_DATETIME (), INTERVAL 29 HOUR)
        OR xx.scgh_upd_dt > DATETIME_SUB (CURRENT_DATETIME (), INTERVAL 29 HOUR)
    )
