SELECT
    an.to_nod_cd || '_' || an.dor_nod_cd || '_' || an.fm_nod_cd `LaneDescription`,
    'ONEY-CHI' `ShipperCode`,
    locationd.loc_cd `OriginLocationCode`,
    CASE
        WHEN instr (locationd.loc_nm, ',') > 0 THEN UPPER(
            TRIM(REGEXP_SUBSTR (locationd.loc_nm, '[^,]+', 1, 1))
        )
        ELSE UPPER(locationd.loc_nm)
    END `OriginLocationName`,
    'DC' `OriginLocationType`,
    'city-state' `GeoHierarchyOrigin`,
    NULL `OriginAddress1`,
    NULL `OriginAddress2`,
    CASE
        WHEN instr (locationd.loc_nm, ',') > 0 THEN TRIM(REGEXP_SUBSTR (locationd.loc_nm, '[^,]+', 1, 1))
        ELSE locationd.loc_nm
    END `OriginCity`,
    locationd.ste_cd `OriginState`,
    NULL `OriginZIP`,
    locationd.cnt_cd `OriginCountry`,
    NULL `OriginZoneCode`,
    NULL `OriginMetroName`,
    CASE
        WHEN length (an.to_nod_cd) = 5 THEN location.loc_cd
        ELSE an.to_nod_cd
    END `DestinationLocationCode`,
    CASE
        WHEN length (an.to_nod_cd) = 5
        AND instr (location.loc_nm, ',') > 0 THEN UPPER(
            TRIM(REGEXP_SUBSTR (location.loc_nm, '[^,]+', 1, 1))
        )
        WHEN instr (yardf.yd_nm, ',') > 0 THEN UPPER(TRIM(REGEXP_SUBSTR (yardf.yd_nm, '[^,]+', 1, 1)))
        ELSE UPPER(yardf.yd_nm)
    END `DestinationLocationName`,
    'CONTAINER_YARD' `DestinationLocationType`,
    CASE
        WHEN length (an.to_nod_cd) = 5 THEN 'city-state'
        ELSE 'location'
    END `GeoHierarchyDestination`,
    CASE
        WHEN length (an.to_nod_cd) = 5 THEN NULL
        ELSE CASE
            WHEN instr (yardf.yd_locl_lang_addr, ',') > 0 THEN TRIM(
                REGEXP_SUBSTR (yardf.yd_locl_lang_addr, '[^,]+', 1, 1)
            )
            ELSE TRIM(yardf.yd_locl_lang_addr)
        END
    END `DestinationAddress1`,
    CASE
        WHEN length (an.to_nod_cd) = 5 THEN NULL
        ELSE CASE
            WHEN instr (yardf.yd_locl_lang_addr, ',') > 0 THEN TRIM(
                REGEXP_SUBSTR (yardf.yd_locl_lang_addr, '[^,]+', 1, 2)
            )
            ELSE NULL
        END
    END `DestinationAddress2`,
    CASE
        WHEN length (an.to_nod_cd) = 5
        AND instr (location.loc_nm, ',') > 0 THEN TRIM(REGEXP_SUBSTR (location.loc_nm, '[^,]+', 1, 1))
        WHEN instr (yardf.yd_loc_cty_nm, ',') > 0 THEN TRIM(
            REGEXP_SUBSTR (yardf.yd_loc_cty_nm, '[^,]+', 1, 1)
        )
        ELSE yardf.yd_loc_cty_nm
    END `DestinationCity`,
    CASE
        WHEN length (an.to_nod_cd) = 5 THEN location.ste_cd
        ELSE yardf.YD_LOC_STE_CD
    END `DestinationState`,
    CASE
        WHEN length (an.to_nod_cd) = 5 THEN NULL
        ELSE yardf.zip_cd
    END `DestinationZIP`,
    location.cnt_cd `DestinationCountry`,
    NULL `DestinationZoneCode`,
    NULL `DestinationMetroName`,
    vendor.usa_edi_cd || '-' || substr (locationcar.scc_cd, 3, 3) `CarrierCode`,
    NULL `CustomerCode`,
    NULL `CommodityClass`,
    CASE
        WHEN scgh.trsp_scg_cd IS NULL THEN 'FALSE'
        ELSE NULL
    END `IsHazmat`,
    NULL `EquipmentClass`,
    ar.TRSP_AGMT_EQ_TP_SZ_CD ESZTP,
    ar.eff_fm_dt `EffectiveDate`,
    ar.eff_to_dt `ExpiryDate`,
    'flat' `RateBasis`,
    CASE
        WHEN ar.TRSP_RND_RT IS NOT NULL THEN ar.TRSP_RND_RT
        ELSE ar.TRSP_ONE_WY_RT
    END `BaseRate`,
    CASE
        WHEN ar.TRSP_RND_RT IS NOT NULL THEN ar.TRSP_RND_RT
        ELSE ar.TRSP_ONE_WY_RT
    END `MinimumCharge`,
    ar.CURR_CD `CurrencyCode`,
    CASE
        WHEN ar.TRSP_RND_RT IS NOT NULL THEN 'RETURN'
        ELSE 'ONE WAY'
    END `TripType`,
    NULL `RateMinDistance`,
    NULL `RateMaxDistance`,
    NULL `DistanceUOM`,
    'Cost' `RateType`,
    replace (ar.USR_DEF_RMK, ',', ' ') `Comments`,
    NULL `Incoterm`,
    'Dray' `Mode`,
    CASE
        WHEN length (an.fm_nod_cd) = length (an.to_nod_cd)
        AND an.fm_nod_cd = an.to_nod_cd
        AND UPPER(locationd.loc_cd) IN ('USTIW', 'USSEA') THEN an.fm_nod_cd
        WHEN length (an.fm_nod_cd) = length (an.to_nod_cd)
        AND an.fm_nod_cd = an.to_nod_cd
        AND an.fm_nod_cd IN ('USTIW', 'USSEA') THEN an.fm_nod_cd
        WHEN length (an.fm_nod_cd) = length (an.to_nod_cd)
        AND an.fm_nod_cd = an.to_nod_cd THEN NULL
        WHEN length (an.fm_nod_cd) = length (an.to_nod_cd)
        AND an.fm_nod_cd <> an.to_nod_cd THEN an.fm_nod_cd
        WHEN length (an.fm_nod_cd) = 7 THEN an.fm_nod_cd
        WHEN length (an.fm_nod_cd) = 5
        AND substr (an.fm_nod_cd, 1, 5) <> substr (an.to_nod_cd, 1, 5) THEN an.fm_nod_cd
        WHEN length (an.fm_nod_cd) = 5
        AND substr (an.fm_nod_cd, 1, 5) = substr (an.to_nod_cd, 1, 5)
        AND substr (an.fm_nod_cd, 1, 5) IN ('USTIW', 'USSEA') THEN an.fm_nod_cd
        ELSE NULL
    END VP1,
    NULL `ViaPoint2`,
    NULL `BuyingTerms`,
    NULL `ContractRefCode`,
    av.vndr_seq `VendorCode`,
    'FALSE' `Representative`,
    NULL `MinimumBracketValue`,
    NULL `MaximumBracketValue`,
    NULL `TieredRate`,
    NULL `TieredBracketUom`,
    NULL `TieredRateCurrencyCode`,
    0 `OriginLatitude`,
    0 `OriginLongitude`,
    0 `DestinationLatitude`,
    0 `DestinationLongitude`,
    CASE
        WHEN length (an.fm_nod_cd) = length (an.to_nod_cd)
        AND an.fm_nod_cd = an.to_nod_cd THEN 'Y'
        WHEN length (an.fm_nod_cd) = 7
        AND length (an.to_nod_cd) = 5
        AND substr (an.fm_nod_cd, 1, 5) = an.to_nod_cd THEN 'Y'
        WHEN length (an.fm_nod_cd) = 5
        AND length (an.to_nod_cd) = 7
        AND an.fm_nod_cd = substr (an.to_nod_cd, 1, 5) THEN 'Y'
        ELSE 'Y'
    END Incl_rate,
    scgh.trsp_scg_cd,
    ar.upd_dt,
    scgh.upd_dt scgh_upd_dt
FROM
    OPUS.DWL_AGMT_APLY_VNDR av
    INNER JOIN OPUS.DWL_TRSP_AGMT_HDR ah ON (
        av.trsp_agmt_seq = ah.trsp_agmt_seq
        AND av.trsp_agmt_ofc_cty_cd = ah.trsp_agmt_ofc_cty_cd
    )
    INNER JOIN OPUS.DWL_AGMT_EQ_RT ar ON (
        av.trsp_agmt_seq = ar.trsp_agmt_seq
        AND av.trsp_agmt_ofc_cty_cd = ar.trsp_agmt_ofc_cty_cd
    )
    INNER JOIN OPUS.DWL_AGMT_RT_TP art ON (
        av.trsp_agmt_seq = art.trsp_agmt_seq
        AND av.trsp_agmt_ofc_cty_cd = art.trsp_agmt_ofc_cty_cd
        AND art.trsp_agmt_rt_tp_ser_no = ar.trsp_agmt_rt_tp_ser_no
        AND art.trsp_cost_mod_cd = 'DR'
        AND art.agmt_trsp_tp_cd = 'TD'
        AND art.cgo_tp_cd = 'F'
    )
    INNER JOIN OPUS.DWL_AGMT_NOD an ON (
        av.trsp_agmt_seq = an.trsp_agmt_seq
        AND av.trsp_agmt_ofc_cty_cd = an.trsp_agmt_ofc_cty_cd
        AND ar.trsp_agmt_nod_seq = an.trsp_agmt_nod_seq
        AND substr (an.fm_nod_cd, 1, 2) IN ('US', 'CA')
    )
    INNER JOIN OPUS.DWC_VENDOR vendor ON (
        vendor.VNDR_SEQ = av.vndr_seq
        AND vendor.VNDR_CNT_CD IN ('US', 'CA')
    )
    INNER JOIN OPUS.DWC_LOCATION locationd ON (
        substr (an.dor_nod_cd, 1, 5) = locationd.loc_cd
        AND locationd.scc_cd IN (
            'USCLT',
            'USCLE',
            'USMEM',
            'USSLC',
            'USDEN',
            'USNYC',
            'USBOS',
            'USPHL',
            'USBUF',
            'USCMH',
            'USCVG',
            'USDET',
            'USPIT',
            'USBAL',
            'USCHI',
            'USIND',
            'USMES',
            'USLUI',
            'USGEO',
            'USSTL',
            'USKCK',
            'USOMA',
            'USSAV',
            'USJAX',
            'USCHS',
            'USGXX',
            'USORF',
            'USFRR',
            'USILM',
            'USATL',
            'USGBO',
            'USHSV',
            'USMIA',
            'USMSY',
            'USHOU',
            'USDLL',
            'USZD1',
            'USRIC',
            'USBHM',
            'USBNA',
            'USDAL',
            'USELP',
            'USLRD',
            'USPFX',
            'USSAT',
            'USSFO',
            'USPDX',
            'USSEA',
            'USTIW',
            'CACAL',
            'CAEDM',
            'CAHAL',
            'CAMTR',
            'CANWP',
            'CAREG',
            'CASAK',
            'CATOR',
            'CAVAN',
            'CAWNP',
            'USLAX',
            'USPHX',
            'USMOB',
            'USHRY',
            'USJOT'
        )
    )
    INNER JOIN OPUS.DWC_LOCATION location ON (
        substr (an.to_nod_cd, 1, 5) = location.loc_cd
        AND (
            (
                substr (an.to_nod_cd, 1, 5) IN ('USCLT', 'USCLE')
                AND locationd.scc_cd IN ('USCLT', 'USCLE')
            )
            OR locationd.scc_cd IN (
                'USMEM',
                'USDEN',
                'USSLC',
                'USNYC',
                'USBOS',
                'USPHL',
                'USBUF',
                'USCMH',
                'USCVG',
                'USDET',
                'USPIT',
                'USBAL',
                'USCHI',
                'USIND',
                'USMES',
                'USLUI',
                'USGEO',
                'USSTL',
                'USKCK',
                'USOMA',
                'USSAV',
                'USJAX',
                'USCHS',
                'USGXX',
                'USORF',
                'USFRR',
                'USILM',
                'USATL',
                'USGBO',
                'USHSV',
                'USMIA',
                'USMSY',
                'USHOU',
                'USDLL',
                'USZD1',
                'USRIC',
                'USBHM',
                'USBNA',
                'USDAL',
                'USELP',
                'USLRD',
                'USPFX',
                'USSAT',
                'USSFO',
                'USPDX',
                'USSEA',
                'USTIW',
                'CACAL',
                'CAEDM',
                'CAHAL',
                'CAMTR',
                'CANWP',
                'CAREG',
                'CASAK',
                'CATOR',
                'CAVAN',
                'CAWNP',
                'USLAX',
                'USPHX',
                'USMOB',
                'USHRY',
                'USJOT'
            )
        )
    )
    INNER JOIN OPUS.DWC_LOCATION locationcar ON (substr (an.fm_nod_cd, 1, 5) = locationcar.loc_cd)
    LEFT OUTER JOIN OPUS.DWC_YARD yardf ON (yardf.yd_cd = an.to_nod_cd)
    LEFT OUTER JOIN (
        SELECT
            a.TRSP_AGMT_OFC_CTY_CD || a.TRSP_AGMT_SEQ AGMT_NO,
            c.vndr_seq,
            f.vndr_lgl_eng_nm,
            d.trsp_cost_mod_cd,
            d.agmt_trsp_tp_cd,
            b.trsp_scg_cd,
            CASE
                WHEN b.trsp_scg_cd = 'SCFAAL' THEN 'Fuel Surcharge'
                ELSE substr (
                    e.act_cost_nm,
                    instr (e.act_cost_nm, ' - ') + 3,
                    length (e.act_cost_nm)
                )
            END SCGH_NM,
            CASE
                WHEN a.com_scg_aply_flg = '0' THEN 'N'
                ELSE 'Y'
            END Common,
            CASE
                WHEN a.wo_aply_flg = '0' THEN 'N'
                ELSE 'Y'
            END Auto_Apply,
            d.cgo_tp_cd,
            d.spcl_cgo_cntr_tp_cd,
            d.cust_nomi_trkr_flg,
            CASE
                WHEN d.cust_cnt_cd || d.cust_seq = 'XX0' THEN NULL
                ELSE d.cust_cnt_cd || d.cust_seq
            END CUST_CD,
            CASE
                WHEN d.cmdt_grp_cd = 'XXXX' THEN NULL
                ELSE d.cmdt_grp_cd
            END CMDT_GRP,
            a.eff_fm_dt,
            a.eff_to_dt,
            b.agmt_rout_all_flg,
            CASE
                WHEN b.fm_nod_cd = '0000000' THEN NULL
                ELSE b.fm_nod_cd
            END fm_nod_cd,
            CASE
                WHEN b.via_nod_cd = '0000000' THEN NULL
                ELSE b.via_nod_cd
            END via_nod_cd,
            CASE
                WHEN b.dor_nod_cd = '0000000' THEN NULL
                ELSE b.dor_nod_cd
            END dor_nod_cd,
            CASE
                WHEN b.to_nod_cd = '0000000' THEN NULL
                ELSE b.to_nod_cd
            END to_nod_cd,
            CASE
                WHEN a.agmt_scg_rt_div_cd = 'F' THEN 'Fixed'
                ELSE 'Ratio'
            END Fixed_Ratio,
            CASE
                WHEN a.curr_cd = 'XXX' THEN NULL
                ELSE a.curr_cd
            END Curr,
            a.trsp_one_wy_rt,
            a.trsp_rnd_rt,
            CASE
                WHEN a.trsp_agmt_eq_tp_sz_cd = 'XXXX' THEN NULL
                ELSE a.trsp_agmt_eq_tp_sz_cd
            END Size_Type,
            CASE
                WHEN a.to_wgt = 0 THEN NULL
                ELSE a.to_wgt
            END Weight_Tier,
            CASE
                WHEN a.wgt_meas_ut_cd = 'XXX' THEN NULL
                ELSE a.wgt_meas_ut_cd
            END UOM,
            a.agmt_cost_flg,
            a.usr_def_rmk,
            b.upd_dt
        FROM
            OPUS.DWL_AGMT_SCG_RT a
            INNER JOIN OPUS.DWL_AGMT_SCG_NOD b ON (
                a.TRSP_AGMT_OFC_CTY_CD || a.TRSP_AGMT_SEQ = b.TRSP_AGMT_OFC_CTY_CD || b.TRSP_AGMT_SEQ
                AND a.TRSP_AGMT_RT_TP_SER_NO = b.TRSP_AGMT_RT_TP_SER_NO
                AND a.TRSP_AGMT_SCG_NOD_SEQ = b.TRSP_AGMT_SCG_NOD_SEQ
            )
            INNER JOIN OPUS.DWL_AGMT_APLY_VNDR c ON (
                a.TRSP_AGMT_OFC_CTY_CD || a.TRSP_AGMT_SEQ = c.TRSP_AGMT_OFC_CTY_CD || c.TRSP_AGMT_SEQ
            )
            INNER JOIN OPUS.DWL_AGMT_RT_TP d ON (
                a.TRSP_AGMT_OFC_CTY_CD || a.TRSP_AGMT_SEQ = d.TRSP_AGMT_OFC_CTY_CD || d.TRSP_AGMT_SEQ
                AND a.TRSP_AGMT_RT_TP_SER_NO = d.TRSP_AGMT_RT_TP_SER_NO
                AND d.delt_flg <> 'Y'
            )
            LEFT OUTER JOIN OPUS.DWF_AP_COST_ACT_INFO e ON (
                b.trsp_scg_cd = e.act_cost_cd
                AND e.src_mdl_cd = 'TRS'
            )
            LEFT OUTER JOIN OPUS.DWC_VENDOR f ON (f.vndr_seq = c.vndr_seq)
        WHERE
            a.TRSP_AGMT_OFC_CTY_CD IN ('CHI', 'RIC')
    ) scgh ON (
        av.trsp_agmt_ofc_cty_cd || av.trsp_agmt_seq = scgh.AGMT_NO
        AND scgh.trsp_scg_cd = 'SCHZAL'
        AND scgh.eff_to_dt > CURRENT_DATETIME ()
        AND scgh.trsp_cost_mod_cd = art.trsp_cost_mod_cd
        AND scgh.agmt_trsp_tp_cd = art.agmt_trsp_tp_cd
        AND scgh.cgo_tp_cd = art.cgo_tp_cd
    )
WHERE
    av.vndr_seq NOT IN (127908)
