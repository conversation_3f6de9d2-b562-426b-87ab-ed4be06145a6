# GHQ BPIT NCOP Matchback API

A FastAPI application for GCP Cloud Run.

## Quick Start

This project implements a street turn matching service for logistics optimization. For detailed information about the project structure, deployment procedures, and development guidelines, please refer to the comprehensive documentation in the `docs/` folder.

## Documentation

For detailed guides and documentation, please refer to the following resources:

### Getting Started

-   **[Onboarding Checklist](docs/onboarding_checklist.md)** - Complete checklist for new team members
-   **[FAQ](docs/faq.md)** - Frequently asked questions and quick answers
-   **[Troubleshooting Guide](docs/troubleshooting_guide.md)** - Solutions for common setup and runtime issues

### Development

-   **[Developer Guide](docs/developer_guide.md)** - Complete development workflow, standards, and best practices
-   **[API Reference](docs/api_reference.md)** - Complete API endpoint documentation with examples
-   **[Project Structure](docs/project_structure.md)** - Comprehensive project folder organization and architecture

### Deployment & Operations

-   **[Deployment Guide](docs/deployment_guide.md)** - Local Docker and Google Cloud Run deployment instructions
-   **[CI/CD Pipeline Guide](docs/cicd_guide.md)** - Comprehensive guide for the GitHub Actions CI/CD pipeline
-   **[Docker Strategy Guide](docs/docker_strategy.md)** - Docker image building, tagging, and deployment strategies

### Architecture & Design

-   **[Architecture Diagram](docs/architecture_diagram.md)** - System architecture overview
-   **[Sequence Diagram](docs/sequence_diagram.md)** - Process flow and interaction diagrams
-   **[Cleanup Policies Guide](docs/setup_cleanup_policies_guide.md)** - GCP Artifact Registry cleanup configuration

## Prerequisites

-   Python 3.11 or higher (< 3.13)
-   [Poetry](https://python-poetry.org/)

## Installation Steps

### 1. Install Python

Make sure you have Python 3.11 or higher (but less than 3.13) installed. You can download it from the [official Python website](https://www.python.org/downloads/).

### 2. Create a Virtual Environment

Activate the virtual environment:

```sh
python -m venv .venv
```

On macOS and Linux:

```sh
source .venv/bin/activate
```

On Windows:

```sh
.venv\Scripts\activate
```

### 3. Set up Configuration Files

Before installing dependencies, you need to create the configuration files from the provided samples:

#### Create Environment Configuration

Copy the environment sample file and configure it with your specific values:

```bash
cp .env.sample .env
```

Edit the `.env` file and fill in the required values:

-   `ENVIRONMENT`: Set to `development`, `staging`, or `production`
-   `SERVICE_ACCOUNT_ENCODE`: Base64 encoded GCP service account JSON
-   `GMAP_API_KEY`: Google Maps API key for distance calculations
-   `DB_REPORT_DATA_FOLDER_ID`: Google Drive folder ID for report data
-   `API_KEY`: Secure API key for authentication
-   Server settings (HOST, PORT, FASTAPI_WORKER)

#### Create Application Configuration

Copy the application configuration sample file:

```bash
cp config/config.toml.sample config/config.toml
```

Edit the `config/config.toml` file and update:

-   `GCP.STORAGE.BUCKET_NAME`: Your GCS bucket name
-   `GCP.GSHEETS.SPREADSHEET_ID`: Your Google Sheets spreadsheet ID
-   `SYSTEM.NODES`: List of nodes to process
-   Database table and dataset IDs to match your GCP BigQuery setup

### 4. Install Poetry

If you don't have Poetry installed, you can install it using the following command:

```sh
pip install poetry
```

### 5. Install Project Dependencies

Use Poetry to install the project dependencies:

```sh
poetry install
```

### 6. Set Up Pre-commit Hooks

Install the pre-commit hooks defined in the .pre-commit-config.yaml file:

```sh
poetry run pre-commit install
```

You can also run the pre-commit hooks manually:

```sh
poetry run pre-commit run --all-files
```

### Additional Commands

#### Activate Virtual Environment

If you need to activate the virtual environment again, use the following command:

On macOS and Linux:

```sh
source .venv/bin/activate
```

On Windows:

```sh
.venv\Scripts\activate
```

#### Add New Dependencies with Poetry

To add a new dependency to your project, use the following command:

```sh
poetry add <package-name>
```

Replace `<package-name>` with the name of the package you want to add. For example, to add `requests`, you would run:

```sh
poetry add requests
```

#### Deactivate Virtual Environment

To deactivate the virtual environment, simply run:

```sh
deactivate
```

#### Run the Project

To run the project locally, use the following command:

```sh
poetry run uvicorn src.main:app --reload
```

This will start the FastAPI server with hot-reload enabled for development.

## Development

### Local Setup

1. Clone the repository:

    ```bash
    <NAME_EMAIL>:ocean-network-express/ghq-bpit-ncop-iro.git
    cd ghq-bpit-ncop-iro
    ```

2. Set up configuration files from samples:

    ```bash
    # Create environment configuration
    cp .env.sample .env

    # Create application configuration
    cp config/config.toml.sample config/config.toml
    ```

    Edit both files with your specific configuration values (see Installation Steps section for details).

3. Install dependencies using Poetry:

    ```bash
    poetry install
    ```

4. Run the development server:

    ```bash
    poetry run uvicorn app:app --reload
    ```

5. Access the API documentation:
    - <http://localhost:8000/docs> (Swagger UI)
    - <http://localhost:8000/redoc> (ReDoc)

### Testing

Run tests using pytest:

```bash
poetry run pytest
```

For test coverage report:

```bash
poetry run pytest --cov=src tests/
```

## Environment Variables

The application uses the following environment variables, which should be defined in the `.env` file for local development or set in Cloud Run environment:

```bash
# Application Environment
ENVIRONMENT=development  # development, staging, production

# Google Cloud Platform Configuration
SERVICE_ACCOUNT_ENCODE=  # Base64 encoded service account JSON key

# Google Maps API Configuration
GMAP_API_KEY=  # API key for Google Maps services

# Google Drive Configuration
DB_REPORT_DATA_FOLDER_ID=  # Folder ID for report data files

# Server Configuration
HOST=0.0.0.0  # Host address for FastAPI server
PORT=8001     # Port number for the server
FASTAPI_WORKER=1  # Number of worker processes

# API Security
API_KEY=  # API key for authenticating requests
```

## Configuration Files

The application also uses a TOML configuration file (`config/config.toml`) for application-specific settings:

-   **GCP Configuration**: Storage bucket and Google Sheets settings
-   **System Configuration**: Processing nodes, distance thresholds, matching criteria
-   **Database Configuration**: BigQuery dataset and table identifiers

Create this file from the sample: `cp config/config.toml.sample config/config.toml`

## Additional Resources

-   **[Poetry documentation](https://python-poetry.org/docs/)** - Dependency management and packaging
-   **[pre-commit documentation](https://pre-commit.com/)** - Git hook management
-   **[FastAPI documentation](https://fastapi.tiangolo.com/)** - Web framework documentation
-   **[Google Cloud Run documentation](https://cloud.google.com/run/docs)** - Deployment platform documentation

## Support and Contributing

For issues, questions, or contributions:

1. Check the documentation in the `docs/` folder first
2. Review existing issues and pull requests
3. Create a new issue with detailed information
4. Follow the project's coding standards and guidelines
